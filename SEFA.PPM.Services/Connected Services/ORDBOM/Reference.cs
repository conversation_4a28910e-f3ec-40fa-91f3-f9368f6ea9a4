//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ORDBOM
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", ConfigurationName="ORDBOM.ZWS_PP_MES_ORDBOM")]
    public interface ZWS_PP_MES_ORDBOM
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:sap-com:document:sap:rfc:functions:ZWS_PP_MES_ORDBOM:ZRFC_PP_MES_ORDBOMReques" +
            "t", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ORDBOM.ZRFC_PP_MES_ORDBOMResponse1> ZRFC_PP_MES_ORDBOMAsync(ORDBOM.ZRFC_PP_MES_ORDBOMRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDBOM
    {
        
        private string b_CKField;
        
        private string b_PKField;
        
        private ZPP_MES_ORDBOM_PO[] iT_ORDBOMField;
        
        private string p_WERKSField;
        
        private RANGE_AUFNR[] s_AUFNRField;
        
        private RANGE_C4[] s_CKARTField;
        
        private RANGE_C4[] s_EXCSTField;
        
        private RANGE_DATE[] s_GSTRPField;
        
        private RANGE_MATNR[] s_MATNRField;
        
        private RANGE_C4[] s_PKARTField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string B_CK
        {
            get
            {
                return this.b_CKField;
            }
            set
            {
                this.b_CKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string B_PK
        {
            get
            {
                return this.b_PKField;
            }
            set
            {
                this.b_PKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDBOM_PO[] IT_ORDBOM
        {
            get
            {
                return this.iT_ORDBOMField;
            }
            set
            {
                this.iT_ORDBOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string P_WERKS
        {
            get
            {
                return this.p_WERKSField;
            }
            set
            {
                this.p_WERKSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_AUFNR[] S_AUFNR
        {
            get
            {
                return this.s_AUFNRField;
            }
            set
            {
                this.s_AUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_CKART
        {
            get
            {
                return this.s_CKARTField;
            }
            set
            {
                this.s_CKARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_EXCST
        {
            get
            {
                return this.s_EXCSTField;
            }
            set
            {
                this.s_EXCSTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_DATE[] S_GSTRP
        {
            get
            {
                return this.s_GSTRPField;
            }
            set
            {
                this.s_GSTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_MATNR[] S_MATNR
        {
            get
            {
                return this.s_MATNRField;
            }
            set
            {
                this.s_MATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_PKART
        {
            get
            {
                return this.s_PKARTField;
            }
            set
            {
                this.s_PKARTField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_ORDBOM_PO
    {
        
        private string aUFNRField;
        
        private string aUARTField;
        
        private string wERKSField;
        
        private string mATNRField;
        
        private string mAKTXField;
        
        private string gSTRPField;
        
        private string gLTRPField;
        
        private decimal pSMNGField;
        
        private string aMEINField;
        
        private string nORMTField;
        
        private string mATNR_CField;
        
        private string mAKTX_CField;
        
        private string pOSNRField;
        
        private string pOSTPField;
        
        private decimal pSMNG_CField;
        
        private decimal nSMNG_CField;
        
        private string aMEIN_CField;
        
        private string sCHGT_CField;
        
        private string mATKL_CField;
        
        private string lGORT_CField;
        
        private string lGTXTField;
        
        private string vERANField;
        
        private string gLASSField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AUART
        {
            get
            {
                return this.aUARTField;
            }
            set
            {
                this.aUARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string WERKS
        {
            get
            {
                return this.wERKSField;
            }
            set
            {
                this.wERKSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string MAKTX
        {
            get
            {
                return this.mAKTXField;
            }
            set
            {
                this.mAKTXField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GSTRP
        {
            get
            {
                return this.gSTRPField;
            }
            set
            {
                this.gSTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GLTRP
        {
            get
            {
                return this.gLTRPField;
            }
            set
            {
                this.gLTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public decimal PSMNG
        {
            get
            {
                return this.pSMNGField;
            }
            set
            {
                this.pSMNGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string AMEIN
        {
            get
            {
                return this.aMEINField;
            }
            set
            {
                this.aMEINField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string NORMT
        {
            get
            {
                return this.nORMTField;
            }
            set
            {
                this.nORMTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string MATNR_C
        {
            get
            {
                return this.mATNR_CField;
            }
            set
            {
                this.mATNR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string MAKTX_C
        {
            get
            {
                return this.mAKTX_CField;
            }
            set
            {
                this.mAKTX_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string POSNR
        {
            get
            {
                return this.pOSNRField;
            }
            set
            {
                this.pOSNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string POSTP
        {
            get
            {
                return this.pOSTPField;
            }
            set
            {
                this.pOSTPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public decimal PSMNG_C
        {
            get
            {
                return this.pSMNG_CField;
            }
            set
            {
                this.pSMNG_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public decimal NSMNG_C
        {
            get
            {
                return this.nSMNG_CField;
            }
            set
            {
                this.nSMNG_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string AMEIN_C
        {
            get
            {
                return this.aMEIN_CField;
            }
            set
            {
                this.aMEIN_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string SCHGT_C
        {
            get
            {
                return this.sCHGT_CField;
            }
            set
            {
                this.sCHGT_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string MATKL_C
        {
            get
            {
                return this.mATKL_CField;
            }
            set
            {
                this.mATKL_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string LGORT_C
        {
            get
            {
                return this.lGORT_CField;
            }
            set
            {
                this.lGORT_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string LGTXT
        {
            get
            {
                return this.lGTXTField;
            }
            set
            {
                this.lGTXTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string VERAN
        {
            get
            {
                return this.vERANField;
            }
            set
            {
                this.vERANField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string GLASS
        {
            get
            {
                return this.gLASSField;
            }
            set
            {
                this.gLASSField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class BAPIRET2
    {
        
        private string tYPEField;
        
        private string idField;
        
        private string nUMBERField;
        
        private string mESSAGEField;
        
        private string lOG_NOField;
        
        private string lOG_MSG_NOField;
        
        private string mESSAGE_V1Field;
        
        private string mESSAGE_V2Field;
        
        private string mESSAGE_V3Field;
        
        private string mESSAGE_V4Field;
        
        private string pARAMETERField;
        
        private int rOWField;
        
        private string fIELDField;
        
        private string sYSTEMField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TYPE
        {
            get
            {
                return this.tYPEField;
            }
            set
            {
                this.tYPEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ID
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string NUMBER
        {
            get
            {
                return this.nUMBERField;
            }
            set
            {
                this.nUMBERField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MESSAGE
        {
            get
            {
                return this.mESSAGEField;
            }
            set
            {
                this.mESSAGEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LOG_NO
        {
            get
            {
                return this.lOG_NOField;
            }
            set
            {
                this.lOG_NOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LOG_MSG_NO
        {
            get
            {
                return this.lOG_MSG_NOField;
            }
            set
            {
                this.lOG_MSG_NOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MESSAGE_V1
        {
            get
            {
                return this.mESSAGE_V1Field;
            }
            set
            {
                this.mESSAGE_V1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MESSAGE_V2
        {
            get
            {
                return this.mESSAGE_V2Field;
            }
            set
            {
                this.mESSAGE_V2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string MESSAGE_V3
        {
            get
            {
                return this.mESSAGE_V3Field;
            }
            set
            {
                this.mESSAGE_V3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string MESSAGE_V4
        {
            get
            {
                return this.mESSAGE_V4Field;
            }
            set
            {
                this.mESSAGE_V4Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string PARAMETER
        {
            get
            {
                return this.pARAMETERField;
            }
            set
            {
                this.pARAMETERField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public int ROW
        {
            get
            {
                return this.rOWField;
            }
            set
            {
                this.rOWField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string FIELD
        {
            get
            {
                return this.fIELDField;
            }
            set
            {
                this.fIELDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string SYSTEM
        {
            get
            {
                return this.sYSTEMField;
            }
            set
            {
                this.sYSTEMField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class RANGE_MATNR
    {
        
        private string sIGNField;
        
        private string oPTIONField;
        
        private string lOWField;
        
        private string hIGHField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string SIGN
        {
            get
            {
                return this.sIGNField;
            }
            set
            {
                this.sIGNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string OPTION
        {
            get
            {
                return this.oPTIONField;
            }
            set
            {
                this.oPTIONField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LOW
        {
            get
            {
                return this.lOWField;
            }
            set
            {
                this.lOWField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string HIGH
        {
            get
            {
                return this.hIGHField;
            }
            set
            {
                this.hIGHField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class RANGE_DATE
    {
        
        private string sIGNField;
        
        private string oPTIONField;
        
        private string lOWField;
        
        private string hIGHField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string SIGN
        {
            get
            {
                return this.sIGNField;
            }
            set
            {
                this.sIGNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string OPTION
        {
            get
            {
                return this.oPTIONField;
            }
            set
            {
                this.oPTIONField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LOW
        {
            get
            {
                return this.lOWField;
            }
            set
            {
                this.lOWField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string HIGH
        {
            get
            {
                return this.hIGHField;
            }
            set
            {
                this.hIGHField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class RANGE_C4
    {
        
        private string sIGNField;
        
        private string oPTIONField;
        
        private string lOWField;
        
        private string hIGHField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string SIGN
        {
            get
            {
                return this.sIGNField;
            }
            set
            {
                this.sIGNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string OPTION
        {
            get
            {
                return this.oPTIONField;
            }
            set
            {
                this.oPTIONField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LOW
        {
            get
            {
                return this.lOWField;
            }
            set
            {
                this.lOWField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string HIGH
        {
            get
            {
                return this.hIGHField;
            }
            set
            {
                this.hIGHField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class RANGE_AUFNR
    {
        
        private string sIGNField;
        
        private string oPTIONField;
        
        private string lOWField;
        
        private string hIGHField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string SIGN
        {
            get
            {
                return this.sIGNField;
            }
            set
            {
                this.sIGNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string OPTION
        {
            get
            {
                return this.oPTIONField;
            }
            set
            {
                this.oPTIONField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LOW
        {
            get
            {
                return this.lOWField;
            }
            set
            {
                this.lOWField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string HIGH
        {
            get
            {
                return this.hIGHField;
            }
            set
            {
                this.hIGHField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDBOMResponse
    {
        
        private ZPP_MES_ORDBOM_PO[] iT_ORDBOMField;
        
        private BAPIRET2 rETField;
        
        private RANGE_AUFNR[] s_AUFNRField;
        
        private RANGE_C4[] s_CKARTField;
        
        private RANGE_C4[] s_EXCSTField;
        
        private RANGE_DATE[] s_GSTRPField;
        
        private RANGE_MATNR[] s_MATNRField;
        
        private RANGE_C4[] s_PKARTField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDBOM_PO[] IT_ORDBOM
        {
            get
            {
                return this.iT_ORDBOMField;
            }
            set
            {
                this.iT_ORDBOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BAPIRET2 RET
        {
            get
            {
                return this.rETField;
            }
            set
            {
                this.rETField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_AUFNR[] S_AUFNR
        {
            get
            {
                return this.s_AUFNRField;
            }
            set
            {
                this.s_AUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_CKART
        {
            get
            {
                return this.s_CKARTField;
            }
            set
            {
                this.s_CKARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_EXCST
        {
            get
            {
                return this.s_EXCSTField;
            }
            set
            {
                this.s_EXCSTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_DATE[] S_GSTRP
        {
            get
            {
                return this.s_GSTRPField;
            }
            set
            {
                this.s_GSTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_MATNR[] S_MATNR
        {
            get
            {
                return this.s_MATNRField;
            }
            set
            {
                this.s_MATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public RANGE_C4[] S_PKART
        {
            get
            {
                return this.s_PKARTField;
            }
            set
            {
                this.s_PKARTField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDBOMRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDBOM.ZRFC_PP_MES_ORDBOM ZRFC_PP_MES_ORDBOM;
        
        public ZRFC_PP_MES_ORDBOMRequest()
        {
        }
        
        public ZRFC_PP_MES_ORDBOMRequest(ORDBOM.ZRFC_PP_MES_ORDBOM ZRFC_PP_MES_ORDBOM)
        {
            this.ZRFC_PP_MES_ORDBOM = ZRFC_PP_MES_ORDBOM;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDBOMResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDBOM.ZRFC_PP_MES_ORDBOMResponse ZRFC_PP_MES_ORDBOMResponse;
        
        public ZRFC_PP_MES_ORDBOMResponse1()
        {
        }
        
        public ZRFC_PP_MES_ORDBOMResponse1(ORDBOM.ZRFC_PP_MES_ORDBOMResponse ZRFC_PP_MES_ORDBOMResponse)
        {
            this.ZRFC_PP_MES_ORDBOMResponse = ZRFC_PP_MES_ORDBOMResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ZWS_PP_MES_ORDBOMChannel : ORDBOM.ZWS_PP_MES_ORDBOM, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ZWS_PP_MES_ORDBOMClient : System.ServiceModel.ClientBase<ORDBOM.ZWS_PP_MES_ORDBOM>, ORDBOM.ZWS_PP_MES_ORDBOM
    {
        
        public ZWS_PP_MES_ORDBOMClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ORDBOM.ZRFC_PP_MES_ORDBOMResponse1> ORDBOM.ZWS_PP_MES_ORDBOM.ZRFC_PP_MES_ORDBOMAsync(ORDBOM.ZRFC_PP_MES_ORDBOMRequest request)
        {
            return base.Channel.ZRFC_PP_MES_ORDBOMAsync(request);
        }
        
        public System.Threading.Tasks.Task<ORDBOM.ZRFC_PP_MES_ORDBOMResponse1> ZRFC_PP_MES_ORDBOMAsync(ORDBOM.ZRFC_PP_MES_ORDBOM ZRFC_PP_MES_ORDBOM)
        {
            ORDBOM.ZRFC_PP_MES_ORDBOMRequest inValue = new ORDBOM.ZRFC_PP_MES_ORDBOMRequest();
            inValue.ZRFC_PP_MES_ORDBOM = ZRFC_PP_MES_ORDBOM;
            return ((ORDBOM.ZWS_PP_MES_ORDBOM)(this)).ZRFC_PP_MES_ORDBOMAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
    }
}
