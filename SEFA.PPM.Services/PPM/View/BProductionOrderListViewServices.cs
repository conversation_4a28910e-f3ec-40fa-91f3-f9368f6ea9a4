
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.PTM.Model.ViewModels;
using System;
using SEFA.MKM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Linq;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using SEFA.Base.Common.Helper;
using LKK.Lib.Core;
using SEFA.PPM.Model.Models.PTM;
using Castle.MicroKernel.Registration;
using SEFA.PPM.IServices.PTM;
using System.Data;
using Parameter = SEFA.PPM.Model.ViewModels.Parameter;
using Abp.Domain.Entities;
using System.Security.Cryptography;
using SharpCompress.Common;
using SEFA.Base.Model.Models;
using SEFA.PPM.Model.ViewModels.MKM.View;
using BasePropertyEntity = SEFA.PPM.Model.Models.PTM.BasePropertyEntity;
using SEFA.MKM.Model.Models.MKM;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using SEFA.Base.Repository.UnitOfWork;
using SEFA.Base.Common.LogHelper;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using OfficeOpenXml.Style;
using System.Drawing;
using SEFA.Base.Common.Common;
using Microsoft.AspNetCore.Http;
using UnitmanageEntity = SEFA.DFM.Model.Models.UnitmanageEntity;
using SEFA.PPM.Model.ViewModels.PTM;
using AutoMapper;
using SEFA.MKM.IServices;
using System.Reactive;
using Microsoft.Data.SqlClient;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Model.Models.Interface;
using OfficeOpenXml.ConditionalFormatting;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Reflection;
using SEFA.PTM.IServices;
using SEFA.PTM.Services;

namespace SEFA.PPM.Services
{
	public class BProductionOrderListViewServices : BaseServices<BProductionOrderListViewEntity>, IBProductionOrderListViewServices
	{
		private readonly IBaseRepository<BProductionOrderListViewEntity> _dal;
		private readonly IBaseRepository<ProductionOrderEntity> _dal2;
		private readonly IBaseRepository<Model.Models.BatchEntity> _dal3;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _dal4;
		private readonly IBaseRepository<PoConsumeRequirementEntity> _dal5;
		private readonly IBaseRepository<PoProducedRequirementEntity> _dal6;
		private readonly IBaseRepository<BatchConsumeRequirementEntity> _dal7;
		private readonly IBaseRepository<BatchProducedRequirementEntity> _dal8;
		private readonly IBaseRepository<BasePropertyEntity> _dal9;
		private readonly IBaseRepository<MMaterialPropertyViewEntity> _dal10;
		private readonly IBaseRepository<MaterialGroupMappingEntity> _dal11;
		private readonly IBaseRepository<MaterialGroupEntity> _dal12;
		private readonly IBaseRepository<OrderBomEntity> _dal13;
		private readonly IBaseRepository<SapPoRoutingEntity> _SapPoRoutingdal;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _posegmentrdal;
		private readonly IBaseRepository<EquipmentEntity> _equdal;
		private readonly IBaseRepository<SapSegmentEquipmentEntity> _segmentequdal;
		private readonly IBaseRepository<SappackorderEntity> _sapOrderdal;
		private readonly IBaseRepository<MaterialProcessDataEntity> _processDatadal;
		private readonly IBaseRepository<SapBomPhaseInjectionEntity> _dal01;
		private readonly IBaseRepository<SapSegmentEntity> _dal02;
		private readonly IBaseRepository<SapSegmentMaterialEntity> _dal03;
		private readonly IBaseRepository<SapSegmentMaterialStepEntity> _dal04;
		private readonly IBaseRepository<MaterialEntity> _dal05;
		private readonly IBaseRepository<MaterialVersionEntity> _dal06;
		private readonly IBaseRepository<PoProducedExecutionEntity> _exdal;
		private readonly IBaseRepository<PoEquipmentEntity> _dalpoeq;
		private readonly IBaseRepository<ThroatadditionEntity> _throatadal;
		private readonly IBaseRepository<WorkorderthroatEntity> _workthroatadal;
		private readonly IPerformanceServices _performanceServices;
		private readonly IProcessDataViewServices _processDataViewServices;
		private readonly IInterfaceServices _iInterfaceServices;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IBaseRepository<DFM.Model.Models.ShiftEntity> _dalShiftEntity;
		private readonly IBaseRepository<MaterialPropertyViewEntity> _dalMaterialPropertyViewEntity;
		private readonly IBaseRepository<OrderThroatadditionEntity> _orderThroDal;
		private readonly IBaseRepository<UnitmanageEntity> _unitDal;
		private readonly IMapper _mapper;
		private readonly IMaterialInventoryServices _materInverntoryServices;
		private readonly IBaseRepository<PoConsumeActualEntity> _poconsumeDal;
		private readonly IBaseRepository<PoProducedActualEntity> _poproduceDal;
		private readonly IProduceViewServices _produceViewServices;
		private readonly IConsumeViewServices _consumeViewServices;
		private readonly IBaseRepository<MaterialLotEntity> _lotDal;



		public BProductionOrderListViewServices(IBaseRepository<BProductionOrderListViewEntity> dal,
			IBaseRepository<ProductionOrderEntity> dal2,
			IBaseRepository<Model.Models.BatchEntity> dal3,
			IBaseRepository<PoSegmentRequirementEntity> dal4,
			IBaseRepository<PoConsumeRequirementEntity> dal5,
			IBaseRepository<PoProducedRequirementEntity> dal6,
			IBaseRepository<BatchConsumeRequirementEntity> dal7,
			IBaseRepository<BatchProducedRequirementEntity> dal8,
			IBaseRepository<BasePropertyEntity> dal9,
			IBaseRepository<MMaterialPropertyViewEntity> dal10,
			IBaseRepository<MaterialGroupMappingEntity> dal11,
			IBaseRepository<MaterialGroupEntity> dal12,
			IProcessDataViewServices processDataViewServices,
			IPerformanceServices performanceServices,
			IInterfaceServices iInterfaceServices,
			IUnitOfWork unitOfWork, IUser user, IBaseRepository<ShiftEntity> dalShiftEntity, IBaseRepository<OrderBomEntity> dal13, IBaseRepository<SapPoRoutingEntity> sapPoRoutingdal, IBaseRepository<PoSegmentRequirementEntity> posegmentrdal, IBaseRepository<EquipmentEntity> equdal, IBaseRepository<SapSegmentEquipmentEntity> segmentequdal, IBaseRepository<MaterialPropertyViewEntity> dalMaterialPropertyViewEntity, IBaseRepository<SappackorderEntity> sapOrderdal, IBaseRepository<MaterialProcessDataEntity> processDatadal, IBaseRepository<SapBomPhaseInjectionEntity> dal01, IBaseRepository<SapSegmentEntity> dal02, IBaseRepository<SapSegmentMaterialEntity> dal03, IBaseRepository<SapSegmentMaterialStepEntity> dal04, IBaseRepository<MaterialEntity> dal05, IBaseRepository<MaterialVersionEntity> dal06, IBaseRepository<PoEquipmentEntity> dalpoeq, IBaseRepository<ThroatadditionEntity> throatadal, IBaseRepository<PoProducedExecutionEntity> exdal, IBaseRepository<OrderThroatadditionEntity> orderThroDal, IBaseRepository<UnitmanageEntity> unitDal, IMapper mapper, IMaterialInventoryServices materInverntoryServices, IBaseRepository<WorkorderthroatEntity> workthroatadal, IBaseRepository<PoConsumeActualEntity> poconsumeDal, IBaseRepository<PoProducedActualEntity> poproduceDal, IProduceViewServices produceViewServices, IConsumeViewServices consumeViewServices, IBaseRepository<MaterialLotEntity> lotDal)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal3 = dal3;
			this._dal4 = dal4;
			this._dal5 = dal5;
			this._dal6 = dal6;
			this._dal7 = dal7;
			this._dal8 = dal8;
			this._dal9 = dal9;
			this._dal10 = dal10;
			this._dal11 = dal11;
			this._dal12 = dal12;
			base.BaseDal = dal;
			this._processDataViewServices = processDataViewServices;
			this._performanceServices = performanceServices;
			this._iInterfaceServices = iInterfaceServices;
			this._unitOfWork = unitOfWork;
			this._user = user;
			_dalShiftEntity = dalShiftEntity;
			_dal13 = dal13;
			_SapPoRoutingdal = sapPoRoutingdal;
			_posegmentrdal = posegmentrdal;
			_equdal = equdal;
			_segmentequdal = segmentequdal;
			_dalMaterialPropertyViewEntity = dalMaterialPropertyViewEntity;
			_sapOrderdal = sapOrderdal;
			_processDatadal = processDatadal;
			_dal01 = dal01;
			_dal02 = dal02;
			_dal03 = dal03;
			_dal04 = dal04;
			_dal05 = dal05;
			_dal06 = dal06;
			_dalpoeq = dalpoeq;
			_throatadal = throatadal;
			_exdal = exdal;
			_orderThroDal = orderThroDal;
			_unitDal = unitDal;
			_mapper = mapper;
			_materInverntoryServices = materInverntoryServices;
			_workthroatadal = workthroatadal;
			_poconsumeDal = poconsumeDal;
			_poproduceDal = poproduceDal;
			_produceViewServices = produceViewServices;
			_consumeViewServices = consumeViewServices;
			_lotDal = lotDal;
		}

		public async Task<List<BProductionOrderListViewEntity>> GetList(BProductionOrderListViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.SegmentCode), a => a.SegmentCode.Contains(reqModel.SegmentCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
				.AndIF(!string.IsNullOrEmpty(reqModel.NeedQARelease), a => a.NeedQARelease == reqModel.NeedQARelease)
				.AndIF(!string.IsNullOrEmpty(reqModel.SapOrderType), a => a.SapOrderType == reqModel.SapOrderType)
				.AndIF(!string.IsNullOrEmpty(reqModel.QaStatus), a => a.QaStatus == reqModel.QaStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode == reqModel.MaterialCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => (a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription)) || (a.MaterialCode != null && a.MaterialCode.Contains(reqModel.MaterialCode)))
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
				.AndIF(!string.IsNullOrEmpty(reqModel.PoStatus), a => a.PoStatus == reqModel.PoStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.ReleaseStatus), a => a.ReleaseStatus == reqModel.ReleaseStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.FinalStatus), a => a.FinalStatus == reqModel.FinalStatus)
				.AndIF(reqModel.StatusList != null && reqModel.StatusList.Count > 0, a => reqModel.StatusList.Contains(a.PoStatus))
				.AndIF(reqModel.StatusList == null || reqModel.StatusList.Count == 0, a => a.PoStatus == "2" || a.PoStatus == "5" || a.PoStatus == "6")
				.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				  .AndIF(!string.IsNullOrEmpty(reqModel.Key),
					a => a.MaterialCode.Contains(reqModel.Key) ||
						 a.MaterialDescription.Contains(reqModel.Key) ||
						 a.MaterialVersionNumber.Contains(reqModel.Key) ||
						 a.ProductionOrderNo.Contains(reqModel.Key) ||
						 a.BomVersion.Contains(reqModel.Key) ||
						 a.Formula.Contains(reqModel.Key) ||
						 a.LineCode.Contains(reqModel.Key) ||
						 a.SegmentCode.Contains(reqModel.Key)

					)
							 .ToExpression();
			//var data = await _dal.FindList(whereExpression, x => x.PlanStartTime, false);
			var data = await _dal.Db.Queryable<BProductionOrderListViewEntity>()
					.Where(whereExpression)
					.OrderBy(x => x.LineCode)
					.OrderBy(x => x.PlanStartTime)
					.OrderBy(x => x.FormulaSequence)
					.OrderBy(x => x.Sequence).ToListAsync();
			foreach (var item in data)
			{
				var cips = await _dal.Db.Queryable<ProductionOrderCipEntity>().Where(p => p.OrderId == item.ID).ToListAsync();
				if (cips.Count() > 0)
				{
					var cipNames = cips.Select(p => p.Switchname).ToList();
					item.CipDetail = string.Join(",", cipNames);
				}
			}
			return data;
		}

		public async Task<PageModel<BProductionOrderListViewEntity>> GetPageList(BProductionOrderListViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.SegmentCode), a => a.SegmentCode.Contains(reqModel.SegmentCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
				.AndIF(!string.IsNullOrEmpty(reqModel.NeedQARelease), a => a.NeedQARelease == reqModel.NeedQARelease)
				.AndIF(!string.IsNullOrEmpty(reqModel.SapOrderType), a => a.SapOrderType == reqModel.SapOrderType)
				.AndIF(!string.IsNullOrEmpty(reqModel.QaStatus), a => a.QaStatus == reqModel.QaStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode == reqModel.MaterialCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => (a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription)) || (a.MaterialCode != null && a.MaterialCode.Contains(reqModel.MaterialCode)))
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
				.AndIF(!string.IsNullOrEmpty(reqModel.PoStatus), a => a.PoStatus == reqModel.PoStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.ReleaseStatus), a => a.ReleaseStatus == reqModel.ReleaseStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.FinalStatus), a => a.FinalStatus == reqModel.FinalStatus)
				.AndIF(reqModel.StatusList != null && reqModel.StatusList.Count > 0, a => reqModel.StatusList.Contains(a.PoStatus))
				.AndIF(reqModel.StatusList == null || reqModel.StatusList.Count == 0, a => a.PoStatus == "2" || a.PoStatus == "5" || a.PoStatus == "6")
				//.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				//.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				.AndIF(!string.IsNullOrEmpty(reqModel.Key),
					a => a.MaterialCode.Contains(reqModel.Key) ||
						 a.MaterialDescription.Contains(reqModel.Key) ||
						 a.MaterialVersionNumber.Contains(reqModel.Key) ||
						 a.ProductionOrderNo.Contains(reqModel.Key) ||
						 a.BomVersion.Contains(reqModel.Key) ||
						 a.Formula.Contains(reqModel.Key) ||
						 a.LineCode.Contains(reqModel.Key) ||
						 a.SegmentCode.Contains(reqModel.Key)

					)
							 .ToExpression();

			RefAsync<int> dataCount = 0;
			var data = await _dal.Db.Queryable<BProductionOrderListViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.LineCode)
				.OrderBy(x => x.ProductionDate)
				.OrderBy(x => x.FormulaSequence)
				.OrderBy(x => x.Sequence)
				.ToListAsync();

			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate <= reqModel.EndTime.Value);
			}
			PageModel<BProductionOrderListViewEntity> result = new PageModel<BProductionOrderListViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			foreach (var item in rDat)
			{
				var cips = await _dal.Db.Queryable<ProductionOrderCipEntity>().Where(p => p.OrderId == item.ID).ToListAsync();
				if (cips.Count() > 0)
				{
					var cipNames = cips.Select(p => p.Switchname).ToList();
					item.CipDetail = string.Join(",", cipNames);
				}
			}
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<PageModel<BProductionOrderListViewEntity>> GetPackPageList(BProductionOrderListViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.SegmentCode), a => a.SegmentCode.Contains(reqModel.SegmentCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
				.AndIF(!string.IsNullOrEmpty(reqModel.SapOrderType), a => a.SapOrderType == reqModel.SapOrderType)
				.AndIF(!string.IsNullOrEmpty(reqModel.QaStatus), a => a.QaStatus == reqModel.QaStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode == reqModel.MaterialCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => (a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription)) || (a.MaterialCode != null && a.MaterialCode.Contains(reqModel.MaterialCode)))
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
				.AndIF(!string.IsNullOrEmpty(reqModel.ReleaseStatus), a => a.ReleaseStatus == reqModel.ReleaseStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.FinalStatus), a => a.FinalStatus == reqModel.FinalStatus)
				.And(a => a.SapOrderType != "ZXH2")
				.And(a => a.PoStatus == "3")
				.AndIF(!string.IsNullOrEmpty(reqModel.Key),
					a => a.MaterialCode.Contains(reqModel.Key) ||
						 a.MaterialDescription.Contains(reqModel.Key) ||
						 a.MaterialVersionNumber.Contains(reqModel.Key) ||
						 a.ProductionOrderNo.Contains(reqModel.Key) ||
						 a.BomVersion.Contains(reqModel.Key) ||
						 a.Formula.Contains(reqModel.Key) ||
						 a.LineCode.Contains(reqModel.Key) ||
						 a.SegmentCode.Contains(reqModel.Key)

					)
							 .ToExpression();

			RefAsync<int> dataCount = 0;
			var data = await _dal.Db.Queryable<BProductionOrderListViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.LineCode)
				.OrderBy(x => x.ProductionDate)
				.OrderBy(x => x.FormulaSequence)
				.OrderBy(x => x.Sequence)
				.ToListAsync();

			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate <= reqModel.EndTime.Value);
			}
			PageModel<BProductionOrderListViewEntity> result = new PageModel<BProductionOrderListViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			foreach (var item in rDat)
			{
				var cips = await _dal.Db.Queryable<ProductionOrderCipEntity>().Where(p => p.OrderId == item.ID).ToListAsync();
				if (cips.Count() > 0)
				{
					var cipNames = cips.Select(p => p.Switchname).ToList();
					item.CipDetail = string.Join(",", cipNames);
				}
			}
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}


		public async Task<List<BProductionOrderListViewEntity>> GetQAList(BProductionOrderListViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.SegmentCode), a => a.SegmentCode.Contains(reqModel.SegmentCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
				.AndIF(reqModel.QaStatus == "-1", a => a.QaStatus == "待QA" || a.QaStatus == "通过")
				.AndIF(!string.IsNullOrEmpty(reqModel.QaStatus) && reqModel.QaStatus != "-1", a => a.QaStatus == reqModel.QaStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode == reqModel.MaterialCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription))
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
				.AndIF(!string.IsNullOrEmpty(reqModel.PoStatus), a => a.PoStatus == reqModel.PoStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.ReleaseStatus), a => a.ReleaseStatus == reqModel.ReleaseStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.FinalStatus), a => a.FinalStatus == reqModel.FinalStatus)
				.AndIF(reqModel.StatusList != null && reqModel.StatusList.Count > 0, a => reqModel.StatusList.Contains(a.PoStatus))
				//.AndIF(reqModel.StatusList == null || reqModel.StatusList.Count == 0, a => a.PoStatus == "2" || a.PoStatus == "5" || a.PoStatus == "6")
				.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				  .AndIF(!string.IsNullOrEmpty(reqModel.Key),
					a => a.MaterialCode.Contains(reqModel.Key) ||
						 a.MaterialDescription.Contains(reqModel.Key) ||
						 a.MaterialVersionNumber.Contains(reqModel.Key) ||
						 a.ProductionOrderNo.Contains(reqModel.Key) ||
						 a.BomVersion.Contains(reqModel.Key) ||
						 a.Formula.Contains(reqModel.Key) ||
						 a.LineCode.Contains(reqModel.Key) ||
						 a.SegmentCode.Contains(reqModel.Key)
					)
							 .ToExpression();
			//var data = await _dal.FindList(whereExpression, x => x.PlanStartTime, false);
			var data = await _dal.Db.Queryable<BProductionOrderListViewEntity>()
								.Where(whereExpression)
								.OrderBy(x => x.QaStatus)
								//.OrderBy(x => x.LineCode)
								//.OrderBy(x => x.PlanStartTime)
								.OrderBy(x => x.FormulaSequence)
								.OrderBy(x => x.Sequence).ToListAsync();
			return data;
		}

		public async Task<PageModel<BProductionOrderListViewEntity>> GetQAPageList(BProductionOrderListViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
				.AndIF(reqModel.QaStatus == "-1", a => a.QaStatus == "待QA" || a.QaStatus == "通过")
				.AndIF(!string.IsNullOrEmpty(reqModel.QaStatus) && reqModel.QaStatus != "-1", a => a.QaStatus == reqModel.QaStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode == reqModel.MaterialCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription))
				.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
				.AndIF(!string.IsNullOrEmpty(reqModel.PoStatus), a => a.PoStatus == reqModel.PoStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.ReleaseStatus), a => a.ReleaseStatus == reqModel.ReleaseStatus)
				.AndIF(!string.IsNullOrEmpty(reqModel.FinalStatus), a => a.FinalStatus == reqModel.FinalStatus)
				.AndIF(reqModel.StatusList != null && reqModel.StatusList.Count > 0, a => reqModel.StatusList.Contains(a.PoStatus))
				//.AndIF(reqModel.StatusList == null || reqModel.StatusList.Count == 0, a => a.PoStatus == "2" || a.PoStatus == "5" || a.PoStatus == "6")
				//.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				//.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				.AndIF(!string.IsNullOrEmpty(reqModel.Key),
					a => a.MaterialCode.Contains(reqModel.Key) ||
						 a.MaterialDescription.Contains(reqModel.Key) ||
						 a.MaterialVersionNumber.Contains(reqModel.Key) ||
						 a.ProductionOrderNo.Contains(reqModel.Key) ||
						 a.BomVersion.Contains(reqModel.Key)
					)
							 .ToExpression();

			RefAsync<int> dataCount = 0;
			var data = await _dal.Db.Queryable<BProductionOrderListViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.QaStatus)
				//.OrderBy(x => x.LineCode)
				//.OrderBy(x => x.PlanStartTime)
				.OrderBy(x => x.FormulaSequence)
				.OrderBy(x => x.Sequence)
				.ToListAsync();
			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate <= reqModel.EndTime.Value);
			}
			PageModel<BProductionOrderListViewEntity> result = new PageModel<BProductionOrderListViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			//foreach (var item in rDat)
			//{
			//	var cips = await _dal.Db.Queryable<ProductionOrderCipEntity>().Where(p => p.OrderId == item.ID).ToListAsync();
			//	if (cips.Count() > 0)
			//	{
			//		var cipNames = cips.Select(p => p.Switchname).ToList();
			//		item.CipDetail = string.Join(",", cipNames);
			//	}
			//}
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<bool> SaveForm(BProductionOrderListViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}

		/// <summary>
		/// 工单操作：通过key值判断执行不同逻辑 key=1修改工单,key=2释放工单,key=3完成工单,key=4撤销释放,key=5放行
		/// Body需要根据key传不同格式的数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> OperationPo(ConsolPoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				success = false,
				msg = "操作失败！"
			};
			string PoId = string.Empty;
			JObject obj;
			try
			{
				obj = JObject.Parse(reqModel.Body);
				PoId = obj["ID"].ToString();
			}
			catch (Exception ex)
			{
				result.msg = "请检查参数[ID]";
				return result;
			}
			ProductionOrderEntity entity = await _dal2.FindEntity(PoId);
			if (entity == null)
			{
				result.msg = "PoId不存在";
				return result;
			}
			List<ProductionOrderEntity> pendingOrders = new List<ProductionOrderEntity>();
			//Model.Models.BatchEntity batchEntity = null;
			bool flag = false;
			int versionNumber = 0;
			switch (reqModel.Key)
			{
				//修改工单
				case "1":
					entity.Speed = int.Parse(obj["Speed"].ToString());
					entity.PlanQty = decimal.Parse(obj["PlanQty"].ToString());
					entity.PoStatus = obj["PoStatus"].ToString();
					break;
				//释放工单
				case "2":
					//生成工单批次
					await CreateBatch(new List<string>() { PoId });
					pendingOrders = await _dal2.FindList(x => x.ID != entity.ID && x.MaterialVersionId == entity.MaterialVersionId && x.PoStatus == "1"/*PENDING_RELEASE*/);
					if (pendingOrders?.Count > 0)
					{
						foreach (var item in pendingOrders)
						{
							await CreateBatch(new List<string>() { item.ID });
							item.PoStatus = "2";
							item.Modify(item.ID, _user.Name);
						}
					}
					break;
				//完成工单
				case "3":

					break;
				//撤销释放
				case "4":
					entity.PoStatus = "1";
					break;
				//放行
				case "5":
					entity.PoStatus = "2";
					break;
				default:
					break;
			}
			_unitOfWork.BeginTran();
			try
			{
				entity.Modify(entity.ID, _user.Name);
				await _dal2.Update(entity);
				if (pendingOrders?.Count > 0)
				{
					await _dal2.Update(pendingOrders);
				}
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			result.msg = "操作成功！";
			return result;
		}

		#region 班次

		/// <summary>
		/// 新增班次状态
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateShift(UpdateStatusRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};

			try
			{
				string[] ids = reqModel.ProIDS;
				_unitOfWork.BeginTran();

				List<ProductionOrderEntity> upList = new List<ProductionOrderEntity>();
				for (int i = 0; i < ids.Length; i++)
				{
					ProductionOrderEntity entity = await _dal2.FindEntity(ids[i]);
					if (entity == null)
					{
						result.msg = "PoId不存在";
						return result;
					}
					entity.PrepareShiftid = reqModel.ShiftID;
					entity.Modify(entity.ID, _user.Name.ToString());
					upList.Add(entity);
				}

				bool results = await _dal2.Update(upList);

				if (results == false)
				{
					return result;
				}

				result.success = true;
				_unitOfWork.CommitTran();

			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
				return result;
			}
			result.msg = "操作成功！";
			return result;
		}

		public async Task<List<DFM.Model.Models.ShiftEntity>> GetShiftSelect()
		{
			var data = await _dalShiftEntity.FindList(null, p => p.Name);
			return data;
		}


		#endregion

		#region QA

		/// <summary>
		/// 修改工单质检状态
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateQaStatus(UpdateQaStatusRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (!reqModel.Ids.Any())
			{
				result.msg = "请至少选择一条数据进行操作";
				return result;
			}
			var productionOrders = await _dal2.FindList(x => reqModel.Ids.Contains(x.ID));
			if (!productionOrders.Any())
			{
				result.msg = "PoId不存在";
				return result;
			}
			var updateList = new List<ProductionOrderEntity>();
			foreach (var item in productionOrders)
			{
				if (item.QaStatus != reqModel.QaStatus)
				{
					if (reqModel.QaStatus == "通过")
					{
						item.QATime = DateTime.Now;
					}
					else
					{
						item.QATime = null;
					}
					item.QaStatus = reqModel.QaStatus;
					item.Modify(item.ID, _user.Name);
					updateList.Add(item);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateList.Any())
				{
					await _dal2.Update(updateList);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		/// <summary>
		/// 获取工单Text
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<UpdateQaStatusRequestModel>> GetLtexts(UpdateQaStatusRequestModel reqModel)
		{
			var result = new MessageModel<UpdateQaStatusRequestModel>
			{
				msg = "获取失败！",
				success = false,
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(reqModel.Id);
			if (entity == null)
			{
				result.msg = "PoId不存在";
				return result;
			}
			var runOrders = await _exdal.FindList(x => x.ProductionOrderId == reqModel.Id && x.Status == "1" && x.EndTime == null);
			var texts = new UpdateQaStatusRequestModel();
			texts.Id = reqModel.Id;
			texts.IsRunOrder = runOrders != null && runOrders.Any();
			texts.QaStatus = entity.QaStatus;
			var sapOrder = await _sapOrderdal.FindEntity(x => x.Aufnr == entity.ProductionOrderNo);
			if (sapOrder == null)
			{
				result.msg = "未找到SAP订单";
			}
			else
			{
				texts.Ltext1 = sapOrder.Ltext1?.Replace("@@", "\n");
				texts.Ltext2 = sapOrder.Ltext2?.Replace("@@", "\n");
				texts.Ltext3 = sapOrder.Ltext3?.Replace("@@", "\n");
				texts.Ltext4 = sapOrder.Ltext4?.Replace("@@", "\n");
				texts.Ltext5 = sapOrder.Ltext5?.Replace("@@", "\n");
				texts.Ltext6 = sapOrder.Ltext6?.Replace("@@", "\n");
			}
			result.response = texts;
			result.success = true;
			result.msg = "获取成功！";
			return result;
		}

		/// <summary>
		/// 获取煮制工单长文本
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<MaterialProcessDataEntity>>> GetCookieOrderLtexts(UpdateQaStatusRequestModel reqModel)
		{
			var result = new MessageModel<List<MaterialProcessDataEntity>>
			{
				msg = "获取成功！",
				success = true,
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(reqModel.Id);
			if (entity == null)
			{
				result.success = false;
				result.msg = "PoId不存在";
				return result;
			}
			var list = new List<MaterialProcessDataEntity>();
			//煮制长文本
			var ltext1 = await _processDatadal.FindEntity(x => x.Status == "2" && x.OrderId == reqModel.Id && x.Type == 0);
			if (ltext1 == null)
			{
				result.msg = "未找到煮制长文本";
				ltext1 = new MaterialProcessDataEntity();
				//return result;
			}
			else
			{
				ltext1.ProcessData = Enigma.Decrypt(ltext1.ProcessData, ltext1.Token)?.Replace("@@", "\n");
			}
			//备料长文本
			var ltext2 = await _processDatadal.FindEntity(x => x.Status == "2" && x.OrderId == reqModel.Id && x.Type == 2);
			if (ltext2 == null)
			{
				result.msg = "未找到备料长文本";
				ltext2 = new MaterialProcessDataEntity();
				//return result;
			}
			else
			{
				ltext2.ProcessData = Enigma.Decrypt(ltext2.ProcessData, ltext2.Token)?.Replace("@@", "\n");
			}
			list.Add(ltext1);
			list.Add(ltext2);
			result.response = list;
			//result.success = true;
			//result.msg = "获取成功！";
			return result;
		}

		/// <summary>
		/// 修改工单Text
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateLtexts(UpdateQaStatusRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(reqModel.Id);
			if (entity == null)
			{
				result.msg = "PoId不存在";
				return result;
			}
			if (entity.QaStatus == "通过")
			{
				result.msg = "质检通过后不允许修改Ltext";
				return result;
			}
			var sapOrder = await _sapOrderdal.FindEntity(x => x.Aufnr == entity.ProductionOrderNo);
			if (sapOrder == null)
			{
				result.msg = "未找到SAP订单";
				return result;
			}
			sapOrder.Ltext1 = reqModel.Ltext1?.Replace("\n", "@@");
			sapOrder.Ltext2 = reqModel.Ltext2?.Replace("\n", "@@");
			sapOrder.Ltext3 = reqModel.Ltext3?.Replace("\n", "@@");
			sapOrder.Ltext4 = reqModel.Ltext4?.Replace("\n", "@@");
			sapOrder.Ltext5 = reqModel.Ltext5?.Replace("\n", "@@");
			sapOrder.Ltext6 = reqModel.Ltext6?.Replace("\n", "@@");
			_unitOfWork.BeginTran();
			try
			{
				sapOrder.Modify(sapOrder.ID, _user.Name);
				await _sapOrderdal.Update(sapOrder);
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		/// <summary>
		/// 修改工单状态
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdatePoStatus(UpdateStatusRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(reqModel.Id);
			if (entity == null)
			{
				result.msg = "PoId不存在";
				return result;
			}
			entity.PoStatus = reqModel.Status;
			if (reqModel.Status == "3" && entity.EndTime == null)
			{
				entity.EndTime = DateTime.Now;
			}
			if (!string.IsNullOrEmpty(reqModel.ProduceStatus))
			{
				entity.ProduceStatus = reqModel.ProduceStatus;
			}
			if (!string.IsNullOrEmpty(reqModel.Reason))
			{
				entity.Reason = reqModel.Reason;
			}
			_unitOfWork.BeginTran();
			try
			{
				entity.Modify(entity.ID, _user.Name);
				await _dal2.Update(entity);
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			result.msg = "操作成功！";
			return result;
		}

		/// <summary>
		/// 对比判断长文本
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckLongText(string productionOrderId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(productionOrderId);
			if (entity == null)
			{
				result.msg = "productionOrderId不存在";
				return result;
			}
			//获取长文本
			MessageModel<List<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<List<MaterialProcessDataEntity>>("DFM", "api/ProcessData/GetList", _user.GetToken(), new { });
			var processDatas = apiResult_processData.response;
			if (processDatas == null || processDatas.Count == 0)
			{
				result.msg = "processDatas为空";
				return result;
			}
			//获取参数
			MessageModel<List<RecipeParameterModel>> apiResult_recipeParameter = await HttpHelper.PostAsync<List<RecipeParameterModel>>("DFM", "api/RecipeCommon/GetPoRecipeParameterList", _user.GetToken(), new { ProductionOrderId = entity.ID, MaterialVersionId = entity.MaterialVersionId, ProductionTime = entity.PlanStartTime });
			var recipeParameters = apiResult_recipeParameter.response;
			if (recipeParameters == null || recipeParameters.Count == 0)
			{
				result.msg = "recipeParameters为空";
				return result;
			}
			var contextVersionId = recipeParameters.FirstOrDefault()?.RecipeContextVersionId;
			if (contextVersionId == null)
			{
				result.msg = "contextVersionId为空";
				return result;
			}
			//获取PROCESS_DATA_MAPPING
			MessageModel<List<ProcessDataMappingEntity>> apiResult_processDataMapping = await HttpHelper.PostAsync<List<ProcessDataMappingEntity>>("DFM", "api/ProcessDataMapping/GetList", _user.GetToken(), new { });
			var processDataMappings = apiResult_processDataMapping.response;
			if (processDataMappings == null || processDataMappings.Count == 0)
			{
				result.msg = "processDataMappings为空";
				return result;
			}
			var mappings = processDataMappings.FindAll(x => x.VersionId == contextVersionId);
			if (mappings == null || mappings.Count == 0)
			{
				result.msg = "mappings为空";
				return result;
			}
			var processDataIds = mappings.Select(x => x.ProcessData);
			var processData = processDatas.Find(x => x.Status == "Released" /*&& x.VersionId == entity.MaterialVersionId */&& processDataIds.Contains(x.ID));
			if (processData == null)
			{
				result.msg = "processData为空";
				return result;
			}
			var sapprocessData = processDatas.Find(x => x.OrderId == productionOrderId);
			if (sapprocessData == null)
			{
				result.msg = "sapprocessData为空";
				return result;
			}
			//对比不通过则新建
			if (processData.HashData != sapprocessData.HashData)
			{
				//_ = int.TryParse(processData.TextVersion, out int versionNumber);
				//versionNumber++;
				MaterialProcessDataEntity pde = new()
				{
					VersionId = entity.MaterialVersionId,
					ProcessData = sapprocessData.ProcessData,
					HashData = sapprocessData.HashData,
					TextVersion = processData.TextVersion + 1,
					IsReminded = "0",
					Status = "2",
					//OrderId = entity.ID,
				};
				pde.CreateCustomGuid(_user.Name);
				//修改上一版本状态
				processData.Status = "1";
				processData.Modify(processData.ID, _user.Name.ToString());
				ProcessDataMappingEntity pdme = new()
				{
					VersionId = contextVersionId,
					ProcessData = pde.ID
				};
				//调用API接口保存和更新数据
				MessageModel<string> apiResult_updatepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
				MessageModel<string> apiResult_savepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/Insert", _user.GetToken(), pde);
				MessageModel<string> apiResult_savepdme = await HttpHelper.PostAsync<string>("DFM", "api/ProcessDataMapping/SaveForm", _user.GetToken(), pdme);
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<List<dynamic>>> CreateBatch(List<string> productionOrderIds)
		{
			SerilogServer.LogDebug($"数据加载开始", "CreateBatch2");

			var result = new MessageModel<List<dynamic>>
			{
				msg = "操作失败！",
				success = false
			};
			List<dynamic> list = new List<dynamic>();
			result.response = list;
			if (!productionOrderIds.Any())
			{
				result.msgDev = "productionOrderIds为空";
				return result;
			}
			var poEntitys = await _dal2.FindList(x => productionOrderIds.Contains(x.ID) || (productionOrderIds.Contains(x.ParentId) && x.Type == "Batch"));
			List<PoSegmentRequirementEntity> psres = new List<PoSegmentRequirementEntity>();
			List<PoConsumeRequirementEntity> pcres = new List<PoConsumeRequirementEntity>();
			List<PoProducedRequirementEntity> ppres = new List<PoProducedRequirementEntity>();
			List<Model.Models.BatchEntity> bes = new List<Model.Models.BatchEntity>();
			List<BatchConsumeRequirementEntity> bcres = new List<BatchConsumeRequirementEntity>();
			List<BatchProducedRequirementEntity> bpres = new List<BatchProducedRequirementEntity>();
			List<PoEquipmentEntity> poEs = new List<PoEquipmentEntity>();
			var materialVersions = await _dal06.FindList(x => poEntitys.Any(x2 => x2.MaterialVersionId == x.ID));
			var materials = await _dal05.Query();
			var sapSegmentMaterials = await _dal03.Query();
			var routings = await _SapPoRoutingdal.FindList(x => poEntitys.Any(x2 => x2.ProductionOrderNo == x.Aufnr));
			var equipments = await _equdal.Query();
			var sapSegments = await _dal02.Query();
			var sapSegmentEquipments1 = await _segmentequdal.Query();
			var sapSegmentMaterialSteps1 = await _dal04.Query();
			var allOrderBoms = await _dal13.FindList(x => poEntitys.Any(x2 => x2.ProductionOrderNo == x.ProductionOrderId));
			var throatadditions = await _orderThroDal.FindList(x => productionOrderIds.Contains(x.OrderId));
			var allthroatadditions = await _throatadal.Query();
			var workorderthroats = await _workthroatadal.Query();
			var materialPropertys = await _dal10.FindList(x => x.PropertyCode == "RequiresPreWeigh" && x.PropertyValue == "1");
			var materialPropertys2 = await _dalMaterialPropertyViewEntity.Query();
			var units = await _unitDal.Query();

			SerilogServer.LogDebug($"CreateBatch 数据加载完成", "CreateBatch");

			var orders = new List<ProductionOrderModel>();
			foreach (var productionOrderId in productionOrderIds)
			{
				var poEntity = poEntitys?.Find(x => x.ID == productionOrderId);
				if (poEntity == null)
				{
					result.msgDev += $"ProductionOrderId{productionOrderId}不存在;";
					list.Add(new { ProductionOrderId = productionOrderId, ProductionOrderNo = "", Msg = "工单Id不存在" });
					continue;
				}
				var poModel = new ProductionOrderModel() { Po = poEntity, Equipments = new List<PoEquipmentEntity>() };
				if (poEntity.PoStatus != "1" && poEntity.PoStatus != "2")
				{
					SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}]工单当前状态[{poEntity.PoStatus}]不允许重新解析构建批", "发布工单PublishOrderLog");
					result.msgDev += $"[{poEntity.ProductionOrderNo}]工单当前状态[{poEntity.PoStatus}]不允许重新解析构建批;";
					list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单当前状态[{poEntity.PoStatus}]不允许重新解析构建批" });
					continue;
				}
				var materialVersion = materialVersions.Find(x => x.ID == poEntity.MaterialVersionId);
				if (materialVersion == null)
				{
					result.msgDev += $"[{poEntity.ProductionOrderNo}]工单MaterialVersionId[{poEntity.MaterialVersionId}]不存在;";
					list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单MaterialVersionId[{poEntity.MaterialVersionId}]不存在" });
					continue;
				}
				var material_po = materials.Find(x => x.ID == materialVersion.MaterialId);
				if (material_po == null)
				{
					result.msgDev += $"[{poEntity.ProductionOrderNo}]工单MaterialId[{materialVersion.MaterialId}]不存在;";
					list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单MaterialId[{materialVersion.MaterialId}]不存在" });
					continue;
				}
				var sapSegmentCodes = new List<string>();
				//煮制工单
				if (poEntity.SapOrderType == "ZXH2")
				{
					if (string.IsNullOrEmpty(poEntity.SegmentCode))
					{
						result.msgDev += $"[{poEntity.ProductionOrderNo}]煮制工单SegmentCode为空;";
						list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"煮制工单SegmentCode为空" });
						continue;
					}
					string[] parts = poEntity.SegmentCode.Split('#');
					if (parts.Length > 0)
					{
						sapSegmentCodes.Add(parts[0]);
					}
				}
				else
				{
					var sapPoRoutings = routings.FindAll(x => x.Aufnr == poEntity.ProductionOrderNo);
					if (sapPoRoutings == null || sapPoRoutings.Count == 0)
					{
						result.msgDev += $"[{poEntity.ProductionOrderNo}]灌包装工单未找到sapPoRoutings;";
						list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"灌包装工单未找到sapPoRoutings" });
						continue;
					}
					sapSegmentCodes = sapPoRoutings.Select(x => x.Arbpl).ToList();
				}
				var orderBoms = allOrderBoms.FindAll(x => x.ProductionOrderId == poEntity.ProductionOrderNo);
				if (orderBoms == null || orderBoms.Count == 0)
				{
					result.msgDev += $"[{poEntity.ProductionOrderNo}]工单未找到orderBoms;";
					list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单未找到orderBoms" });
					continue;
				}
				string lineId = equipments.Find(x => x.EquipmentCode == poEntity.LineCode)?.ID;
				if (lineId == null)
				{
					lineId = equipments.Find(x => x.EquipmentName == poEntity.LineCode)?.ID;
				}
				var poList = poEntitys.Where(x => x.ParentId == productionOrderId && x.Type == "Batch").OrderBy(x => x.Sequence).ToList();
				var o_throatadditions = throatadditions.FindAll(x => x.OrderId == productionOrderId);
				var o_throMatIds = o_throatadditions.Select(x => x.MatId)?.ToList() ?? new List<string>();
				var m_throatadditions = allthroatadditions.FindAll(x => !o_throMatIds.Contains(x.MatId) && x.MatAddableId == poEntity.MaterialId);
				if (poList == null || poList.Count == 0)
				{
					//不包含在工单关联喉头中的加进来 数量都为0
					foreach (var item in m_throatadditions)
					{
						var orderThroataddition = new OrderThroatadditionEntity()
						{
							MatId = item.MatId,
							OrderId = poEntity.ID,
							SumInweight = 0,
							TWeight = 0,
							Rate = item.Rate
						};
						o_throatadditions.Add(orderThroataddition);
					}
				}
				//茄汁工单
				else
				{
					m_throatadditions = allthroatadditions.FindAll(x => x.MatAddableId == poEntity.MaterialId);
				}
				foreach (var sapSegmentCode in sapSegmentCodes)
				{
					List<Model.Models.BatchEntity> o_bes = new List<Model.Models.BatchEntity>();
					var eqs_seg = new List<string>();
					var sapSegment = sapSegments.FindAll(x => x.SegmentName == sapSegmentCode && x.Level == 2)?.FirstOrDefault();
					if (sapSegment == null)
					{
						result.msgDev += $"[{poEntity.ProductionOrderNo}]工单sapSegment为空;";
						list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单sapSegment为空" });
						goto outerLoop;
					}
					{
						//获取工序关联的设备
						var sapSegmentEquipments = sapSegmentEquipments1.FindAll(x => x.SapSegmentId == sapSegment.ID);
						if (sapSegmentEquipments == null || sapSegmentEquipments.Count == 0)
						{
							result.msgDev += $"[{poEntity.ProductionOrderNo}]工单未找到工序关联设备sapSegmentEquipments;";
							list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单未找到工序关联设备sapSegmentEquipments" });
							goto outerLoop;
						}
						foreach (var item in sapSegmentEquipments)
						{
							var eq = equipments?.Find(x => x.ID == item.EquipmentId);
							if (eq == null)
							{
								continue;
							}
							var eqp = equipments?.Find(x => x.ID == eq.ParentId);
							if (eqp == null)
							{
								continue;
							}
							if (!poEs.Exists(x => x.OrderId == poEntity.ID && x.SegmentId == item.SapSegmentId && x.EquipmentId == item.EquipmentId) && ((poEntity.SapOrderType == "ZXH2" && eqp.EquipmentCode == poEntity.SegmentCode) || (poEntity.SapOrderType != "ZXH2" && eqp.EquipmentCode == sapSegmentCode)))
							{
								var poe = new PoEquipmentEntity()
								{
									OrderId = poEntity.ID,
									SegmentId = item.SapSegmentId,
									EquipmentId = item.EquipmentId
								};
								poe.CreateCustomGuid(_user.Name);
								poEs.Add(poe);
								eqs_seg.Add(item.EquipmentId);
								poModel.Equipments.Add(poe);
							}
						}
						if (eqs_seg.Count == 0)
						{
							result.msgDev += $"[{poEntity.ProductionOrderNo}]工单有工序未找到关联设备sapSegmentEquipments;";
							list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单有工序未找到关联设备sapSegmentEquipments" });
							goto outerLoop;
						}
						PoSegmentRequirementEntity poSegmentRequirement = new PoSegmentRequirementEntity()
						{
							ProductionOrderId = productionOrderId,
							SegmentId = sapSegment.ID
						};
						poSegmentRequirement.CreateCustomGuid(_user.Name);
						psres.Add(poSegmentRequirement);

						List<SapSegmentMaterialStepEntity> sapSegmentMaterialSteps = null;
						//煮制工单
						if (poEntity.SapOrderType == "ZXH2")
						{
							//根据工单BOMVersion和工序获取
							var sapSegmentMaterial = sapSegmentMaterials.Find(x => x.MaterialVersionId == poEntity.MaterialVersionId && x.SapSegmentId == sapSegment.ID);
							if (sapSegmentMaterial == null)
							{
								result.msgDev += $"[{poEntity.ProductionOrderNo}]工单未找到sapSegmentMaterial;";
								list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单未找到sapSegmentMaterial" });
								goto outerLoop;
							}
							//获取sapSegmentMaterial
							sapSegmentMaterialSteps = sapSegmentMaterialSteps1.FindAll(x => x.SapSegmentMaterialId == sapSegmentMaterial.ID);
							if (sapSegmentMaterialSteps == null || sapSegmentMaterialSteps.Count == 0)
							{
								result.msgDev += $"[{poEntity.ProductionOrderNo}]工单未找到sapSegmentMaterialSteps;";
								list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工单未找到sapSegmentMaterialSteps" });
								goto outerLoop;
							}
						}
						SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]物料产出需求开始", "CreateBatch");
						#region 物料产出需求

						List<PoProducedRequirementEntity> ppres2 = new List<PoProducedRequirementEntity>();
						int i = 0;

						//写入PPM_B_PO_PRODUCED_REQUIREMENT
						PoProducedRequirementEntity producedRequirementEntity = new()
						{
							ProductionOrderId = poEntity.ID,
							PoSegmentRequirementId = poSegmentRequirement.ID,
							MaterialId = materialVersion.MaterialId,
							MaterialVersionId = poEntity.MaterialVersionId,
							Quantity = poEntity.PlanQty,
							UnitId = material_po.Unit,
							//MaterialLotNo = "",
							//StorageBin = "",
							//StorageLocation = "",
							SortNo = ++i,
							Deleted = 0
						};
						producedRequirementEntity.CreateCustomGuid(_user.Name);
						ppres2.Add(producedRequirementEntity);
						ppres.Add(producedRequirementEntity);

						#endregion
						SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]物料产出需求结束", "CreateBatch");
						#region 创建批次

						if (!poList.Any())
						{
							poList.Add(poEntity);
						}
						int j = 0;
						foreach (ProductionOrderEntity po in poList)
						{
							j++;
							var materialVersion1 = materialVersions.Find(x => x.ID == po.MaterialVersionId);
							var material = materials.Find(x => x.ID == materialVersion1.MaterialId);
							//写入PPM_B_BATCH
							Model.Models.BatchEntity batch = new()
							{
								LineId = lineId,
								Number = j.ToString(),
								ProductionOrderId = poEntity.ID,
								PoSegmentRequirementId = poSegmentRequirement.ID,
								PoProducedRequirementId = producedRequirementEntity.ID,
								/*后续改成生成批次号方法*/
								//BatchCode = "B" + DateTime.Now.ToString("yyyyMMddHHmmssfff"),
								TargetQuantity = po.PlanQty,
								MaterialId = materialVersion1.MaterialId,
								MaterialVersionId = po.MaterialVersionId,
								MaterialCode = material.Code,
								//MaterialDescription = material.NAME,
								UnitId = material.Unit,
								MaterialDescription = material.Description,
								RunEquipmentId = "",
								PrepStatus = "1",
								Status = "1"
							};
							batch.CreateCustomGuid(_user.Name);
							bes.Add(batch);
							o_bes.Add(batch);
							foreach (var poProduced in ppres2)
							{
								//写入PPM_B_BATCH_PRODUCED_REQUIREMENT
								BatchProducedRequirementEntity batchProducedRequirement = new BatchProducedRequirementEntity
								{
									BatchId = batch.ID,
									PoProducedRequirementId = producedRequirementEntity.ID,
									Quantity = poProduced == ppres[0] ? poProduced.Quantity.Value : batch.TargetQuantity.Value
								};
								batchProducedRequirement.CreateCustomGuid(_user.Name);
								bpres.Add(batchProducedRequirement);
							}
						}

						#endregion
						SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]创建批次结束", "CreateBatch");
						#region 物料消耗需求

						foreach (var orderBom in orderBoms)
						{
							if (string.IsNullOrEmpty(orderBom.ItemId))
							{
								continue;
							}
							var materialCode = Enigma.Decrypt(orderBom.ItemId, orderBom.TranNo);
							var materialName = Enigma.Decrypt(orderBom.ItemDecs, orderBom.TranNo);
							var material = materials.Find(x => x.Code == materialCode);
							if (material == null)
							{
								result.msgDev += $"工序[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]未找到物料[{materialCode}];";
								list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"工序[{sapSegment.SegmentCode}]未找到物料[{materialCode}]" });
								goto outerLoop;
							}
							int sortOrder = 0;
							decimal q1 = 0;
							decimal? q3 = null;
							string unit = orderBom.ItemUnit;
							bool flag = false;
							var poUnit = units.FirstOrDefault(x => x.ID == material_po.Unit)?.Name ?? "";
							var v1 = 1;
							if (poUnit.ToUpper() == "KG" && orderBom.ItemUnit?.ToLower() == "g")
							{
								v1 = 1000;
							}
							//煮制工单
							if (poEntity.SapOrderType == "ZXH2")
							{
								var sapSegmentMaterialStep = sapSegmentMaterialSteps.Find(x => x.MaterialId == material.ID);
								if (sapSegmentMaterialStep == null)
								{
									result.msgDev += $"[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]sapSegmentMaterialStep未找到material[{materialCode}];";
									list.Add(new { ProductionOrderId = productionOrderId, poEntity.ProductionOrderNo, Msg = $"[{sapSegment.SegmentCode}]sapSegmentMaterialStep未找到material[{materialCode}]" });
									goto outerLoop;
								}
								//q3 = sapSegmentMaterialStep.AdjustPercentQuantity;
								sortOrder = sapSegmentMaterialStep.SortOrder;
								//if (unit != "g")
								if (unit != "xxx")//需要g转换为KG就注释这行，放开上一行
								{
									if (sapSegmentMaterialStep.AdjustPercentQuantity == null || sapSegmentMaterialStep.AdjustPercentQuantity == 0)
									{
										q1 = 0;
										//q1 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : poEntity.PlanQty / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.Quantity, 3);
									}
									else
									{
										q1 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : poEntity.PlanQty / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.AdjustPercentQuantity.Value * v1, 3);
									}
								}
								else
								{
									flag = true;
									unit = "KG";
									if (sapSegmentMaterialStep.AdjustPercentQuantity == null || sapSegmentMaterialStep.AdjustPercentQuantity == 0)
									{
										q1 = 0;
										//q1 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : poEntity.PlanQty / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.Quantity / 1000, 3);
									}
									else
									{
										q1 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : poEntity.PlanQty / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.AdjustPercentQuantity.Value / 1000, 3);
									}
									if (q3 != null)
									{
										q3 = Math.Round(q3.Value / 1000, 3);
									}
								}
							}
							else
							{
								q1 = orderBom.ItemQuantityWithoutLoss.Value;
								q3 = orderBom.ItemQuantityWithLoss;
							}
							if (q1 == 0)
							{
								continue;
							}
							var unitId = units?.Find(x => x.Name == unit)?.ID ?? unit;
							//写入PPM_B_PO_CONSUME_REQUIREMENT
							PoConsumeRequirementEntity poConsumeRequirementEntity = new()
							{
								ProductionOrderId = poEntity.ID,
								PoSegmentRequirementId = poSegmentRequirement.ID,
								MaterialId = material.ID,
								//MaterialVersionId = "",
								//MaterialCode = material.Code,
								//MaterialDescription = material.Description;
								//MaterialDescription = material.NAME;
								//Quantity = Math.Round(poEntity.PlanQty * quantity * 0.01m, 3),
								Quantity = q1,
								UnitId = unitId,
								WeighingQty = GetMqty(materialPropertys2, material.ID, q1, unit),
								AdjustPercentQuantity = q3,
								//MaterialLotNo = "",
								StorageBin = orderBom.WarehouseId,
								//StorageLocation = "",
								SortOrder = sortOrder,
								Deleted = 0,
								ChangeUnit = unit
							};
							poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
							pcres.Add(poConsumeRequirementEntity);
							var pv2 = materialPropertys.Find(x => x.MaterialId == material.ID)?.PropertyValue;
							foreach (var batch in o_bes)
							{
								if (pv2?.ToLower() == "1")
								{
									batch.PrepStatus = "2";
								}
								decimal q2 = 0;
								//煮制工单
								if (poEntity.SapOrderType == "ZXH2")
								{
									var sapSegmentMaterialStep = sapSegmentMaterialSteps.Find(x => x.MaterialId == material.ID);

									if (!flag)
									{
										if (sapSegmentMaterialStep.AdjustPercentQuantity == null || sapSegmentMaterialStep.AdjustPercentQuantity == 0)
										{
											q2 = 0;
											//q2 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : batch.TargetQuantity.Value / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.Quantity, 3);
										}
										else
										{
											q2 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : batch.TargetQuantity.Value / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.AdjustPercentQuantity.Value * v1, 3);
										}
									}
									else
									{
										if (sapSegmentMaterialStep.AdjustPercentQuantity == null || sapSegmentMaterialStep.AdjustPercentQuantity == 0)
										{
											q2 = 0;
											//q2 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : batch.TargetQuantity.Value / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.Quantity / 1000, 3);
										}
										else
										{
											q2 = Math.Round((sapSegmentMaterialStep.ParentQuantity == 0 ? 0 : batch.TargetQuantity.Value / sapSegmentMaterialStep.ParentQuantity) * sapSegmentMaterialStep.AdjustPercentQuantity.Value / 1000, 3);
										}
									}
								}
								else
								{
									q2 = orderBom.ItemQuantityWithoutLoss.Value;
								}
								if (q2 == 0)
								{
									continue;
								}
								BatchConsumeRequirementEntity batchConsumeRequirement = new BatchConsumeRequirementEntity
								{
									BatchId = batch.ID,
									PoConsumeRequirementId = poConsumeRequirementEntity.ID,
									//Quantity = Math.Round(batch.TargetQuantity.Value * quantity * 0.01m, 3)
									Quantity = q2,
									WeighingQty = GetMqty(materialPropertys2, material.ID, q2, unit),
									FeedStates = 0,
									ChangeUnit = poConsumeRequirementEntity.ChangeUnit
								};
								batchConsumeRequirement.CreateCustomGuid(_user.Name);
								bcres.Add(batchConsumeRequirement);
							}
						}
						//喉头逻辑
						if (poList == null || poList.Count == 0 || poList.Count == 1)
						{
							foreach (var item in o_throatadditions)
							{
								var material = materials.Find(x => x.ID == item.MatId);
								string unitId = material?.Unit ?? "";
								//var orderQty = Math.Round(poEntity.PlanQty * item.Rate, 3);
								var orderQty = Math.Round(item.SumInweight.Value, 3);
								var adjustPercentQuantity = item.TWeight.Value;
								string changeUnit = "";
								if (material != null)
								{
									var unit = units?.FirstOrDefault(x => x.ID == material.Unit);
									changeUnit = unit.Name;
								}
								//写入PPM_B_PO_CONSUME_REQUIREMENT
								PoConsumeRequirementEntity poConsumeRequirementEntity = new()
								{
									ProductionOrderId = poEntity.ID,
									PoSegmentRequirementId = poSegmentRequirement.ID,
									MaterialId = item.MatId,
									//MaterialVersionId = "",
									//MaterialCode = material.Code,
									//MaterialDescription = material.NAME;
									Quantity = orderQty,
									WeighingQty = GetMqty(materialPropertys2, item.MatId, orderQty, changeUnit),
									AdjustPercentQuantity = adjustPercentQuantity,
									UnitId = unitId,
									//MaterialLotNo = "",
									StorageBin = "SUR3",
									//StorageLocation = "",
									SortOrder = 1,
									Deleted = 0,
									ChangeUnit = changeUnit
								};
								poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
								pcres.Add(poConsumeRequirementEntity);

								foreach (var batch in o_bes)
								{
									//var btchQty = Math.Round(batch.TargetQuantity.Value * item.Rate, 3);
									var btchQty = orderQty;
									BatchConsumeRequirementEntity batchConsumeRequirement = new BatchConsumeRequirementEntity
									{
										BatchId = batch.ID,
										PoConsumeRequirementId = poConsumeRequirementEntity.ID,
										Quantity = btchQty,
										WeighingQty = GetMqty(materialPropertys2, item.MatId, btchQty, changeUnit),
										FeedStates = 0,
										ChangeUnit = poConsumeRequirementEntity.ChangeUnit
									};
									batchConsumeRequirement.CreateCustomGuid(_user.Name);
									bcres.Add(batchConsumeRequirement);
								}
							}
						}
						//茄汁工单
						else
						{
							foreach (var item in m_throatadditions)
							{
								var material = materials.Find(x => x.ID == item.MatId);
								string unitId = material?.Unit ?? "";
								string changeUnit = "";
								if (material != null)
								{
									var unit = units?.FirstOrDefault(x => x.ID == material.Unit);
									changeUnit = unit.Name;
								}
								//写入PPM_B_PO_CONSUME_REQUIREMENT
								PoConsumeRequirementEntity poConsumeRequirementEntity = new()
								{
									ProductionOrderId = poEntity.ID,
									PoSegmentRequirementId = poSegmentRequirement.ID,
									MaterialId = item.MatId,
									//MaterialVersionId = "",
									//MaterialCode = material.Code,
									//MaterialDescription = material.NAME;
									Quantity = 0,
									WeighingQty = GetMqty(materialPropertys2, item.MatId, 0, changeUnit),
									AdjustPercentQuantity = 0,
									UnitId = unitId,
									//MaterialLotNo = "",
									StorageBin = "SUR3",
									//StorageLocation = "",
									SortOrder = 1,
									Deleted = 0,
									ChangeUnit = changeUnit
								};
								poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
								pcres.Add(poConsumeRequirementEntity);

								foreach (var batch in o_bes)
								{
									var batchId = poList.Where(x => x.Sequence.ToString() == batch.Number).FirstOrDefault()?.ID;
									var batchQty = workorderthroats.FindAll(x => x.OrderId == batchId && x.MatId == item.MatId)?.Sum(x => x.Inweight) ?? 0;
									poConsumeRequirementEntity.Quantity += batchQty;
									poConsumeRequirementEntity.AdjustPercentQuantity += batchQty;
									BatchConsumeRequirementEntity batchConsumeRequirement = new BatchConsumeRequirementEntity
									{
										BatchId = batch.ID,
										PoConsumeRequirementId = poConsumeRequirementEntity.ID,
										Quantity = batchQty,
										WeighingQty = GetMqty(materialPropertys2, item.MatId, batchQty, changeUnit),
										FeedStates = 0,
										ChangeUnit = poConsumeRequirementEntity.ChangeUnit
									};
									batchConsumeRequirement.CreateCustomGuid(_user.Name);
									bcres.Add(batchConsumeRequirement);
								}
								poConsumeRequirementEntity.WeighingQty = GetMqty(materialPropertys2, item.MatId, poConsumeRequirementEntity.Quantity.Value, changeUnit);
							}
						}

						#endregion
						SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}][{sapSegment.SegmentCode}]计算物料消耗需求结束", "CreateBatch");
					}
				}
				orders.Add(poModel);
				continue;
			outerLoop:
				psres.RemoveAll(x => x.ProductionOrderId == poEntity.ID);
				pcres.RemoveAll(x => x.ProductionOrderId == poEntity.ID);
				ppres.RemoveAll(x => x.ProductionOrderId == poEntity.ID);
				var batchIds = bes.FindAll(x => x.ProductionOrderId == poEntity.ID)?.Select(x => x.ID);
				bcres.RemoveAll(x => batchIds.Contains(x.BatchId));
				bpres.RemoveAll(x => batchIds.Contains(x.BatchId));
				bes.RemoveAll(x => x.ProductionOrderId == poEntity.ID);
				poEs.RemoveAll(x => x.OrderId == poEntity.ID);
			}

			_unitOfWork.BeginTran();
			try
			{
				if (psres.Count > 1000)
				{
					await _dal4.AddBigData(psres);
				}
				else if (psres.Count > 0)
				{
					await _dal4.Add(psres);
				}

				if (pcres.Count > 1000)
				{
					await _dal5.AddBigData(pcres);
				}
				else if (pcres.Count > 0)
				{
					await _dal5.Add(pcres);
				}

				if (ppres.Count > 1000)
				{
					await _dal6.AddBigData(ppres);
				}
				else if (ppres.Count > 0)
				{
					await _dal6.Add(ppres);
				}

				if (bes.Count > 1000)
				{
					await _dal3.AddBigData(bes);
				}
				else if (bes.Count > 0)
				{
					await _dal3.Add(bes);
				}

				if (bcres.Count > 1000)
				{
					await _dal7.AddBigData(bcres);
				}
				else if (bcres.Count > 0)
				{
					await _dal7.Add(bcres);
				}

				if (bpres.Count > 1000)
				{
					await _dal8.AddBigData(bpres);
				}
				else if (bpres.Count > 0)
				{
					await _dal8.Add(bpres);
				}

				if (poEs.Count > 1000)
				{
					await _dalpoeq.AddBigData(poEs);
				}
				else if (poEs.Count > 0)
				{
					await _dalpoeq.Add(poEs);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				SerilogServer.LogDebug($"CreateBatch 异常：" + ex.ToString(), "CreateBatch");
				_unitOfWork.RollbackTran();
				result.msgDev = ex.Message;
				return result;
			}

			SerilogServer.LogDebug($"[{string.Join(',', orders.Select(x => x.Po.ProductionOrderNo))}]提交完成", "CreateBatch");
			foreach (var item in orders)
			{
				MessageModel<string> apiResult = await HttpHelper.PostAsync<string>("DFM", "api/RecipeCommon/BindPoRecipe", _user.GetToken(), new { ProductionOrderId = item.Po.ID, MaterialVersionId = item.Po.MaterialVersionId, ProductionTime = item.Po.PlanStartTime, EquipmentIdList = item.Equipments.Select(x => x.EquipmentId) });
				try
				{
					var ssresult = await _iInterfaceServices.SendOrderInfoToSS(item.Po.ID, 0);
				}
				catch (Exception ex)
				{
				}
			}
			SerilogServer.LogDebug($"[{string.Join(',', orders.Select(x => x.Po.ProductionOrderNo))}]绑定配方数据完成", "CreateBatch");
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		public async Task<MessageModel<string>> AddPoConsumeRequirement(PoConsumeRequirementRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel.Quantity == null)
			{
				result.msg = "数量不能为空";
				return result;
			}
			var poConsumeRequirements = await _dal5.FindList(x => x.PoSegmentRequirementId == reqModel.PoSegmentRequirementId && x.MaterialId == reqModel.MaterialId);
			if (poConsumeRequirements?.Count > 0)
			{
				result.msg = "该物料需求已存在";
				return result;
			}
			List<PoConsumeRequirementEntity> pcres = new List<PoConsumeRequirementEntity>();
			List<BatchConsumeRequirementEntity> bcres = new List<BatchConsumeRequirementEntity>();
			var materialPropertys2 = await _dalMaterialPropertyViewEntity.FindList(x => x.ID == reqModel.MaterialId);
			var unit = await _unitDal.FindEntity(reqModel.UnitId);
			//写入PPM_B_PO_CONSUME_REQUIREMENT
			PoConsumeRequirementEntity poConsumeRequirementEntity = new()
			{
				ProductionOrderId = reqModel.ProductionOrderId,
				PoSegmentRequirementId = reqModel.PoSegmentRequirementId,
				MaterialId = reqModel.MaterialId,
				Quantity = reqModel.Quantity,
				UnitId = reqModel.UnitId,
				WeighingQty = GetMqty(materialPropertys2, reqModel.MaterialId, reqModel.Quantity.Value, unit.Name),
				AdjustPercentQuantity = 0,
				MaterialLotNo = reqModel.MaterialLotNo,
				StorageBin = "PKG3",
				//StorageLocation = "",
				SortOrder = 0,
				Deleted = 0,
				ChangeUnit = unit.Name
			};
			poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
			pcres.Add(poConsumeRequirementEntity);
			var batchs = await _dal3.FindList(x => x.ProductionOrderId == reqModel.ProductionOrderId);
			foreach (var batch in batchs)
			{
				BatchConsumeRequirementEntity batchConsumeRequirement = new BatchConsumeRequirementEntity
				{
					BatchId = batch.ID,
					PoConsumeRequirementId = poConsumeRequirementEntity.ID,
					Quantity = poConsumeRequirementEntity.Quantity.Value,
					WeighingQty = poConsumeRequirementEntity.WeighingQty,
					FeedStates = 0,
					ChangeUnit = poConsumeRequirementEntity.ChangeUnit
				};
				batchConsumeRequirement.CreateCustomGuid(_user.Name);
				bcres.Add(batchConsumeRequirement);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (pcres.Count > 1000)
				{
					await _dal5.AddBigData(pcres);
				}
				else if (pcres.Count > 0)
				{
					await _dal5.Add(pcres);
				}
				if (bcres.Count > 1000)
				{
					await _dal7.AddBigData(bcres);
				}
				else if (bcres.Count > 0)
				{
					await _dal7.Add(bcres);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msgDev = ex.Message;
				return result;
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		/// <summary>
		/// 计算物料的拼锅数量
		/// </summary>
		/// <param name="mCode"></param>
		/// <param name="qty"></param>
		/// <returns></returns>
		public decimal GetMqty(string mID, decimal qty)
		{
			try
			{
				var result = _dalMaterialPropertyViewEntity.FindEntity(p => p.ID == mID).Result;
				if (result != null)
				{
					string fullBags = result.Bagweight;
					string fullType = result.Fulltype;
					if (fullType != null)
					{
						if (fullType == "1")
						{
							decimal ys = Math.Round(qty % Convert.ToDecimal(fullBags), 3);
							return ys;
						}
					}

				}
				return qty;
			}
			catch (Exception)
			{
				return qty;
			}
		}

		/// <summary>
		/// 计算物料的拼锅数量
		/// </summary>
		/// <param name="mCode"></param>
		/// <param name="qty"></param>
		/// <returns></returns>
		public decimal GetMqty(List<MaterialPropertyViewEntity> list, string mID, decimal qty, string unit)
		{
			try
			{
				int v = unit?.ToLower() == "g" ? 1000 : 1;
				qty /= v;
				var result = list.Find(p => p.ID == mID);
				if (result != null)
				{
					string fullBags = result.Bagweight;
					string fullType = result.Fulltype;
					if (fullType != null)
					{
						if (fullType == "1")
						{
							decimal ys = Math.Round(qty % Convert.ToDecimal(fullBags), 3);
							return ys;
						}
					}

				}
				return qty;
			}
			catch (Exception)
			{
				return qty;
			}
		}

		/// <summary>
		/// 根据工单id获取相关联的设备
		/// </summary>
		/// <param name="productionOrderId"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<Select>>> GetEquipmentsByOrderId(string productionOrderId)
		{
			var result = new MessageModel<List<Select>>
			{
				msg = "获取成功！",
				success = true,
				response = new List<Select>()
			};
			var segmentIds = (await _posegmentrdal.FindList(x => x.ProductionOrderId == productionOrderId))?.Select(x => x.SegmentId);
			if (segmentIds != null && segmentIds.Count() > 0)
			{
				var sapSegmentEquipmentIds = (await _segmentequdal.FindList(x => segmentIds.Contains(x.SapSegmentId)))?.Select(x => x.EquipmentId)?.Distinct();
				if (sapSegmentEquipmentIds != null && sapSegmentEquipmentIds.Count() > 0)
				{
					result.response = (await _equdal.FindList(x => sapSegmentEquipmentIds.Contains(x.ID))).Select(x => new Select() { key = x.ID, value = x.EquipmentCode }).ToList();
				}
			}
			return result;
		}

		/// <summary>
		/// 重新生成批次
		/// </summary>
		/// <param name="productionOrderIds"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> RebuildBatch(List<string> productionOrderIds)
		{
			SerilogServer.LogDebug($"开始构建批次", "RebuildBatchLog");
			var result = new MessageModel<string>
			{
				msg = "操作成功！",
				success = true
			};
			if (!productionOrderIds.Any())
			{
				result.msg = "至少选择一条数据";
				result.success = false;
				return result;
			}
			var productionOrders = await _dal2.FindList(x => productionOrderIds.Contains(x.ID));
			var batchs = await _dal3.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poSegmentRequirements = await _dal4.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poConsumeRequirements = await _dal5.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poProducedRequirements = await _dal6.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poEquipments = await _dalpoeq.FindList(x => productionOrderIds.Contains(x.OrderId));
			var allbatchConsumeRequirements = await _dal7.Query();
			var allbatchProducedRequirements = await _dal8.Query();
			var orderIds = new List<string>();
			var d_batchIds = new List<string>();
			var d_poSegmentRequirementIds = new List<string>();
			var d_poConsumeRequirementIds = new List<string>();
			var d_poProducedRequirementIds = new List<string>();
			var d_poEquipmentIds = new List<string>();
			var d_batchConsumeRequirementIds = new List<string>();
			var d_batchProducedRequirementIds = new List<string>();
			foreach (var productionOrderId in productionOrderIds)
			{
				var productionOrder = productionOrders?.Find(x => x.ID == productionOrderId);
				if (productionOrder == null)
				{
					result.msg = $"未找到productionOrderId{productionOrderId}";
					result.success = false;
					continue;
				}
				if (productionOrder.PoStatus != "1" && productionOrder.PoStatus != "2")
				{
					result.msgDev += $"构建失败，该工单{productionOrder.ProductionOrderNo}当前状态不允许重新解析构建批;";
					result.success = false;
					continue;
				}
				var batchs2 = batchs.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (batchs2.Any())
				{
					if (batchs2.Exists(x => x.PrepStatus != "1" && x.PrepStatus != "2"))
					{
						result.msgDev += $"构建失败，该工单{productionOrder.ProductionOrderNo}已开始备料;";
						result.success = false;
						continue;
					}

					var batchIds = batchs2.Select(x => x.ID);
					d_batchIds.AddRange(batchIds);
					var batchConsumeRequirements = allbatchConsumeRequirements.FindAll(x => batchIds.Contains(x.BatchId));
					if (batchConsumeRequirements.Any())
					{
						d_batchConsumeRequirementIds.AddRange(batchConsumeRequirements.Select(x => x.ID));
					}

					var batchProducedRequirements = allbatchProducedRequirements.FindAll(x => batchIds.Contains(x.BatchId));
					if (batchProducedRequirements.Any())
					{
						d_batchProducedRequirementIds.AddRange(batchProducedRequirements.Select(x => x.ID));
					}
				}

				orderIds.Add(productionOrderId);

				var poSegmentRequirements2 = poSegmentRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poSegmentRequirements2.Any())
				{
					var poSegmentRequirementIds = poSegmentRequirements2.Select(x => x.ID);
					d_poSegmentRequirementIds.AddRange(poSegmentRequirementIds);
				}
				var poConsumeRequirements2 = poConsumeRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poConsumeRequirements2.Any())
				{
					var poConsumeRequirementIds = poConsumeRequirements2.Select(x => x.ID);
					d_poConsumeRequirementIds.AddRange(poConsumeRequirementIds);
				}
				var poProducedRequirements2 = poProducedRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poProducedRequirements2.Any())
				{
					var poProducedRequirementIds = poProducedRequirements2.Select(x => x.ID);
					d_poProducedRequirementIds.AddRange(poProducedRequirementIds);
				}
				var poEquipments2 = poEquipments.FindAll(x => x.OrderId == productionOrderId);
				if (poEquipments2.Any())
				{
					var poEquipmentIds = poEquipments2.Select(x => x.ID);
					d_poEquipmentIds.AddRange(poEquipmentIds);
				}
			}
			try
			{
				SerilogServer.LogDebug($"开始删除批次等信息", "RebuildBatchLog");
				_unitOfWork.BeginTran();
				try
				{
					if (d_batchIds.Any())
					{
						await _dal3.DeleteByIds(d_batchIds.ToArray());
					}
					if (d_poSegmentRequirementIds.Any())
					{
						await _dal4.DeleteByIds(d_poSegmentRequirementIds.ToArray());
					}
					if (d_poConsumeRequirementIds.Any())
					{
						await _dal5.DeleteByIds(d_poConsumeRequirementIds.ToArray());
					}
					if (d_poProducedRequirementIds.Any())
					{
						await _dal6.DeleteByIds(d_poProducedRequirementIds.ToArray());
					}
					if (d_batchConsumeRequirementIds.Any())
					{
						await _dal7.DeleteByIds(d_batchConsumeRequirementIds.ToArray());
					}
					if (d_batchProducedRequirementIds.Any())
					{
						await _dal8.DeleteByIds(d_batchProducedRequirementIds.ToArray());
					}
					if (d_poEquipmentIds.Any())
					{
						await _dalpoeq.DeleteByIds(d_poEquipmentIds.ToArray());
					}
					_unitOfWork.CommitTran();
				}
				catch (Exception ex)
				{
					_unitOfWork.RollbackTran();
					result.msg = "操作失败！";
					result.msgDev = $"提交数据库时出现异常：{ex.Message}";
					result.success = false;
					return result;
				}
				SerilogServer.LogDebug($"删除批次等信息完成", "RebuildBatchLog");
				//重新创建工单工序、批次等信息
				var r = await CreateBatch(orderIds);
				result.msgDev += "CreateBatch:" + r.msgDev;
				if (!r.success)
				{
					result.msg = "操作失败！";
					result.success = false;
					return result;
				}
			}
			catch (Exception ex)
			{
				result.msg = "操作失败！";
				result.msgDev += "err:" + ex.Message;
				result.success = false;
				return result;
			}
			SerilogServer.LogDebug($"构建批次完成", "RebuildBatchLog");
			return result;

		}

		/// <summary>
		/// 重新生成批次（计划用）
		/// </summary>
		/// <param name="productionOrderIds"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<dynamic>>> RebuildBatch2(List<string> productionOrderIds)
		{
			SerilogServer.LogDebug($"开始构建批次", "RebuildBatch2Log");
			var result = new MessageModel<List<dynamic>>
			{
				msg = "操作成功！",
				success = true
			};
			List<dynamic> list = new List<dynamic>();
			List<dynamic> list2 = new List<dynamic>();
			result.response = list;
			if (!productionOrderIds.Any())
			{
				result.msg = "至少选择一条数据";
				result.success = false;
				return result;
			}
			var productionOrders = await _dal2.FindList(x => productionOrderIds.Contains(x.ID));
			var batchs = await _dal3.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poSegmentRequirements = await _dal4.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poConsumeRequirements = await _dal5.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poProducedRequirements = await _dal6.FindList(x => productionOrderIds.Contains(x.ProductionOrderId));
			var poEquipments = await _dalpoeq.FindList(x => productionOrderIds.Contains(x.OrderId));
			SerilogServer.LogDebug($"数据加载完成1", "RebuildBatch2Log");
			var allbatchConsumeRequirements = await _dal7.Query();
			SerilogServer.LogDebug($"数据加载完成2", "RebuildBatch2Log");
			var allbatchProducedRequirements = await _dal8.Query();
			SerilogServer.LogDebug($"数据加载完成3", "RebuildBatch2Log");

			var orderIds = new List<string>();
			var d_batchIds = new List<string>();
			var d_poSegmentRequirementIds = new List<string>();
			var d_poConsumeRequirementIds = new List<string>();
			var d_poProducedRequirementIds = new List<string>();
			var d_poEquipmentIds = new List<string>();
			var d_batchConsumeRequirementIds = new List<string>();
			var d_batchProducedRequirementIds = new List<string>();

			SerilogServer.LogDebug($"数据加载完成", "RebuildBatch2Log");

			foreach (var productionOrderId in productionOrderIds)
			{

				var productionOrder = productionOrders?.Find(x => x.ID == productionOrderId);
				if (productionOrder == null)
				{
					result.msg = $"未找到productionOrderId{productionOrderId}";
					list.Add(new { ProductionOrderId = productionOrderId, ProductionOrderNo = "", Msg = "未找到productionOrderId" });
					result.success = false;
					continue;
				}
				SerilogServer.LogDebug($"处理工单[{productionOrder.ProductionOrderNo}]", "RebuildBatch2Log");

				list2.Add(new { ProductionOrderId = productionOrderId, ProductionOrderNo = productionOrder.ProductionOrderNo, Msg = "" });
				if (productionOrder.PoStatus != "1" && productionOrder.PoStatus != "2")
				{
					result.msgDev += $"构建失败，该工单{productionOrder.ProductionOrderNo}当前状态不允许重新解析构建批;";
					list.Add(new { ProductionOrderId = productionOrderId, ProductionOrderNo = "", Msg = "构建失败，工单当前状态不允许重新解析构建批" });
					result.success = false;
					continue;
				}
				var batchs2 = batchs.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (batchs2.Any())
				{
					if (batchs2.Exists(x => x.PrepStatus != "1" && x.PrepStatus != "2"))
					{
						result.msgDev += $"构建失败，该工单{productionOrder.ProductionOrderNo}已开始备料;";
						list.Add(new { ProductionOrderId = productionOrderId, ProductionOrderNo = "", Msg = "构建失败，工单已开始备料" });
						result.success = false;
						continue;
					}

					var batchIds = batchs2.Select(x => x.ID);
					d_batchIds.AddRange(batchIds);
					var batchConsumeRequirements = allbatchConsumeRequirements.FindAll(x => batchIds.Contains(x.BatchId));
					if (batchConsumeRequirements.Any())
					{
						d_batchConsumeRequirementIds.AddRange(batchConsumeRequirements.Select(x => x.ID));
					}

					var batchProducedRequirements = allbatchProducedRequirements.FindAll(x => batchIds.Contains(x.BatchId));
					if (batchProducedRequirements.Any())
					{
						d_batchProducedRequirementIds.AddRange(batchProducedRequirements.Select(x => x.ID));
					}
				}

				orderIds.Add(productionOrderId);

				var poSegmentRequirements2 = poSegmentRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poSegmentRequirements2.Any())
				{
					var poSegmentRequirementIds = poSegmentRequirements2.Select(x => x.ID);
					d_poSegmentRequirementIds.AddRange(poSegmentRequirementIds);
				}
				var poConsumeRequirements2 = poConsumeRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poConsumeRequirements2.Any())
				{
					var poConsumeRequirementIds = poConsumeRequirements2.Select(x => x.ID);
					d_poConsumeRequirementIds.AddRange(poConsumeRequirementIds);
				}
				var poProducedRequirements2 = poProducedRequirements.FindAll(x => x.ProductionOrderId == productionOrderId);
				if (poProducedRequirements2.Any())
				{
					var poProducedRequirementIds = poProducedRequirements2.Select(x => x.ID);
					d_poProducedRequirementIds.AddRange(poProducedRequirementIds);
				}
				var poEquipments2 = poEquipments.FindAll(x => x.OrderId == productionOrderId);
				if (poEquipments2.Any())
				{
					var poEquipmentIds = poEquipments2.Select(x => x.ID);
					d_poEquipmentIds.AddRange(poEquipmentIds);
				}
			}


			SerilogServer.LogDebug($"处理工单结束", "RebuildBatch2Log");

			try
			{
				SerilogServer.LogDebug($"开始删除批次等信息", "RebuildBatch2Log");
				_unitOfWork.BeginTran();
				try
				{
					if (d_batchIds.Any())
					{
						await _dal3.DeleteByIds(d_batchIds.ToArray());
					}
					if (d_poSegmentRequirementIds.Any())
					{
						await _dal4.DeleteByIds(d_poSegmentRequirementIds.ToArray());
					}
					if (d_poConsumeRequirementIds.Any())
					{
						await _dal5.DeleteByIds(d_poConsumeRequirementIds.ToArray());
					}
					if (d_poProducedRequirementIds.Any())
					{
						await _dal6.DeleteByIds(d_poProducedRequirementIds.ToArray());
					}
					if (d_batchConsumeRequirementIds.Any())
					{
						await _dal7.DeleteByIds(d_batchConsumeRequirementIds.ToArray());
					}
					if (d_batchProducedRequirementIds.Any())
					{
						await _dal8.DeleteByIds(d_batchProducedRequirementIds.ToArray());
					}
					if (d_poEquipmentIds.Any())
					{
						await _dalpoeq.DeleteByIds(d_poEquipmentIds.ToArray());
					}
					_unitOfWork.CommitTran();
				}
				catch (Exception ex)
				{
					SerilogServer.LogDebug($"异常：" + ex.ToString(), "RebuildBatch2Log");
					_unitOfWork.RollbackTran();
					//list2.ForEach(x => x.Msg = "提交数据库时出现异常");
					result.response = list2;
					result.msg = "操作失败！";
					result.msgDev = $"提交数据库时出现异常：{ex.Message}";
					result.success = false;
					return result;
				}
				SerilogServer.LogDebug($"删除批次等信息完成", "RebuildBatch2Log");
				//重新创建工单工序、批次等信息
				SerilogServer.LogDebug($"CreateBatch2 start", "RebuildBatch2Log");
				var r = await CreateBatch(orderIds);
				SerilogServer.LogDebug($"CreateBatch2 end", "RebuildBatch2Log");
				if (r.response.Count > 0)
				{
					list.AddRange(r.response);
				}
				if (!r.success)
				{
					result.msg = "操作失败！";
					result.msgDev += "CreateBatch:" + r.msgDev;
					result.success = false;
					return result;
				}
			}
			catch (Exception ex)
			{
				//list2.ForEach(x => x.Msg = ex.Message);
				result.response = list2;
				result.msg = "操作失败！";
				result.msgDev = ex.Message;
				result.success = false;
				return result;
			}
			SerilogServer.LogDebug($"构建批次完成", "RebuildBatchLog");
			return result;

		}

		/// <summary>
		/// 绑定配方
		/// </summary>
		/// <param name="productionOrderIds"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> BindPoRecipe(List<string> productionOrderIds)
		{
			var result = new MessageModel<string>
			{
				msg = "操作成功！",
				success = true
			};
			if (!productionOrderIds.Any())
			{
				result.msg = "至少选择一条数据";
				result.success = false;
				return result;
			}
			var productionOrders = await _dal2.FindList(x => productionOrderIds.Contains(x.ID));
			var poEquipments = await _dalpoeq.FindList(x => productionOrderIds.Contains(x.OrderId));
			foreach (var productionOrderId in productionOrderIds)
			{
				var poEntity = productionOrders.Find(x => x.ID == productionOrderId);
				if (poEntity != null)
				{
					var poEquipments_o = poEquipments.FindAll(x => x.OrderId == productionOrderId);
					if (poEquipments_o?.Count > 0)
					{
						MessageModel<string> apiResult = await HttpHelper.PostAsync<string>("DFM", "api/RecipeCommon/BindPoRecipe", _user.GetToken(), new { ProductionOrderId = productionOrderId, MaterialVersionId = poEntity.MaterialVersionId, ProductionTime = poEntity.PlanStartTime, EquipmentIdList = poEquipments_o.Select(x => x.EquipmentId) });
						SerilogServer.LogDebug($"[{poEntity.ProductionOrderNo}]绑定配方数据完成,return:{FAJsonConvert.ToJson(apiResult)}", "BindPoRecipeLog");
					}
				}
			}
			return result;
		}

		/// <summary>
		/// 获取物料属性
		/// </summary>
		/// <param name="materialId"></param>
		/// <param name="propertyCode"></param>
		/// <returns></returns>
		public async Task<string> GetMaterialPropertyValue(string materialId, string propertyCode)
		{
			var data = await _dal10.FindEntity(x => x.MaterialId == materialId && x.PropertyCode == propertyCode);
			return data?.PropertyValue;
		}

		/// <summary>
		/// 获取工单配方参数
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<ParameterDownloadModel>> GetPoRecipeData(string productionOrderId)
		{
			var result = new MessageModel<ParameterDownloadModel>
			{
				msg = "操作失败！",
				success = true
			};
			var parameterDownload = new ParameterDownloadModel();
			MessageModel<PoRecipeDataModel> apiResult_poRecipeDataModel = await HttpHelper.PostAsync<PoRecipeDataModel>("DFM", "api/RecipeCommon/GetPoRecipeDataNew", _user.GetToken(), new { ProductionOrderId = productionOrderId });
			if (apiResult_poRecipeDataModel.response == null || apiResult_poRecipeDataModel.success != true)
			{
				result.msg = apiResult_poRecipeDataModel.msg;
				return result;
			}
			var data = apiResult_poRecipeDataModel.response;
			parameterDownload.ParameterNameList = new List<string>();
			parameterDownload.StepList = new List<Step>();
			parameterDownload.ControlRecipeList = new List<ControlRecipe>();
			foreach (var item in data.RecipeTableList)
			{
				if (!parameterDownload.ParameterNameList.Contains(item.ParameterName))
				{
					parameterDownload.ParameterNameList.Add(item.ParameterName);
				}
				var step = parameterDownload.StepList.Find(x => x.Name == item.ParameterGroupName);
				if (step == null)
				{
					step = new Step()
					{
						Name = item.ParameterGroupName,
						Parameters = new List<Parameter>(),
					};
					parameterDownload.StepList.Add(step);
				}
				if (string.IsNullOrEmpty(step.EquipmentNames) || !step.EquipmentNames.Split("、").Contains(item.EquipmentName))
				{
					if (string.IsNullOrEmpty(step.EquipmentNames))
					{
						step.EquipmentNames += item.EquipmentName;
					}
					else
					{
						step.EquipmentNames += "、" + item.EquipmentName;
					}
				}
				step.Parameters.Add(
					new Parameter()
					{
						Name = item.ParameterName,
						Value = item.Target.ToString(),
						Uom = item.Uom
					});
			}
			foreach (var item in data.ControlRecipeList)
			{
				var controlRecipe = parameterDownload.ControlRecipeList.Find(x => x.GrouopName == item.ParameterGroupName && x.EquipmentName == item.EquipmentName);
				if (controlRecipe == null)
				{
					controlRecipe = new ControlRecipe()
					{
						GrouopName = item.ParameterGroupName,
						EquipmentName = item.EquipmentName,
						Parameters = new List<Parameter>(),
					};
					parameterDownload.ControlRecipeList.Add(controlRecipe);
				}
				controlRecipe.Parameters.Add(
					new Parameter()
					{
						Name = item.ParameterName,
						Value = item.Target.ToString(),
						Uom = item.Uom
					});
			}
			//parameterDownload.ParameterNameList = new List<string> { "用水量", "温度" };
			//parameterDownload.StepList = new List<Step>()
			//{
			//	new Step()
			//	{
			//		Name = "Step1",
			//		Parameters = new List<Parameter>()
			//		{
			//			new Parameter()
			//			{
			//				Name = "用水量",
			//				Value = "0.8"
			//			},
			//			new Parameter()
			//			{
			//				Name = "温度",
			//				Value = "0.8"
			//			}
			//		},
			//		EquipmentNames = "设备1、设备2"
			//	},
			//	new Step()
			//	{
			//		Name = "Step2",
			//		Parameters = new List<Parameter>()
			//		{
			//			new Parameter()
			//			{
			//				Name = "用水量",
			//				Value = "0.8"
			//			},
			//			new Parameter()
			//			{
			//				Name = "温度",
			//				Value = "0.8"
			//			}
			//		},
			//		EquipmentNames = "设备1、设备2"
			//	}
			//};
			//parameterDownload.ControlRecipeList = new List<ControlRecipe>()
			//{
			//	new ControlRecipe()
			//	{
			//		GrouopName = "Group1",
			//		EquipmentName = "设备1",
			//		Parameters = new List<Parameter>()
			//		{
			//			new Parameter()
			//			{
			//				Name = "参数1",
			//				Value = "0.9"
			//			},
			//			new Parameter()
			//			{
			//				Name = "参数2",
			//				Value = "5.6"
			//			}
			//		}
			//	},
			//	new ControlRecipe()
			//	{
			//		GrouopName = "Group2",
			//		EquipmentName = "设备1",
			//		Parameters = new List<Parameter>()
			//		{
			//			new Parameter()
			//			{
			//				Name = "参数1",
			//				Value = "0.9"
			//			},
			//			new Parameter()
			//			{
			//				Name = "参数2",
			//				Value = "5.6"
			//			}
			//		}
			//	}
			//};
			result.success = true;
			result.response = parameterDownload;
			result.msg = "操作成功！";
			return result;
		}

		public class CreateBatchRequestMode
		{
			public string ProductionOrderId { get; set; }

			public string SegmentId { get; set; }

			public int BatchCount { get; set; }

			public decimal Quantity { get; set; }

		}

		#region 煮制工单喉头产出

		/// <summary>
		/// 获取设备下拉选
		/// </summary>
		/// <returns></returns>
		public async Task<List<Select>> GetEquipmentsSelect()
		{
			return (await _equdal.FindList(x => x.Level == "Unit" || x.Level == "Storage")).Select(x => new Select() { key = x.ID, value = x.EquipmentCode }).OrderBy(x => x.value).ToList();
		}

		/// <summary>
		/// ProduceOpen
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<ProduceOpenModel>> ProduceOpen(StartPoRequestModel reqModel)
		{
			var result = new MessageModel<ProduceOpenModel>()
			{
				success = false,
				msg = "操作失败！"
			};
			var poExecutions = await _dal.Db.Queryable<PoExecutionHistroyViewEntity>().Where(x => x.RunEquipmentId == reqModel.EquipmentId && ((x.StartTime <= reqModel.ProductionDate && x.EndTime >= reqModel.ProductionDate) || (x.EndTime == null && x.StartTime <= reqModel.ProductionDate))).ToListAsync();
			if (poExecutions == null || poExecutions.Count == 0)
			{
				result.msg = "该时间点未找到相关的工单执行记录！";
				return result;
			}
			else if (poExecutions.Count > 1)
			{
				result.msg = $"该时间点找到多条({poExecutions.Count})工单执行记录！";
				return result;
			}
			var poExecution = poExecutions.FirstOrDefault();
			var poProducedExecution = (await _dal.Db.Queryable<PoProducedExecutionEntity>().Where(x => x.ID == poExecution.ID).ToListAsync()).FirstOrDefault();
			var material = (await _dal.Db.Queryable<MaterialEntity>().Where(x => x.ID == poExecution.MaterialId).ToListAsync()).FirstOrDefault();
			if (material == null)
			{
				result.msg = $"未找到物料！";
				return result;
			}
			var produceOpenModel = _mapper.Map<ProduceOpenModel>(poExecution);
			produceOpenModel.ExecutionId = produceOpenModel.ID;
			produceOpenModel.EquipmentId = produceOpenModel.RunEquipmentId;
			produceOpenModel.ProductionOrderNo = produceOpenModel.ProcessOrder;
			produceOpenModel.Source = produceOpenModel.EquipmentName;
			produceOpenModel.MaterialDescription = produceOpenModel.MaterialName;
			produceOpenModel.BatchId = poProducedExecution?.BatchId;
			produceOpenModel.Mhdhb = material.Mhdhb;
			produceOpenModel.Iprkz = material.Iprkz;
			produceOpenModel.ProductionDate = reqModel.ProductionDate;
			//获取有效期
			var expirationDate = _materInverntoryServices.GetExpirationDate(material.Mhdhb.Value, material.Iprkz, reqModel.ProductionDate.Value);
			produceOpenModel.ExpirationDate = expirationDate;
			result.response = produceOpenModel;
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#region 通过包装工单产出推算报工数据

		public async Task<MessageModel<List<OrderTreeModel>>> PackReport1(List<string> productionOrderIds)
		{
			var result = new MessageModel<List<OrderTreeModel>>
			{
				msg = "操作失败！",
				success = false
			};

			try
			{
				// 获取包装工单数据
				var packOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
					.LeftJoin<PoProducedActualEntity>((po, pa) => po.ID == pa.ProductionOrderId)
					.LeftJoin<MaterialLotEntity>((po, pa, ml) => pa.LotId == ml.ID)
					.LeftJoin<MaterialEntity>((po, pa, ml, m) => ml.MaterialId == m.ID)
					.LeftJoin<UnitmanageEntity>((po, pa, ml, m, um) => pa.UnitId == um.ID)
					.LeftJoin<SappackorderEntity>((po, pa, ml, m, um, sp) => po.ProductionOrderNo == sp.Aufnr)
					.Where(po => po.PoStatus != "4" && productionOrderIds.Contains(po.ID))
					.GroupBy((po, pa, ml, m, um, sp) => new
					{
						po.ID,
						po.ProductionOrderNo,
						po.Sequence,
						PlanStartDate = po.PlanStartTime.Value.Date,
						po.PlanDate,
						po.FormulaMaterialId,
						ml.MaterialId,
						m.Code,
						m.NAME,
						LotId = ml.ID,
						LotCode = ml.LotId,
						sp.AuartFill,
						sp.MngPu,
						po.PlanQty
					})
					.Select((po, pa, ml, m, um, sp) => new OrderTreeModel
					{
						ProductionOrderId = po.ID,
						ProductionOrderNo = po.ProductionOrderNo,
						PlanStartDate = po.PlanStartTime.Value.Date,
						PlanDate = po.PlanDate,
						FormulaMaterialId = po.FormulaMaterialId,
						OrderType = "PACK",
						MaterialId = ml.MaterialId,
						MaterialCode = m.Code,
						MaterialName = m.NAME,
						LotId = ml.ID,
						LotCode = ml.LotId,
						AuartFill = sp.AuartFill,
						MngPu = sp.MngPu,
						Sequence = po.Sequence,
						PlanQty = po.PlanQty,
						Q1 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity, 0)),
						Q2 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q3 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q4 = 0,
						Q5 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q6 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						ChildrenList = new List<OrderTreeModel>()
					})
					.ToListAsync();
				packOrders = packOrders.Where(x => x.Q3 > 0).ToList();
				if (packOrders?.Count > 0)
				{
					if (packOrders.Exists(x => string.IsNullOrEmpty(x.AuartFill)) && packOrders.Exists(x => !string.IsNullOrEmpty(x.AuartFill)))
					{
						result.msg = "请选择包装工单或灌包装工单中的一种";
						return result;
					}

					//酱料总消耗
					decimal jlzxh = 0;

					//制造工单计划总产出
					decimal cookProduceSum = 0;

					//不为空则是包装工单
					var isPacke = !string.IsNullOrEmpty(packOrders.FirstOrDefault().AuartFill.Trim());

					var packFillaverageWeightDict = new Dictionary<string, decimal>();

					List<OrderTreeModel> fillOrders = new List<OrderTreeModel>();

					var averageWeightDict = new Dictionary<string, decimal>();

					var materialSummariesDict = new Dictionary<string, decimal>();

					var parentOrders = new List<OrderTreeModel>();

					//灌包一体
					if (!isPacke)
					{
						// 获取 BWeightRecordsEntity 数据
						var packFillweightRecords = await _dal.Db.Queryable<BWeightRecordsEntity>()
						.Where(x => productionOrderIds.Contains(x.OrderId) && x.AverageWeight > 0)
						.ToListAsync();

						// 计算灌包装工单平均重量
						packFillaverageWeightDict = packFillweightRecords
						   .GroupBy(x => x.OrderId)
						   .ToDictionary(
							   x => x.Key,
							   x => x.Average(y => y.AverageWeight)
						   );
					}
					else
					{
						var fillOrderNos = packOrders.Where(x => !string.IsNullOrEmpty(x.AuartFill)).Select(x => x.AuartFill).Distinct().ToList();

						// 获取 BWeightRecordsEntity 数据
						var weightRecords = await _dal.Db.Queryable<BWeightRecordsEntity>()
							.Where(x => fillOrderNos.Contains(x.ProductionOrderNo) && x.AverageWeight > 0)
							.ToListAsync();

						// 计算平均重量
						averageWeightDict = weightRecords
						   .GroupBy(x => x.OrderId)
						   .ToDictionary(
							   x => x.Key,
							   x => x.Average(y => y.AverageWeight)
						   );

						// 获取 灌装工单 数据
						var fillProductionOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
							.Where(x => x.PoStatus != "4" && fillOrderNos.Contains(x.ProductionOrderNo))
							.ToListAsync();

						// 获取分组求和数据
						materialSummariesDict = fillProductionOrders
						   .GroupBy(x => x.MaterialId)
						   .ToDictionary(
							   x => x.Key,
							   x => x.Sum(y => y.PlanQty)
						   );

						// 获取主查询数据
						fillOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
						   .LeftJoin<MaterialEntity>((po, m) => po.MaterialId == m.ID)
						   .Where(po => po.PoStatus != "4" && fillOrderNos.Contains(po.ProductionOrderNo))
						   .Select((po, m) => new OrderTreeModel
						   {
							   ProductionOrderId = po.ID,
							   ProductionOrderNo = po.ProductionOrderNo,
							   PlanStartDate = po.PlanStartTime.Value.Date,
							   PlanDate = po.PlanDate,
							   FormulaMaterialId = po.FormulaMaterialId,
							   MaterialId = po.MaterialId,
							   MaterialCode = m.Code,
							   MaterialName = m.NAME,
							   Sequence = po.Sequence,
							   PlanQty = po.PlanQty,
						   })
						   .ToListAsync();
					}

					// 构建最终结果
					foreach (var packOrder in packOrders)
					{
						//灌包一体
						if (!isPacke)
						{
							packOrder.AVG = packFillaverageWeightDict.GetValueOrDefault(packOrder.ProductionOrderId, 1);
							packOrder.Q7 = packOrder.Q6 * packOrder.AVG;
							parentOrders.Add(packOrder);
							jlzxh += packOrder.Q7.Value;
						}
						else
						{
							var fillOrdersForPackOrder = fillOrders.Where(x => x.ProductionOrderNo == packOrder.AuartFill).ToList();
							if (fillOrdersForPackOrder?.Count > 0)
							{
								packOrder.ChildrenList = fillOrdersForPackOrder.Select(x => new OrderTreeModel
								{
									ProductionOrderId = x.ProductionOrderId,
									ProductionOrderNo = x.ProductionOrderNo,
									PlanStartDate = x.PlanStartDate,
									PlanDate = x.PlanDate,
									FormulaMaterialId = x.FormulaMaterialId,
									OrderType = "FILL",
									MaterialId = x.MaterialId,
									MaterialCode = x.MaterialCode,
									LotId = packOrder.LotId,
									LotCode = packOrder.LotCode,
									MaterialName = x.MaterialName,
									Sequence = x.Sequence,
									AVG = averageWeightDict.GetValueOrDefault(x.ProductionOrderId, 1),
									//Q1 = x.PlanQty / materialSummariesDict.GetValueOrDefault(x.MaterialId, 1) * packOrder.Q6,
									//Q2 = x.PlanQty / materialSummariesDict.GetValueOrDefault(x.MaterialId, 1) * packOrder.Q6 * averageWeightDict.GetValueOrDefault(x.ProductionOrderId, 300),
									Q1 = packOrder.Q6,
									Q2 = packOrder.Q6 * averageWeightDict.GetValueOrDefault(x.ProductionOrderId, 1),
									ChildrenList = new List<OrderTreeModel>()
								}).ToList();
								parentOrders.AddRange(packOrder.ChildrenList);
							}
							jlzxh += packOrder.ChildrenList.Sum(x => x.Q2.Value);
						}
					}

					// 获取煮制工单及其最新 LOTCode
					var cookOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
						.LeftJoin<MaterialEntity>((po, m) => po.MaterialId == m.ID)
						.LeftJoin<PoProducedExecutionEntity>((po, m, pe) => po.ID == pe.ProductionOrderId)
						.LeftJoin<MaterialLotEntity>((po, m, pe, ml) => pe.LotId == ml.ID) // 关联 Lot 表
						.Where(po =>
							po.PoStatus != "4" &&
							po.Type == "WorkOrder" &&
							po.SapOrderType == "ZXH2" &&
							parentOrders.Any(x2 =>
								po.PlanDate == x2.PlanDate &&
								po.FormulaMaterialId == x2.FormulaMaterialId
							))
						.GroupBy((po, m, pe, ml) => new
						{
							po.ID,
							po.ProductionOrderNo,
							PlanStartDate = po.PlanStartTime.Value.Date,
							po.PlanDate,
							po.FormulaMaterialId,
							po.MaterialId,
							m.Code,
							m.NAME,
							po.Sequence,
							po.PlanQty,
						})
						.Select((po, m, pe, ml) => new
						{
							po.ID,
							po.ProductionOrderNo,
							PlanStartDate = po.PlanStartTime.Value.Date,
							po.PlanDate,
							po.FormulaMaterialId,
							po.MaterialId,
							m.Code,
							m.NAME,
							po.Sequence,
							po.PlanQty
						})
						.OrderBy(po => po.PlanDate)
						.OrderBy(po => po.Sequence)
						.ToListAsync();

					//煮制工单LotCodes
					var cookOrderlotCodes = await _dal.Db.Queryable<PoProducedExecutionEntity>()
						.LeftJoin<MaterialLotEntity>((pe, ml) => pe.LotId == ml.ID)
						.Where(pe => cookOrders.Any(x => x.ID == pe.ProductionOrderId))
						.Select((pe, ml) => new
						{
							pe.ID,
							pe.ProductionOrderId,
							pe.StartTime,
							pe.EndTime,
							pe.CreateDate,
							pe.ModifyDate,
							pe.LotId,
							LotCode = ml.LotId
						})
						.OrderByDescending(pe => pe.CreateDate)
						.ToListAsync();

					cookProduceSum = cookOrders.Sum(x => x.PlanQty);

					// 将结果转换为 OrderTreeModel
					var finalcookOrders = cookOrders.Select(cookOrder => new OrderTreeModel
					{
						ProductionOrderId = cookOrder.ID,
						OrderType = "COOK",
						ProductionOrderNo = cookOrder.ProductionOrderNo,
						PlanStartDate = cookOrder.PlanStartDate,
						PlanDate = cookOrder.PlanDate,
						FormulaMaterialId = cookOrder.FormulaMaterialId,
						MaterialId = cookOrder.MaterialId,
						MaterialCode = cookOrder.Code,
						MaterialName = cookOrder.NAME,
						Sequence = cookOrder.Sequence,
						PlanQty = cookOrder.PlanQty,
						LotId = cookOrderlotCodes.FirstOrDefault(x => x.ProductionOrderId == cookOrder.ID)?.LotId ?? "",
						LotCode = cookOrderlotCodes.FirstOrDefault(x => x.ProductionOrderId == cookOrder.ID)?.LotCode ?? "",
						Q6 = Math.Round(cookOrder.PlanQty / cookProduceSum * jlzxh, 3),
						Q7 = Math.Round(cookOrder.PlanQty / cookProduceSum * jlzxh, 3)
					}).ToList();

					foreach (var packOrder in packOrders)
					{
						if (!isPacke)
						{
							packOrder.ChildrenList = new List<OrderTreeModel>();
							// 处理灌包一体的情况
							ProcessPackOrder(packOrder, packOrder.Q7, finalcookOrders);
						}
						else
						{
							// 处理灌包分离的情况
							foreach (var fillOrder in packOrder.ChildrenList)
							{
								fillOrder.ChildrenList = new List<OrderTreeModel>();
								ProcessPackOrder(fillOrder, fillOrder.Q2, finalcookOrders);
							}
						}
					}
				}
				result.response = packOrders;
				result.success = true;
				result.msg = "操作成功！";
			}
			catch (Exception ex)
			{
				result.msg = $"操作失败！{ex.Message}";
			}

			return result;
		}

		/// <summary>
		/// 处理包装订单，匹配并添加子订单
		/// </summary>
		/// <param name="packOrder">包装订单</param>
		/// <param name="q1">需要匹配的数量</param>
		/// <param name="finalcookOrders">最终的烹饪订单列表</param>
		void ProcessPackOrder(OrderTreeModel packOrder, decimal? q1, List<OrderTreeModel> finalcookOrders)
		{
			decimal? q = q1;

			// 根据计划日期和配方材料ID筛选出相关的烹饪订单
			//var orderTreeModels = finalcookOrders.Where(x => x.PlanDate == packOrder.PlanDate && x.FormulaMaterialId == packOrder.FormulaMaterialId).ToList();
			var orderTreeModels = finalcookOrders;

			var removeOrders = new List<OrderTreeModel>();
			foreach (var cookOrder in orderTreeModels)
			{
				decimal? q2 = 0;
				if (cookOrder.Q7 >= q)
				{
					// 如果当前烹饪订单的数量足够，则减少当前烹饪订单的数量，并将q设为0
					q2 = q;
					cookOrder.Q7 -= q2;
					q = 0;
				}
				else
				{
					// 如果当前烹饪订单的数量不够，则减少q，并移除当前烹饪订单
					q2 = cookOrder.Q7;
					q -= q2;
					removeOrders.Add(cookOrder);
				}

				// 将匹配到的烹饪订单添加到包装订单的子订单列表中
				packOrder.ChildrenList.Add(new OrderTreeModel()
				{
					ProductionOrderId = cookOrder.ProductionOrderId,
					OrderType = "COOK",
					ProductionOrderNo = cookOrder.ProductionOrderNo,
					PlanStartDate = cookOrder.PlanStartDate,
					PlanDate = cookOrder.PlanDate,
					FormulaMaterialId = cookOrder.FormulaMaterialId,
					MaterialId = cookOrder.MaterialId,
					MaterialCode = cookOrder.MaterialCode,
					MaterialName = cookOrder.MaterialName,
					Sequence = cookOrder.Sequence,
					PlanQty = cookOrder.PlanQty,
					LotId = cookOrder.LotId,
					LotCode = cookOrder.LotCode,
					Q1 = q2,
					Q6 = cookOrder.Q6,
					Q7 = cookOrder.Q7,
				});

				// 如果q已经减少到0，提前退出循环
				if (q == 0) break;
			}

			if (removeOrders.Count > 0)
			{
				foreach (var item in removeOrders)
				{
					finalcookOrders.Remove(item);
				}
			}
		}

		public async Task<MessageModel<List<OrderTreeModel>>> PackReport(List<string> productionOrderIds)
		{
			var result = new MessageModel<List<OrderTreeModel>>
			{
				msg = "操作失败！",
				success = false
			};

			try
			{
				if (productionOrderIds == null || !productionOrderIds.Any())
				{
					result.msg = "生产订单ID列表不能为空！";
					return result;
				}

				// 获取包装工单数据
				var packOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
					.LeftJoin<PoProducedActualEntity>((po, pa) => po.ID == pa.ProductionOrderId)
					.LeftJoin<MaterialLotEntity>((po, pa, ml) => pa.LotId == ml.ID)
					.LeftJoin<MaterialEntity>((po, pa, ml, m) => ml.MaterialId == m.ID)
					.LeftJoin<UnitmanageEntity>((po, pa, ml, m, um) => pa.UnitId == um.ID)
					.LeftJoin<SappackorderEntity>((po, pa, ml, m, um, sp) => po.ProductionOrderNo == sp.Aufnr)
					.Where(po => po.PoStatus != "4" && productionOrderIds.Contains(po.ID))
					.GroupBy((po, pa, ml, m, um, sp) => new
					{
						po.ID,
						po.ProductionOrderNo,
						po.Sequence,
						PlanStartDate = po.PlanStartTime.Value.Date,
						po.PlanDate,
						po.FormulaMaterialId,
						ml.MaterialId,
						m.Code,
						m.NAME,
						LotId = ml.ID,
						LotCode = ml.LotId,
						sp.AuartFill,
						sp.MngPu,
						po.PlanQty,
						sp.Arbpl,
						sp.MatnrComp
					})
					.Select((po, pa, ml, m, um, sp) => new OrderTreeModel
					{
						ProductionOrderId = po.ID,
						ProductionOrderNo = po.ProductionOrderNo,
						PlanStartDate = po.PlanStartTime.Value.Date,
						PlanDate = po.PlanDate,
						FormulaMaterialId = po.FormulaMaterialId,
						OrderType = "PACK",
						MaterialId = ml.MaterialId,
						MaterialCode = m.Code,
						MaterialName = m.NAME,
						LotId = ml.ID,
						LotCode = ml.LotId,
						AuartFill = sp.AuartFill == null ? null : sp.AuartFill.Trim(),
						MngPu = sp.MngPu,
						Sequence = po.Sequence,
						PlanQty = po.PlanQty,
						isPackFill = sp.Arbpl != null && sp.Arbpl.Contains("FIL") ? true : false,
						MatnrComp = sp.MatnrComp,
						Q1 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity, 0)),
						Q2 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q3 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q4 = 0,
						Q5 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						Q6 = SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "TR" || um.Name == "CAR", pa.Quantity * sp.MngPu, 0)) + SqlFunc.AggregateSum(SqlFunc.IIF(um.Name == "EA", pa.Quantity, 0)),
						ChildrenList = new List<OrderTreeModel>()
					})
					.ToListAsync();


				if (packOrders == null || !packOrders.Any())
				{
					result.msg = "未找到相关包装工单数据！";
					return result;
				}
				if (packOrders.Exists(x => x.FormulaMaterialId != packOrders.FirstOrDefault().FormulaMaterialId))
				{
					result.msg = "请选择相同配方的工单！";
					return result;
				}

				packOrders = packOrders.Where(x => x.Q3 > 0).ToList();
				if (packOrders?.Count > 0)
				{
					if (packOrders.Exists(x => x.isPackFill) && packOrders.Exists(x => !x.isPackFill))
					{
						result.msg = "请选择包装工单或灌包装工单中的一种";
						return result;
					}

					//酱料总消耗
					decimal jlzxh = 0;

					//制造工单计划总产出
					decimal cookProduceSum = 0;

					//是否灌包一体
					var isPacke = packOrders.FirstOrDefault().isPackFill;

					var packFillaverageWeightDict = new Dictionary<string, decimal>();

					List<OrderTreeModel> fillOrders = new List<OrderTreeModel>();

					List<OrderTreeModel> fillOrderlotCodes = new List<OrderTreeModel>();

					var averageWeightDict = new Dictionary<string, decimal>();

					var materialSummariesDict = new Dictionary<string, decimal>();

					var parentOrders = new List<OrderTreeModel>();

					//灌包一体
					if (isPacke)
					{
						// 获取 BWeightRecordsEntity 数据
						var packFillweightRecords = await _dal.Db.Queryable<BWeightRecordsEntity>()
							.Where(x => productionOrderIds.Contains(x.OrderId) && x.AverageWeight > 0)
							.ToListAsync();

						if (packFillweightRecords == null)
						{
							packFillweightRecords = new List<BWeightRecordsEntity>();
						}

						// 计算灌包装工单平均重量
						packFillaverageWeightDict = packFillweightRecords
							.GroupBy(x => x.OrderId)
							.ToDictionary(
								x => x.Key,
								x => x.Average(y => y.AverageWeight) / 1000
							);
					}
					else
					{
						var fillOrderNos = packOrders.Where(x => !string.IsNullOrEmpty(x.AuartFill)).Select(x => x.AuartFill).Distinct().ToList();

						if (fillOrderNos == null || !fillOrderNos.Any())
						{
							fillOrderNos = new List<string>();
						}

						// 获取 BWeightRecordsEntity 数据
						var weightRecords = await _dal.Db.Queryable<BWeightRecordsEntity>()
							.Where(x => fillOrderNos.Contains(x.ProductionOrderNo) && x.AverageWeight > 0)
							.ToListAsync();

						if (weightRecords == null)
						{
							weightRecords = new List<BWeightRecordsEntity>();
						}

						// 计算平均重量
						averageWeightDict = weightRecords
							.GroupBy(x => x.OrderId)
							.ToDictionary(
								x => x.Key,
								x => x.Average(y => y.AverageWeight) / 1000
							);

						// 获取 灌装工单 数据
						var fillProductionOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
							.Where(x => x.PoStatus != "4" && fillOrderNos.Contains(x.ProductionOrderNo))
							.ToListAsync();

						if (fillProductionOrders == null)
						{
							fillProductionOrders = new List<ProductionOrderEntity>();
						}

						// 获取分组求和数据
						materialSummariesDict = fillProductionOrders
							.GroupBy(x => x.MaterialId)
							.ToDictionary(
								x => x.Key,
								x => x.Sum(y => y.PlanQty)
							);

						// 获取主查询数据
						fillOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
							.LeftJoin<MaterialEntity>((po, m) => po.MaterialId == m.ID)
							.LeftJoin<SappackorderEntity>((po, m, sp) => po.ProductionOrderNo == sp.Aufnr)
							.LeftJoin<EquipmentEntity>((po, m, sp, eq) => eq.EquipmentCode == ("PSA_" + sp.Lgort))
							.Where(po => po.PoStatus != "4" && fillOrderNos.Contains(po.ProductionOrderNo))
							.Select((po, m, sp, eq) => new OrderTreeModel
							{
								ProductionOrderId = po.ID,
								ProductionOrderNo = po.ProductionOrderNo,
								PlanStartDate = po.PlanStartTime.Value.Date,
								PlanDate = po.PlanDate,
								FormulaMaterialId = po.FormulaMaterialId,
								MaterialId = po.MaterialId,
								MaterialCode = m.Code,
								MaterialName = m.NAME,
								Sequence = po.Sequence,
								PlanQty = po.PlanQty,
								MatnrComp = sp.MatnrComp,
								ProduceEquipmentId = eq.ID
							})
							.ToListAsync();

						if (fillOrders == null)
						{
							fillOrders = new List<OrderTreeModel>();
						}

						//灌装工单LotCodes
						fillOrderlotCodes = await _dal.Db.Queryable<PoProducedExecutionEntity>()
						   .LeftJoin<MaterialLotEntity>((pe, ml) => pe.LotId == ml.ID)
						   .Where(pe => fillOrders.Any(x => x.ProductionOrderId == pe.ProductionOrderId))
						   .Select((pe, ml) => new OrderTreeModel
						   {
							   ProductionOrderId = pe.ProductionOrderId,
							   LotId = pe.LotId,
							   LotCode = ml.LotId,
							   CreateDate = pe.CreateDate
						   })
						   .OrderByDescending(pe => pe.CreateDate)
						   .ToListAsync();
					}

					if (fillOrderlotCodes == null)
					{
						fillOrderlotCodes = new List<OrderTreeModel>();
					}

					// 构建最终结果
					foreach (var packOrder in packOrders)
					{
						//灌包一体
						if (isPacke)
						{
							packOrder.AVG = packFillaverageWeightDict.GetValueOrDefault(packOrder.ProductionOrderId, 1);
							packOrder.Q7 = packOrder.Q6 * packOrder.AVG;
							parentOrders.Add(packOrder);
							jlzxh += packOrder.Q7.Value;
						}
						else
						{
							var fillOrdersForPackOrder = fillOrders.Where(x => x.ProductionOrderNo == packOrder.AuartFill).ToList();
							if (fillOrdersForPackOrder?.Count > 0)
							{
								packOrder.ChildrenList = fillOrdersForPackOrder.Select(x => new OrderTreeModel
								{
									ProductionOrderId = x.ProductionOrderId,
									ProductionOrderNo = x.ProductionOrderNo,
									PlanStartDate = x.PlanStartDate,
									PlanDate = x.PlanDate,
									FormulaMaterialId = x.FormulaMaterialId,
									OrderType = "FILL",
									MaterialId = x.MaterialId,
									MaterialCode = x.MaterialCode,
									//LotId = packOrder.LotId,
									//LotCode = packOrder.LotCode,
									LotId = fillOrderlotCodes.FirstOrDefault(x1 => x1.ProductionOrderId == x.ProductionOrderId)?.LotId ?? "",
									LotCode = fillOrderlotCodes.FirstOrDefault(x1 => x1.ProductionOrderId == x.ProductionOrderId)?.LotCode ?? "",
									MaterialName = x.MaterialName,
									Sequence = x.Sequence,
									AVG = averageWeightDict.GetValueOrDefault(x.ProductionOrderId, 1),
									Q1 = packOrder.Q6,
									Q2 = packOrder.Q6 * averageWeightDict.GetValueOrDefault(x.ProductionOrderId, 1),
									MatnrComp = x.MatnrComp,
									ProduceEquipmentId = x.ProduceEquipmentId,
									ChildrenList = new List<OrderTreeModel>()
								}).ToList();
								parentOrders.AddRange(packOrder.ChildrenList);
							}
							jlzxh += packOrder.ChildrenList?.Sum(x => x.Q2.Value) ?? 0;
						}
					}

					// 获取煮制工单及其最新 LOTCode
					var cookOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
						.LeftJoin<MaterialEntity>((po, m) => po.MaterialId == m.ID)
						.LeftJoin<PoProducedExecutionEntity>((po, m, pe) => po.ID == pe.ProductionOrderId)
						.LeftJoin<MaterialLotEntity>((po, m, pe, ml) => pe.LotId == ml.ID) // 关联 Lot 表
						.Where(po =>
							po.PoStatus != "4" &&
							po.Type == "WorkOrder" &&
							po.SapOrderType == "ZXH2" &&
							parentOrders.Any(x2 =>
								po.PlanDate == x2.PlanDate &&
								po.FormulaMaterialId == x2.FormulaMaterialId
							))
						.GroupBy((po, m, pe, ml) => new
						{
							po.ID,
							po.ProductionOrderNo,
							PlanStartDate = po.PlanStartTime.Value.Date,
							po.PlanDate,
							po.FormulaMaterialId,
							po.MaterialId,
							m.Code,
							m.NAME,
							po.Sequence,
							po.PlanQty,
						})
						.Select((po, m, pe, ml) => new OrderTreeModel
						{
							ProductionOrderId = po.ID,
							ProductionOrderNo = po.ProductionOrderNo,
							PlanStartDate = po.PlanStartTime.Value.Date,
							PlanDate = po.PlanDate,
							FormulaMaterialId = po.FormulaMaterialId,
							MaterialId = po.MaterialId,
							MaterialCode = m.Code,
							MaterialName = m.NAME,
							Sequence = po.Sequence,
							PlanQty = po.PlanQty
						})
						.OrderBy(po => po.Sequence)
						.ToListAsync();

					if (cookOrders == null)
					{
						cookOrders = new List<OrderTreeModel>();
					}

					//煮制工单LotCodes
					var cookOrderlotCodes = await _dal.Db.Queryable<PoProducedExecutionEntity>()
						.LeftJoin<MaterialLotEntity>((pe, ml) => pe.LotId == ml.ID)
						.Where(pe => cookOrders.Any(x => x.ProductionOrderId == pe.ProductionOrderId))
						.Select((pe, ml) => new OrderTreeModel
						{
							ProductionOrderId = pe.ProductionOrderId,
							LotId = pe.LotId,
							LotCode = ml.LotId,
							CreateDate = pe.CreateDate
						})
						.OrderByDescending(pe => pe.CreateDate)
						.ToListAsync();

					if (cookOrderlotCodes == null)
					{
						cookOrderlotCodes = new List<OrderTreeModel>();
					}

					cookProduceSum = cookOrders.Sum(x => x.PlanQty);

					var cookProduceEquipmentId = (await _equdal.FindList(x => x.EquipmentCode == "PSA_MFG3"))?.FirstOrDefault()?.ID ?? "";

					// 将结果转换为 OrderTreeModel
					var finalcookOrders = cookOrders.Select(cookOrder => new OrderTreeModel
					{
						ProductionOrderId = cookOrder.ProductionOrderId,
						OrderType = "COOK",
						ProductionOrderNo = cookOrder.ProductionOrderNo,
						PlanStartDate = cookOrder.PlanStartDate,
						PlanDate = cookOrder.PlanDate,
						FormulaMaterialId = cookOrder.FormulaMaterialId,
						MaterialId = cookOrder.MaterialId,
						MaterialCode = cookOrder.MaterialCode,
						MaterialName = cookOrder.MaterialName,
						Sequence = cookOrder.Sequence,
						PlanQty = cookOrder.PlanQty,
						LotId = cookOrderlotCodes.FirstOrDefault(x => x.ProductionOrderId == cookOrder.ProductionOrderId)?.LotId ?? "",
						LotCode = cookOrderlotCodes.FirstOrDefault(x => x.ProductionOrderId == cookOrder.ProductionOrderId)?.LotCode ?? "",
						Q6 = Math.Round(cookOrder.PlanQty / cookProduceSum * jlzxh, 3),
						Q7 = Math.Round(cookOrder.PlanQty / cookProduceSum * jlzxh, 3),
						ProduceEquipmentId = cookProduceEquipmentId
					}).ToList();

					foreach (var packOrder in packOrders)
					{
						if (isPacke)
						{
							packOrder.ChildrenList = new List<OrderTreeModel>();
							// 处理灌包一体的情况
							ProcessPackOrder(packOrder, packOrder.Q7, finalcookOrders);
						}
						else
						{
							packOrder.ChildrenList = packOrder.ChildrenList ?? new List<OrderTreeModel>();
							// 处理灌包分离的情况
							foreach (var fillOrder in packOrder.ChildrenList)
							{
								fillOrder.ChildrenList = new List<OrderTreeModel>();
								ProcessPackOrder(fillOrder, fillOrder.Q2, finalcookOrders);
							}
						}
					}
				}

				result.response = packOrders;
				result.success = true;
				result.msg = "操作成功！";
			}
			catch (Exception ex)
			{
				result.msg = $"操作失败！{ex.Message}";
			}

			return result;
		}

		/// <summary>
		/// 提交保存报工数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SavePackReport(List<OrderTreeModel> reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};

			// 初始化 consume 和 produce 列表
			List<PoConsumeActualEntity> poConsumeActuals1 = new List<PoConsumeActualEntity>();
			List<PoProducedActualEntity> poProducedActuals1 = new List<PoProducedActualEntity>();
			List<PoConsumeActualEntity> poConsumeActuals2 = new List<PoConsumeActualEntity>();
			List<PoProducedActualEntity> poProducedActuals2 = new List<PoProducedActualEntity>();
			var now = DateTime.Now;
			var dateList = new List<DateTime>
			{
				now.AddMinutes(-15),
				now.AddMinutes(-10),
				now.AddMinutes(-5),
				now,
			};
			var units = (await _dal.Db.Queryable<UnitmanageEntity>().ToListAsync())
						   .ToDictionary(
							   x => x.Name,
							   x => x.ID
						   );
			// 获取主查询数据
			var produceExs = await _dal.Db.Queryable<PoProducedExecutionEntity>()
			   .LeftJoin<PoSegmentRequirementEntity>((pe, psr) => pe.PoSegmentRequirementId == psr.ID)
			   .LeftJoin<SapSegmentEntity>((pe, psr, ss) => psr.SegmentId == ss.ID)
			   .Select((pe, psr, ss) => new RunOrderModel()
			   {
				   ID = pe.ID,
				   ProductionOrderId = pe.ProductionOrderId,
				   RunEquipmentId = pe.RunEquipmentId,
				   PoProducedRequirementId = pe.PoProducedRequirementId,
				   StartTime = pe.StartTime,
				   EndTime = pe.EndTime,
				   CreateDate = pe.CreateDate,
				   ModifyDate = pe.ModifyDate,
				   SegmentId = psr.SegmentId,
				   SegmentCode = ss.SegmentCode,
				   SegmentName = ss.SegmentName,
			   })
			   .ToListAsync();

			var materLots = GetUniqueFields(reqModel);

			var lotList = await _dal.Db.Queryable<MaterialLotEntity>().Where(x => materLots.Item1.Contains(x.LotId))
				.ToListAsync();

			var poConsumeRequirements = await _dal.Db.Queryable<PoConsumeRequirementEntity>().Where(x => materLots.Item2.Contains(x.ProductionOrderId))
			.ToListAsync();

			List<MaterialLotEntity> addLots = new List<MaterialLotEntity>();

			foreach (var item in reqModel)
			{
				TraverseOrderTree(item.isPackFill, dateList, item, poProducedActuals1, poConsumeActuals1, poProducedActuals2, poConsumeActuals2, units, produceExs, addLots, lotList, poConsumeRequirements);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (poProducedActuals1.Count > 1000)
				{
					await _poproduceDal.AddBigData(poProducedActuals1);
				}
				else if (poProducedActuals1.Count > 0)
				{
					await _poproduceDal.Add(poProducedActuals1);
				}

				if (poConsumeActuals1.Count > 1000)
				{
					await _poconsumeDal.AddBigData(poConsumeActuals1);
				}
				else if (poConsumeActuals1.Count > 0)
				{
					await _poconsumeDal.Add(poConsumeActuals1);
				}

				if (poProducedActuals2.Count > 1000)
				{
					await _poproduceDal.AddBigData(poProducedActuals2);
				}
				else if (poProducedActuals2.Count > 0)
				{
					await _poproduceDal.Add(poProducedActuals2);
				}

				if (poConsumeActuals2.Count > 1000)
				{
					await _poconsumeDal.AddBigData(poConsumeActuals2);
				}
				else if (poConsumeActuals2.Count > 0)
				{
					await _poconsumeDal.Add(poConsumeActuals2);
				}

				if (addLots.Count > 1000)
				{
					await _lotDal.AddBigData(addLots);
				}
				else if (addLots.Count > 0)
				{
					await _lotDal.Add(addLots);
				}

				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}

			await _produceViewServices.ProduceReport(poProducedActuals1.Select(x => x.ID).ToList());
			await _consumeViewServices.ConsumeReport(poConsumeActuals1.Select(x => x.ID).ToList());
			await _produceViewServices.ProduceReport(poProducedActuals2.Select(x => x.ID).ToList());
			await _consumeViewServices.ConsumeReport(poConsumeActuals2.Select(x => x.ID).ToList());

			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		// 递归遍历函数
		private void TraverseOrderTree(bool isFillPack, List<DateTime> dateList, OrderTreeModel model, List<PoProducedActualEntity> produce1,
			List<PoConsumeActualEntity> consume1, List<PoProducedActualEntity> produce2, List<PoConsumeActualEntity> consume2,
			Dictionary<string, string> units, List<RunOrderModel> poExs, List<MaterialLotEntity> addMaterialLots, List<MaterialLotEntity> materialLots, List<PoConsumeRequirementEntity> poConsumeRequirements)
		{
			if (model == null) return;

			var unit_id_EA = units.GetValueOrDefault("EA", "");
			var unit_id_KG = units.GetValueOrDefault("KG", "");
			var runOrder = (poExs.Where(x => x.ProductionOrderId == model.ProductionOrderId && (x.SegmentName.Contains("Fill") || x.SegmentName.Contains("Pack"))).OrderByDescending(x => x.CreateDate)).FirstOrDefault();
			if (model.OrderType == "COOK")
			{
				runOrder = (poExs.Where(x => x.ProductionOrderId == model.ProductionOrderId).OrderByDescending(x => x.CreateDate)).FirstOrDefault();
			}

			string poConsumeRequirementId = "";
			string consumeLotId = "";

			if (model.ChildrenList?.Count > 0)
			{
				var child_first = model.ChildrenList.First();

				poConsumeRequirementId = poConsumeRequirements.FirstOrDefault(x => x.ProductionOrderId == model.ProductionOrderId && x.MaterialId == child_first.MaterialId)?.ID ?? "";

				var lot = addMaterialLots.FirstOrDefault(x => x.LotId == model.LotCode && x.MaterialId == child_first.MaterialId);
				if (lot == null)
				{
					lot = materialLots.FirstOrDefault(x => x.LotId == model.LotCode && x.MaterialId == child_first.MaterialId);
				}
				if (lot == null)
				{
					var lot1 = materialLots.FirstOrDefault(x => x.ID == child_first.LotId);
					lot = new();
					lot.CreateCustomGuid(_user.Name.ToString());
					lot.LotId = model.LotCode;
					lot.MaterialId = lot1.MaterialId;
					lot.ProductionDateLocal = lot1.ProductionDateLocal;
					lot.ExpirationDate = lot1.ExpirationDate;
					lot.Type = "1";
					lot.ExternalStatus = "2";//默认为解锁
					lot.DispositionId = "1";
					addMaterialLots.Add(lot);
				}
				consumeLotId = lot.ID;
			}

			// 处理 灌包分离的逻辑
			if (!isFillPack)
			{
				switch (model.OrderType)
				{
					case "PACK":
						//写入poConsumeActual表
						var poConsumeActual2 = new PoConsumeActualEntity
						{
							ProductionOrderId = model.ProductionOrderId,
							PoConsumeRequirementId = poConsumeRequirementId,
							ProductExecutionId = runOrder?.ID ?? "",
							EquipmentId = runOrder?.RunEquipmentId ?? "",
							SourceEquipmentId = "",
							Quantity = model.Q6,
							UnitId = unit_id_EA,
							LotId = consumeLotId,
							SubLotId = "",
							SubLotStatus = 0,
							StorageBin = "",
							StorageLocation = "",
							ContainerId = "",
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							ReasonCode = "",
							Comment = "",
							Deleted = 0,
							SendExternal = 0
						};
						poConsumeActual2.CreateCustomGuid(_user.Name);
						poConsumeActual2.CreateDate = dateList[3];
						poConsumeActual2.ModifyDate = dateList[3];
						consume2.Add(poConsumeActual2);
						break;
					case "FILL":
						//写入poProducedActual表
						var poProducedActual2 = new PoProducedActualEntity()
						{
							ProductExecutionId = runOrder?.ID ?? "",
							ProductionOrderId = model.ProductionOrderId,
							PoProducedRequirementId = runOrder?.PoProducedRequirementId ?? "",
							LotId = model.LotId,
							LotStatus = 0,
							SubLotId = "",
							SubLotStatus = 3,
							SourceType = 0,
							EquipmentId = model.ProduceEquipmentId ?? "",
							DesinationEquipmentId = model.ProduceEquipmentId ?? "",
							UnitId = unit_id_EA,
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							Quantity = model.Q1,
						};
						poProducedActual2.CreateCustomGuid(_user.Name);
						poProducedActual2.CreateDate = dateList[2];
						poProducedActual2.ModifyDate = dateList[2];
						produce2.Add(poProducedActual2);
						var poConsumeActual1 = new PoConsumeActualEntity
						{
							ProductionOrderId = model.ProductionOrderId,
							PoConsumeRequirementId = poConsumeRequirementId,
							ProductExecutionId = runOrder?.ID ?? "",
							EquipmentId = runOrder?.RunEquipmentId ?? "",
							SourceEquipmentId = "",
							LotId = consumeLotId,
							Quantity = model.Q2,
							UnitId = unit_id_KG,
							SubLotId = "",
							SubLotStatus = 0,
							StorageBin = "",
							StorageLocation = "",
							ContainerId = "",
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							ReasonCode = "",
							Comment = "",
							Deleted = 0,
							SendExternal = 0
						};
						poConsumeActual1.CreateCustomGuid(_user.Name);
						poConsumeActual1.CreateDate = dateList[1];
						poConsumeActual1.ModifyDate = dateList[1];
						consume1.Add(poConsumeActual1);
						break;
					case "COOK":
						//写入poProducedActual表
						var poProducedActual1 = new PoProducedActualEntity()
						{
							ProductExecutionId = runOrder?.ID ?? "",
							ProductionOrderId = model.ProductionOrderId,
							PoProducedRequirementId = runOrder?.PoProducedRequirementId ?? "",
							LotId = model.LotId,
							LotStatus = 0,
							SubLotId = "",
							SubLotStatus = 3,
							SourceType = 0,
							EquipmentId = model.ProduceEquipmentId ?? "",
							DesinationEquipmentId = model.ProduceEquipmentId ?? "",
							UnitId = unit_id_KG,
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							Quantity = model.Q1,
						};
						poProducedActual1.CreateCustomGuid(_user.Name);
						poProducedActual1.CreateDate = dateList[0];
						poProducedActual1.ModifyDate = dateList[0];
						produce1.Add(poProducedActual1);
						break;
					default:
						break;
				}
			}
			// 处理 灌包一体的逻辑 
			else
			{
				switch (model.OrderType)
				{
					case "PACK":
						//写入poConsumeActual表
						var poConsumeActual1 = new PoConsumeActualEntity
						{
							ProductionOrderId = model.ProductionOrderId,
							PoConsumeRequirementId = poConsumeRequirementId,
							ProductExecutionId = runOrder?.ID ?? "",
							EquipmentId = runOrder?.RunEquipmentId ?? "",
							SourceEquipmentId = "",
							Quantity = model.Q7,
							UnitId = unit_id_KG,
							LotId = consumeLotId,
							SubLotId = "",
							SubLotStatus = 0,
							StorageBin = "",
							StorageLocation = "",
							ContainerId = "",
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							ReasonCode = "",
							Comment = "",
							Deleted = 0,
							SendExternal = 0
						};
						poConsumeActual1.CreateCustomGuid(_user.Name);
						poConsumeActual1.CreateDate = dateList[1];
						poConsumeActual1.ModifyDate = dateList[1];
						consume1.Add(poConsumeActual1);
						break;
					case "COOK":
						//写入poProducedActual表
						var poProducedActual1 = new PoProducedActualEntity()
						{
							ProductExecutionId = runOrder?.ID ?? "",
							ProductionOrderId = model.ProductionOrderId,
							PoProducedRequirementId = runOrder.PoProducedRequirementId,
							LotId = model.LotId,
							LotStatus = 0,
							SubLotId = "",
							SubLotStatus = 3,
							SourceType = 0,
							EquipmentId = model.ProduceEquipmentId ?? "",
							DesinationEquipmentId = model.ProduceEquipmentId ?? "",
							UnitId = unit_id_KG,
							TeamId = "",
							ShiftId = "02308292-2383-5176-163e-0370f6000000",
							Quantity = model.Q1,
						};
						poProducedActual1.CreateCustomGuid(_user.Name);
						poProducedActual1.CreateDate = dateList[0];
						poProducedActual1.ModifyDate = dateList[0];
						produce1.Add(poProducedActual1);
						break;
					default:
						break;
				}

			}
			if (model.ChildrenList?.Count > 0)
			{
				// 递归遍历子列表
				foreach (var child in model.ChildrenList)
				{
					TraverseOrderTree(isFillPack, dateList, child, produce1, consume1, produce2, consume2, units, poExs, addMaterialLots, materialLots, poConsumeRequirements);
				}
			}
		}

		/// <summary>
		/// 获取所有相关的物料lot
		/// </summary>
		/// <param name="orderTreeModels"></param>
		/// <returns></returns>
		public static (List<string>, List<string>) GetUniqueFields(List<OrderTreeModel> orderTreeModels)
		{
			var uniqueFields = new HashSet<string>();
			var uniqueFields2 = new HashSet<string>();
			var result = new List<string>();
			var result2 = new List<string>();

			if (orderTreeModels == null)
			{
				return (result, result2);
			}

			void Traverse(OrderTreeModel node)
			{
				if (node == null) return;

				// 将字段组合成一个字符串，用于去重
				var fieldString = $"{node.LotCode}";
				if (uniqueFields.Add(fieldString))
				{
					result.Add(node.LotCode);
				}

				// 将字段组合成一个字符串，用于去重
				var fieldString2 = $"{node.ProductionOrderId}";
				if (uniqueFields2.Add(fieldString2))
				{
					result2.Add(node.ProductionOrderId);
				}

				if (node.ChildrenList?.Count > 0)
				{
					// 递归遍历子节点
					foreach (var child in node.ChildrenList)
					{
						Traverse(child);
					}
				}
			}

			foreach (var model in orderTreeModels)
			{
				Traverse(model);
			}

			return (result, result2);
		}

		#endregion


	}
}