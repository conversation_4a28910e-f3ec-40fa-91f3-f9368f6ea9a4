
using LKK.Lib.Core;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.Base.Extensions;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Repository.Base;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.PTM.Services;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using UnitmanageEntity = SEFA.DFM.Model.Models.UnitmanageEntity;

namespace SEFA.PPM.Services
{
	public class ProductionOrderServices : BaseServices<ProductionOrderEntity>, IProductionOrderServices
	{
		private readonly IBaseRepository<ProductionOrderEntity> _dal;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _poSegmentRequirementService;
		private readonly IBaseRepository<PoConsumeRequirementEntity> _poConsumeRequirementService;
		private readonly IBaseRepository<PoProducedRequirementEntity> _poProducedRequirementService;
		private readonly IDFMServices _dFMServices;
		private readonly IUser _user;
		private readonly IUnitOfWork _unitOfWork;
        private readonly IPoProducedExecutionServices _poProducedExecutionServices;
        private readonly IRedisBasketRepository _redisBasketRepository;
        public ProductionOrderServices(IBaseRepository<ProductionOrderEntity> dal,
			IBaseRepository<PoSegmentRequirementEntity> poSegmentRequirementService,
			IBaseRepository<PoConsumeRequirementEntity> poConsumeRequirementService,
			IBaseRepository<PoProducedRequirementEntity> poProducedRequirementService,
			IDFMServices dFMServices, IUser user, IUnitOfWork unitOfWork,
			IRedisBasketRepository redisBasketRepository, 
			IPoProducedExecutionServices poProducedExecutionServices)
		{
			this._dal = dal;
			base.BaseDal = dal;
			this._dFMServices = dFMServices;
			this._user = user;
			_poSegmentRequirementService = poSegmentRequirementService;
			_poConsumeRequirementService = poConsumeRequirementService;
			_poProducedRequirementService = poProducedRequirementService;
			_unitOfWork = unitOfWork;
            _poProducedExecutionServices = poProducedExecutionServices;
            _redisBasketRepository = redisBasketRepository;
        }

		public async Task<List<ProductionOrderEntity>> GetList(ProductionOrderRequestModel reqModel)
		{
			List<ProductionOrderEntity> result = new List<ProductionOrderEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProductionOrderEntity>()
							 .ToExpression();
			var data = await _dal.Db.Queryable<ProductionOrderEntity>()
				.Where(whereExpression).ToListAsync();
			return data;
		}

		public async Task<PageModel<ProductionOrderEntity>> GetPageList(ProductionOrderRequestModel reqModel)
		{
			PageModel<ProductionOrderEntity> result = new PageModel<ProductionOrderEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProductionOrderEntity>()
							 .ToExpression();

			var data = await _dal.Db.Queryable<ProductionOrderEntity>()
				.Where(whereExpression)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<List<OrderTextResponse>> GetOrderText(OrderTextRequest reqModel)
		{
			var whereExpression = Expressionable.Create<ProductionOrderEntity, MaterialProcessDataEntity, MaterialEntity, MaterialVersionEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), (p, mp, m, mv) => p.ProductionOrderNo == reqModel.ProductionOrderNo)
				.AndIF(string.IsNullOrEmpty(reqModel.ProductionOrderNo), (p, mp, m, mv) => p.MaterialId == reqModel.MaterialId && p.PlanStartTime.Value.ToString("yyyy-MM-dd") == reqModel.Date)
				.And((p, mp, m, mv) => p.SapOrderType == "ZXH2")
				.And((p, mp, m, mv) => !string.IsNullOrEmpty(p.ProductionOrderNo))
				.ToExpression();
			var result = await _dal.QueryMuch<ProductionOrderEntity, MaterialProcessDataEntity, MaterialEntity, MaterialVersionEntity, OrderTextResponse>((p, mp, m, mv) =>
			new object[] {
					JoinType.Left,mp.OrderId == p.ID && mp.Type == 0 && mp.Status == "2",
					JoinType.Left,m.ID == p.MaterialId,
					JoinType.Left,mv.ID == p.MaterialVersionId
			},
			(p, mp, m, mv) => new OrderTextResponse
			{
				ProductionOrderId = p.ID,
				ProductionOrderNo = p.ProductionOrderNo,
				PlanStartTime = p.PlanStartTime,
				MaterialCode = m.Code,
				MaterialVersionNumber = mv.MaterialVersionNumber,
				ProcessData = mp.ProcessData,
				Token = mp.Token,
			},
			whereExpression
			);

			foreach (var item in result)
			{
				if (!string.IsNullOrEmpty(item.ProcessData))
				{
					item.ProcessData = Enigma.Decrypt(item.ProcessData, item.Token).Replace("@@", "");
				}
			}

			return result.OrderBy(x => x.PlanStartTime).ToList();
		}
		/// <summary>
		/// 批量修改备注信息
		/// </summary>
		/// <param name="req">请求信息</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> BatchEditOrderRemark(OrderRemarkChangeModel req)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			var orderInfoList = await _dal.FindList(p => req.OrderID.Contains(p.ID));
			if (orderInfoList.Count == 0)
			{
				result.msg = "工单不存在";
			}
			else
			{
				foreach (var orderInfo in orderInfoList)
				{
					orderInfo.Remark = req.Remark;
					orderInfo.Modify(orderInfo.ID, _user.Name);
				}
				result.success = await _dal.Update(orderInfoList);
				result.msg = result.success ? "操作成功" : "操作失败";
			}
			return result;
		}

		public async Task<bool> SaveForm(ProductionOrderEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				entity.CreateCustomGuid(_user.Name);
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
		public async Task<MessageModel<string>> CalculateSegementRequirement(string PoId)
		{
			var entity = await _dal.FindEntity(PoId);
			if (entity == null)
			{
				return new MessageModel<string>()
				{
					msg = "PoId不存在",
					success = false
				};
			}
			return await CalculateSegementRequirement(entity);
		}

		public async Task<MessageModel<string>> CalculateSegementRequirement(ProductionOrderEntity poEntity)
		{
			var result = new MessageModel<string>();
			result.success = false;

			List<PoSegmentRequirementEntity> poSegmentRequirementEntities = new List<PoSegmentRequirementEntity>();
			List<PoConsumeRequirementEntity> poConsumeRequirementEntities = new List<PoConsumeRequirementEntity>();
			List<PoProducedRequirementEntity> poProducedRequirementEntities = new List<PoProducedRequirementEntity>();
			List<ConsumeTempModel> consumeTempModelList = new List<ConsumeTempModel>();
			List<ProductTempModel> productTempModelList = new List<ProductTempModel>();

			/*根据工单物料版本号+ Bom版本 从DFM 获取维护的工序物料信息*/
			var segmentMaterialResopnse = await _dFMServices.GetSapSegmentMaterialList(poEntity.MaterialVersionId, poEntity.BomVersion);
			if (!segmentMaterialResopnse.success)
			{
				return result;
			}
			/*根据工单物料版本号获取BomInjection物料版本维护的工序物料信息*/
			var bomInjectionResopnse = await _dFMServices.GetSapSapBomPhaseInjectList(poEntity.MaterialVersionId);
			if (!bomInjectionResopnse.success)
			{
				return result;
			}

			var materialVersionResponse = await _dFMServices.GetMaterialVersion(poEntity.MaterialVersionId);
			if (!materialVersionResponse.success)
			{
				return result;
			}


			var segmentMaterialList = segmentMaterialResopnse.response;
			var bomInjectionList = bomInjectionResopnse.response;

			List<string> segmentidList = segmentMaterialList.Select(a => a.SapSegmentId).Distinct().ToList();

			foreach (var bomInjectionItem in bomInjectionList)
			{
				if (segmentidList.Contains(bomInjectionItem.ProductSapSegmentId) == false)
					continue;
				segmentidList.Add(bomInjectionItem.ProductSapSegmentId);
			}
			foreach (var bomInjectionItem in bomInjectionResopnse.response)
			{
				if (segmentidList.Contains(bomInjectionItem.ConsumeSapSegmentId) == false)
					continue;
				segmentidList.Add(bomInjectionItem.ConsumeSapSegmentId);
			}
			if (segmentidList.Count == 0)
			{
				return result;
			}

			foreach (var segmentId in segmentidList)
			{
				PoSegmentRequirementEntity poSegmentRequirementEntity = new PoSegmentRequirementEntity();
				poSegmentRequirementEntity.CreateCustomGuid(_user.Name);
				poSegmentRequirementEntity.SegmentId = segmentId;
				poSegmentRequirementEntity.ProductionOrderId = poEntity.ID;

				poSegmentRequirementEntities.Add(poSegmentRequirementEntity);
			}

			foreach (var segmentRequireItem in poSegmentRequirementEntities)
			{
				/*获取工序消耗物料*/
				var consumeMateriallist = segmentMaterialList.Where(a => a.SapSegmentId == segmentRequireItem.SegmentId).OrderBy(a => a.SortOrder);
				foreach (var item in consumeMateriallist)
				{
					PoConsumeRequirementEntity poConsumeRequirementEntity = new PoConsumeRequirementEntity();
					poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
					poConsumeRequirementEntity.PoSegmentRequirementId = segmentRequireItem.ID;
					poConsumeRequirementEntity.MaterialId = item.MaterialId;
					poConsumeRequirementEntity.Quantity = poEntity.PlanQty * (item.AdjustPercentQuantity ?? item.PercentQuantity) / 100;
					poConsumeRequirementEntity.UnitId = item.UnitId;
					poConsumeRequirementEntity.ProductionOrderId = poEntity.ID;
					poConsumeRequirementEntities.Add(poConsumeRequirementEntity);

					consumeTempModelList.Add(new ConsumeTempModel
					{
						PoSegmentRequirementId = poConsumeRequirementEntity.PoSegmentRequirementId,
						MaterialId = poConsumeRequirementEntity.MaterialId,
						SapSegmentId = segmentRequireItem.SegmentId,
						Quantity = poConsumeRequirementEntity.Quantity,
						UnitId = poConsumeRequirementEntity.UnitId,
					});
				}
				/*获取仅消耗的物料*/
				var bomConsumeOnlyMaterialList = bomInjectionList.Where(a => a.ProductSapSegmentId == segmentRequireItem.SegmentId && a.Type == 2).ToList();
				foreach (var item in bomConsumeOnlyMaterialList)
				{
					PoConsumeRequirementEntity poConsumeRequirementEntity = new PoConsumeRequirementEntity();
					poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
					poConsumeRequirementEntity.PoSegmentRequirementId = segmentRequireItem.ID;
					poConsumeRequirementEntity.MaterialId = item.MaterialId;
					poConsumeRequirementEntity.Quantity = item.ConsumeQuantity;
					poConsumeRequirementEntity.UnitId = item.ConsumeUnitId;
					poConsumeRequirementEntity.ProductionOrderId = poEntity.ID;
					poConsumeRequirementEntities.Add(poConsumeRequirementEntity);

					consumeTempModelList.Add(new ConsumeTempModel
					{
						PoSegmentRequirementId = poConsumeRequirementEntity.PoSegmentRequirementId,
						MaterialId = poConsumeRequirementEntity.MaterialId,
						SapSegmentId = segmentRequireItem.SegmentId,
						Quantity = poConsumeRequirementEntity.Quantity,
						UnitId = poConsumeRequirementEntity.UnitId,
					});
				}
			}

			foreach (var segmentRequireItem in poSegmentRequirementEntities)
			{
				var wipQty = consumeTempModelList.Where(a => a.SapSegmentId == segmentRequireItem.SegmentId).Sum(a => a.Quantity);
				/*获取WIP消耗的物料*/
				var bomWipConsumeMaterialList = bomInjectionList.Where(a => a.ProductSapSegmentId == segmentRequireItem.SegmentId && a.Type == 1).ToList();
				foreach (var item in bomWipConsumeMaterialList)
				{
					PoProducedRequirementEntity poProducedRequirementEntity = new PoProducedRequirementEntity();
					poProducedRequirementEntity.CreateCustomGuid(_user.Name);
					poProducedRequirementEntity.PoSegmentRequirementId = segmentRequireItem.ID;
					poProducedRequirementEntity.MaterialId = item.MaterialId;
					poProducedRequirementEntity.Quantity = item.PercentQuantity * wipQty / 100;
					poProducedRequirementEntity.UnitId = item.ProductMaterialUnitId;
					poProducedRequirementEntity.ProductionOrderId = poEntity.ID;
					poProducedRequirementEntities.Add(poProducedRequirementEntity);

					productTempModelList.Add(new ProductTempModel
					{
						PoSegmentRequirementId = poProducedRequirementEntity.PoSegmentRequirementId,
						MaterialId = poProducedRequirementEntity.MaterialId,
						SapSegmentId = segmentRequireItem.SegmentId,
						Quantity = poProducedRequirementEntity.Quantity,
						UnitId = poProducedRequirementEntity.UnitId,
					});
				}
			}

			foreach (var segmentRequireItem in poSegmentRequirementEntities)
			{
				/*获取产出工序消耗的WIP物料*/
				var bomWipConsumeMaterialList = bomInjectionList.Where(a => a.ConsumeSapSegmentId == segmentRequireItem.SegmentId && a.Type == 1).ToList();
				foreach (var item in bomWipConsumeMaterialList)
				{
					var wipQty = consumeTempModelList.Where(a => a.SapSegmentId == item.ProductSapSegmentId && a.MaterialId == item.MaterialId).Sum(a => a.Quantity);

					PoConsumeRequirementEntity poConsumeRequirementEntity = new PoConsumeRequirementEntity();
					poConsumeRequirementEntity.CreateCustomGuid(_user.Name);
					poConsumeRequirementEntity.PoSegmentRequirementId = segmentRequireItem.ID;
					poConsumeRequirementEntity.MaterialId = item.MaterialId;
					poConsumeRequirementEntity.Quantity = wipQty;
					poConsumeRequirementEntity.UnitId = item.ProductMaterialUnitId;
					poConsumeRequirementEntity.ProductionOrderId = poEntity.ID;
					poConsumeRequirementEntities.Add(poConsumeRequirementEntity);
				}
			}
			string lastSegment = string.Empty;
			if (segmentidList.Count == 1)
			{
				lastSegment = segmentidList[0];
			}
			else
			{
				lastSegment = CalculateLastSegment(segmentMaterialList, bomInjectionList);
			}

			if (!string.IsNullOrEmpty(lastSegment))
			{
				var entity = poSegmentRequirementEntities.Where(a => a.SegmentId == lastSegment).FirstOrDefault();
				if (entity != null)
				{

					PoProducedRequirementEntity poProducedRequirementEntity = new PoProducedRequirementEntity();
					poProducedRequirementEntity.CreateCustomGuid(_user.Name);
					poProducedRequirementEntity.PoSegmentRequirementId = entity.ID;
					poProducedRequirementEntity.MaterialId = materialVersionResponse.response.MaterialId;
					poProducedRequirementEntity.MaterialVersionId = poEntity.MaterialVersionId;
					poProducedRequirementEntity.Quantity = poEntity.PlanQty;
					poProducedRequirementEntity.UnitId = "";
					poProducedRequirementEntity.ProductionOrderId = poEntity.ID;
					poProducedRequirementEntities.Add(poProducedRequirementEntity);
				}
			}

			_unitOfWork.BeginTran();
			try
			{

				if (poSegmentRequirementEntities.Count > 0)
					await _poSegmentRequirementService.Add(poSegmentRequirementEntities);
				if (poProducedRequirementEntities.Count > 0)
					await _poProducedRequirementService.Add(poProducedRequirementEntities);
				if (poConsumeRequirementEntities.Count > 0)
					await _poConsumeRequirementService.Add(poConsumeRequirementEntities);
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}

			return result;
		}

		public string CalculateLastSegment(List<SapSegmentMaterialStepModel> sapSegmentMaterialStepModels, List<SapBomPhaseInjectionModel> sapBomPhaseInjectionModels)
		{

			string lastSegment = string.Empty;

			if (sapSegmentMaterialStepModels.Select(a => a.SapSegmentId).Distinct().Count() == 1 && sapBomPhaseInjectionModels.Count == 0)
			{
				return sapSegmentMaterialStepModels[0].SapSegmentId;
			}
			if (sapSegmentMaterialStepModels.Select(a => a.SapSegmentId).Distinct().Count() == 0 && sapBomPhaseInjectionModels.Count == 1)
			{
				return sapBomPhaseInjectionModels[0].ConsumeParentSapSegmentId;
			}

			sapBomPhaseInjectionModels = sapBomPhaseInjectionModels.Where(a => a.Type == 1).ToList();

			foreach (var item in sapBomPhaseInjectionModels)
			{
				if (lastSegment == null)
				{
					lastSegment = item.ConsumeSapSegmentId;
					continue;
				}
				else
				{
					var segment = sapBomPhaseInjectionModels.Where(a => a.ProductSapSegmentId == lastSegment).FirstOrDefault();
					if (segment != null)
					{
						lastSegment = segment.ConsumeSapSegmentId;
						continue;
					}
				}
			}
			return lastSegment;
		}

        /// <summary>
        /// 定时比对长文本信息
        /// </summary>
        /// <param name="ids">工单ID集合</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ComparePoLongText (List<string> poIds = null) {
            var result = new MessageModel<string>();
            string key = "ComparePoLongText";
            string logName = "ComparePoLongText";

            if (await _redisBasketRepository.Exist(key))
            {
                result.msg = "存在未完成的任务，本次退出";
                SerilogServer.LogDebug(result.msg, logName);
                return result;
            }
            try
            {
                await _redisBasketRepository.Set(key, "1", new TimeSpan(0, 0, 3, 0));
                result = await ComparePoLongTextData(poIds);
                SerilogServer.LogDebug(result.msg, logName);
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug(ex.ToString(), logName);
                result.msg = ex.ToString();
            }
            finally
            {
                await _redisBasketRepository.Remove(key);
            }
            return result;
        }


        public async Task<MessageModel<string>> ComparePoLongTextData (List<string> poIds) {
            var result = new MessageModel<string>();
            string logName = "ComparePoLongText";
            List<ProductionOrderEntity> productionOrderList = new List<ProductionOrderEntity>();
            if (poIds != null && poIds.Count > 0)
            {
                productionOrderList = await _dal.FindList(a => a.SapOrderType == "ZXH2"  && a.SapFlag > 0  && poIds.Contains(a.ID), a => a.MaterialId);
            }
            else
            {
				DateTime date = DateTime.Now.Date;
                productionOrderList = await _dal.FindList(a => a.SapOrderType == "ZXH2" && a.SapFlag > 0 && a.LongTextResult != "NA" &&
				(string.IsNullOrEmpty(a.LongTextResult) || (a.LongTextResult != null && !a.LongTextResult.Contains("比对通过")) ) && a.PoStatus == "2" && a.PlanDate >= date, a => a.MaterialId);
            }
            if (productionOrderList.Count == 0)
            {
                result.success = true;
                result.msg = "无需要处理的记录";
                return result;
            }
            SerilogServer.LogDebug($"本次比对工单[{productionOrderList.Count}]", logName);
            int successCount = 0;
            foreach (var item in productionOrderList)
            {
                var data = await _poProducedExecutionServices.GetCookOrderLtexts(new UpdateQaStatusRequestModel() { Id = item.ID });
                //更新比对结果
                item.LongTextResult = data.msg;
                if (data.success)
                {
                    successCount++;
                }
                SerilogServer.LogDebug($"比对工单[{item.ProductionOrderNo}],结果[{item.LongTextResult}]", logName);
            }
            await _dal.Db.Updateable<ProductionOrderEntity>(productionOrderList).UpdateColumns(a => a.LongTextResult).ExecuteCommandAsync();
            result.msg = $"本次比对工单[{productionOrderList.Count}],比对成功[{successCount}]";
            return result;
        }




	


        public class ConsumeTempModel
		{
			public string PoSegmentRequirementId { get; set; }
			public string SapSegmentId { get; set; }
			public string MaterialId { get; set; }
			public decimal? Quantity { get; set; }
			public string UnitId { get; set; }
		}
		public class ProductTempModel
		{
			public string PoSegmentRequirementId { get; set; }
			public string SapSegmentId { get; set; }
			public string MaterialId { get; set; }
			public decimal? Quantity { get; set; }
			public string UnitId { get; set; }
		}
	}
}