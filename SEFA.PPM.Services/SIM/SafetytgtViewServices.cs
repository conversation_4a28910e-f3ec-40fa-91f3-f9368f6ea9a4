
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPM.Services
{
    public class SafetytgtViewServices : BaseServices<SafetytgtViewEntity>, ISafetytgtViewServices
    {
        private readonly IBaseRepository<SafetytgtViewEntity> _dal;
        public SafetytgtViewServices(IBaseRepository<SafetytgtViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<SafetytgtViewEntity>> GetList(SafetytgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SafetytgtViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }
        /// <summary>
        /// 安全质量维护导出
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SafetyModel>> SafetyExportData(SafetytgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SafetytgtViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ModelRef), p => p.ModelRefCode == reqModel.ModelRef)
                .AndIF(!string.IsNullOrEmpty(reqModel.EventType), p => p.EventType == reqModel.EventType)
                .AndIF(!string.IsNullOrEmpty(reqModel.DateOfOccurrenceFrom), p => p.DateOfOccurrence >= Convert.ToDateTime(reqModel.DateOfOccurrenceFrom))
                .AndIF(!string.IsNullOrEmpty(reqModel.DateOfOccurrenceTo), p => p.DateOfOccurrence <= Convert.ToDateTime(reqModel.DateOfOccurrenceTo))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            var results = (from a in data
                           select new SafetyModel
                           {
                               Itemname = a.Itemname,
                               DateOfOccurrence = a.DateOfOccurrence.ToString("yyyy-MM-dd HH:mm:ss"),
                               ModelRefName = a.ModelRefName,
                               Comments = a.Comments

                           }).ToList();
            return results;
        }
        /// <summary>
        /// 安全质量维护查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>

        public async Task<PageModel<SafetytgtViewEntity>> GetPageList(SafetytgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SafetytgtViewEntity>()
                .AndIF (!string.IsNullOrEmpty(reqModel.ModelRef), p=>p.ModelRefCode == reqModel.ModelRef)
                .AndIF (!string.IsNullOrEmpty(reqModel.EventType), p=>p.EventType == reqModel.EventType)
                .AndIF (!string.IsNullOrEmpty(reqModel.DateOfOccurrenceFrom), p=>p.DateOfOccurrence>= Convert.ToDateTime(reqModel.DateOfOccurrenceFrom))
                .AndIF (!string.IsNullOrEmpty(reqModel.DateOfOccurrenceTo), p=>p.DateOfOccurrence <= Convert.ToDateTime(reqModel.DateOfOccurrenceTo))
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(SafetytgtViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await _dal.Add(entity) > 0;
            }
            else
            {
                return await _dal.Update(entity);
            }
        }
    }
}