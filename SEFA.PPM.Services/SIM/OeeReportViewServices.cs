
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.SIM.View;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{
    public class OeeReportViewServices : BaseServices<OeeReportViewEntity>, IOeeReportViewServices
    {
        private readonly IBaseRepository<OeeReportViewEntity> _dal;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _EquipmentEntity;
        public OeeReportViewServices(IBaseRepository<OeeReportViewEntity> dal, IBaseRepository<DFM.Model.Models.EquipmentEntity> EquipmentEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _EquipmentEntity = EquipmentEntity;

        }

        public async Task<List<OeeReportViewEntity>> GetList(OeeReportViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<OeeReportViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<OeeReportViewEntity>> GetPageList(OeeReportViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<OeeReportViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        /// <summary>
        /// 12.3.7	OEE数据（产量+工时+停机） 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<OEEDateMode>> GetOeeDate(OeeReportViewRequestModel reqModel)
        {
            int year = 365;
            if (DateTime.IsLeapYear(reqModel.dateTime.Year))
            {
                year = 366;
            }
            int WorkDay = GetWorkdaysInYear(reqModel.dateTime.Year);//全年工作日

            List<OEEDateMode> oEEDateModes = new List<OEEDateMode>();

            //查询输入年份
            var whereExpression = Expressionable.Create<OeeReportViewEntity>()
                .And(p => p.Year == reqModel.dateTime.Year)
                .And(p => p.Status == 1)
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);

            //查询去年
            var whereExpression1 = Expressionable.Create<OeeReportViewEntity>()
                 .And(p => p.Year == reqModel.dateTime.Year - 1)
                 .And(p => p.Status == 1)
                 .ToExpression();
            var data1 = await _dal.FindList(whereExpression1);

            //查询前年
            var whereExpression2 = Expressionable.Create<OeeReportViewEntity>()
                 .And(p => p.Year == reqModel.dateTime.Year - 2)
                 .And(p => p.Status == 1)
                 .ToExpression();
            var data2 = await _dal.FindList(whereExpression2);

            //包装车间下产线
            var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0 && p.Level == "Line");
            LineModel.OrderBy(p => p.EquipmentName).Select(p => p.EquipmentName).ToList();

            #region L12变量
            //前年
            decimal A0001 = 0;//总产量
            decimal A0002 = 0;//非计划停机
            decimal A0003 = 0;//计划停机
            decimal A0004 = 0;//总产量标准时间
            decimal A0005 = 0;//总运行时间
            decimal A0006 = 0;//良品数
            //去年
            decimal A001 = 0;//总产量
            decimal A002 = 0;//非计划停机
            decimal A003 = 0;//计划停机
            decimal A004 = 0;//总产量标准时间
            decimal A005 = 0;//总运行时间
            decimal A006 = 0;//良品数
            //当年
            decimal A01 = 0;//总产量
            decimal A02 = 0;//非计划停机
            decimal A03 = 0;//计划停机
            decimal A04 = 0;//总产量标准时间
            decimal A05 = 0;//总运行时间
            decimal A06 = 0;//良品数


            decimal A4 = 9;
            decimal A5 = 0;
            decimal A6 = 0;
            decimal A7 = 0;
            decimal Z1 = 0;//良品数
            decimal Z2 = 0;//总数

            decimal A8 = 0;
            decimal A9 = 0;
            decimal A10 = 0;
            decimal A11 = 0;
            decimal Z3 = 0;//良品数
            decimal Z4 = 0;//总数

            decimal A12 = 0;
            decimal A13 = 0;
            decimal A14 = 0;
            decimal A15 = 0;
            decimal Z5 = 0;//良品数
            decimal Z6 = 0;//总数

            decimal A16 = 0;
            decimal A17 = 0;
            decimal A18 = 0;
            decimal A19 = 0;
            decimal Z7 = 0;//良品数
            decimal Z8 = 0;//总数

            decimal A20 = 0;
            decimal A21 = 0;
            decimal A22 = 0;
            decimal A23 = 0;
            decimal Z9 = 0; //良品数
            decimal Z10 = 0;//总数

            decimal A24 = 0;
            decimal A25 = 0;
            decimal A26 = 0;
            decimal A27 = 0;
            decimal Z11 = 0;//良品数
            decimal Z12 = 0;//总数

            decimal A28 = 0;
            decimal A29 = 0;
            decimal A30 = 0;
            decimal A31 = 0;
            decimal Z13 = 0;//良品数
            decimal Z14 = 0;//总数

            decimal A32 = 0;
            decimal A33 = 0;
            decimal A34 = 0;
            decimal A35 = 0;
            decimal Z15 = 0;//良品数
            decimal Z16 = 0;//总数

            decimal A36 = 0;
            decimal A37 = 0;
            decimal A38 = 0;
            decimal A39 = 0;
            decimal Z17 = 0;//良品数
            decimal Z18 = 0;//总数

            decimal A40 = 0;
            decimal A41 = 0;
            decimal A42 = 0;
            decimal A43 = 0;
            decimal Z19 = 0;//良品数
            decimal Z20 = 0;//总数

            decimal A44 = 0;
            decimal A45 = 0;
            decimal A46 = 0;
            decimal A47 = 0;
            decimal Z21 = 0; //良品数
            decimal Z22 = 0; //总数

            decimal A48 = 0;
            decimal A49 = 0;
            decimal A50 = 0;
            decimal A51 = 0;
            decimal Z23 = 0;//良品数
            decimal Z24 = 0;//总数

            decimal A52 = 0;
            decimal A53 = 0;
            decimal A54 = 0;
            decimal A55 = 0;
            decimal Z25 = 0;//良品数
            decimal Z26 = 0;//总数
            #endregion

            #region L14变量
            //前年
            decimal B0001 = 0;//总产量
            decimal B0002 = 0;//非计划停机
            decimal B0003 = 0;//计划停机
            decimal B0004 = 0;//总产量标准时间
            decimal B0005 = 0;//总运行时间
            decimal B0006 = 0;//良品数
            //去年
            decimal B001 = 0;//总产量
            decimal B002 = 0;//非计划停机
            decimal B003 = 0;//计划停机
            decimal B004 = 0;//总产量标准时间
            decimal B005 = 0;//总运行时间
            decimal B006 = 0;//良品数
            //当年
            decimal B01 = 0;//总产量
            decimal B02 = 0;//非计划停机
            decimal B03 = 0;//计划停机
            decimal B04 = 0;//总产量标准时间
            decimal B05 = 0;//总运行时间
            decimal B06 = 0;//良品数


            decimal B4 = 0;
            decimal B5 = 0;
            decimal B6 = 0;
            decimal B7 = 0;
            decimal X1 = 0;//良品数
            decimal X2 = 0;//总数


            decimal B8 = 0;
            decimal B9 = 0;
            decimal B10 = 0;
            decimal B11 = 0;
            decimal X3 = 0;//良品数
            decimal X4 = 0;//总数

            decimal B12 = 0;
            decimal B13 = 0;
            decimal B14 = 0;
            decimal B15 = 0;
            decimal X5 = 0;//良品数
            decimal X6 = 0;//总数

            decimal B16 = 0;
            decimal B17 = 0;
            decimal B18 = 0;
            decimal B19 = 0;
            decimal X7 = 0;//良品数
            decimal X8 = 0;//总数

            decimal B20 = 0;
            decimal B21 = 0;
            decimal B22 = 0;
            decimal B23 = 0;
            decimal X9 = 0;//良品数
            decimal X10 = 0;//总数

            decimal B24 = 0;
            decimal B25 = 0;
            decimal B26 = 0;
            decimal B27 = 0;
            decimal X11 = 0;//良品数
            decimal X12 = 0;//总数

            decimal B28 = 0;
            decimal B29 = 0;
            decimal B30 = 0;
            decimal B31 = 0;
            decimal X13 = 0;//良品数
            decimal X14 = 0;//总数

            decimal B32 = 0;
            decimal B33 = 0;
            decimal B34 = 0;
            decimal B35 = 0;
            decimal X15 = 0;//良品数
            decimal X16 = 0;//总数

            decimal B36 = 0;
            decimal B37 = 0;
            decimal B38 = 0;
            decimal B39 = 0;
            decimal X17 = 0;//良品数
            decimal X18 = 0;//总数

            decimal B40 = 0;
            decimal B41 = 0;
            decimal B42 = 0;
            decimal B43 = 0;
            decimal X19 = 0;//良品数
            decimal X20 = 0;//总数

            decimal B44 = 0;
            decimal B45 = 0;
            decimal B46 = 0;
            decimal B47 = 0;
            decimal X21 = 0;//良品数
            decimal X22 = 0;//总数

            decimal B48 = 0;
            decimal B49 = 0;
            decimal B50 = 0;
            decimal B51 = 0;
            decimal X23 = 0;//良品数
            decimal X24 = 0;//总数

            decimal B52 = 0;
            decimal B53 = 0;
            decimal B54 = 0;
            decimal B55 = 0;
            decimal X25 = 0;//良品数
            decimal X26 = 0;//总数
            #endregion

            #region L19变量
            //前年
            decimal C0001 = 0;//总产量
            decimal C0002 = 0;//非计划停机
            decimal C0003 = 0;//计划停机
            decimal C0004 = 0;//总产量标准时间
            decimal C0005 = 0;//总运行时间
            decimal C0006 = 0;//良品数
            //去年    00
            decimal C001 = 0;//总产量
            decimal C002 = 0;//非计划停机
            decimal C003 = 0;//计划停机
            decimal C004 = 0;//总产量标准时间
            decimal C005 = 0;//总运行时间
            decimal C006 = 0;//良品数
            //当年
            decimal C01 = 0;//总产量
            decimal C02 = 0;//非计划停机
            decimal C03 = 0;//计划停机
            decimal C04 = 0;//总产量标准时间
            decimal C05 = 0;//总运行时间
            decimal C06 = 0;//良品数

            decimal C4 = 0;
            decimal C5 = 0;
            decimal C6 = 0;
            decimal C7 = 0;
            decimal W1 = 0;//良品数
            decimal W2 = 0;//总数

            decimal C8 = 0;
            decimal C9 = 0;
            decimal C10 = 0;
            decimal C11 = 0;
            decimal W3 = 0;//良品数
            decimal W4 = 0;//总数

            decimal C12 = 0;
            decimal C13 = 0;
            decimal C14 = 0;
            decimal C15 = 0;
            decimal W5 = 0;//良品数
            decimal W6 = 0;//总数

            decimal C16 = 0;
            decimal C17 = 0;
            decimal C18 = 0;
            decimal C19 = 0;
            decimal W7 = 0;//良品数
            decimal W8 = 0;//总数

            decimal C20 = 0;
            decimal C21 = 0;
            decimal C22 = 0;
            decimal C23 = 0;
            decimal W9 = 0;//良品数
            decimal W10 = 0;//总数

            decimal C24 = 0;
            decimal C25 = 0;
            decimal C26 = 0;
            decimal C27 = 0;
            decimal W11 = 0;//良品数
            decimal W12 = 0;//总数

            decimal C28 = 0;
            decimal C29 = 0;
            decimal C30 = 0;
            decimal C31 = 0;
            decimal W13 = 0;//良品数
            decimal W14 = 0;//总数

            decimal C32 = 0;
            decimal C33 = 0;
            decimal C34 = 0;
            decimal C35 = 0;
            decimal W15 = 0;//良品数
            decimal W16 = 0;//总数

            decimal C36 = 0;
            decimal C37 = 0;
            decimal C38 = 0;
            decimal C39 = 0;
            decimal W17 = 0;//良品数
            decimal W18 = 0;//总数

            decimal C40 = 0;
            decimal C41 = 0;
            decimal C42 = 0;
            decimal C43 = 0;
            decimal W19 = 0;//良品数
            decimal W20 = 0;//总数

            decimal C44 = 0;
            decimal C45 = 0;
            decimal C46 = 0;
            decimal C47 = 0;
            decimal W21 = 0;//良品数
            decimal W22 = 0;//总数

            decimal C48 = 0;
            decimal C49 = 0;
            decimal C50 = 0;
            decimal C51 = 0;
            decimal W23 = 0;//良品数
            decimal W24 = 0;//总数

            decimal C52 = 0;
            decimal C53 = 0;
            decimal C54 = 0;
            decimal C55 = 0;
            decimal W25 = 0;//良品数
            decimal W26 = 0;//总数
            #endregion

            //循环产线和工作中心
            foreach (var item in LineModel)
            {
                OEEDateMode oEEDateMode = new OEEDateMode();
                List<OEEMonthModel> models = new List<OEEMonthModel>();
                #region 输入年、去年、前年的实际OEE计算
                data = data.Where(p => p.LineId == item.ID).ToList();
                data1 = data1.Where(p => p.LineId == item.ID).ToList();
                data2 = data2.Where(p => p.LineId == item.ID).ToList();
                #region 前年
                //var Before1 = Convert.ToDecimal(data2.Sum(p => p.OeeSpeed));//总产量标准时间
                var Before2 = data2.Sum(p => p.Unplannedtime);//非计划停机
                var Before3 = data2.Sum(p => p.Plannedtime);//计划停机
                var Before5 = data2.Sum(p => p.Runtime);//总运行时间
                var Before6 = data2.Sum(p => p.GoodCount);//良品数/总产量
                List<decimal> BeforeBOMTime = new List<decimal>();
                foreach (var DI in data)
                {
                    decimal k = 0;
                    if (DI.OeeSpeed > 0)
                    {
                        k = DI.GoodCount / Convert.ToDecimal(DI.OeeSpeed);
                    }
                    BeforeBOMTime.Add(k);
                }
                decimal Before4 = BeforeBOMTime.Sum();//总产量标准时间

                decimal val1 = 0;
                decimal val2 = 0;
                decimal val3 = 0;
                if (Before5 != 0 || Before3 != 0 || Before2 != 0)
                {
                    //有效性
                    val1 = Math.Round(Convert.ToDecimal(Before5 / (Before5 + Before3 + Before2)), 3);
                }
                if (Before5 != 0)
                {
                    //表现性
                    val2 = Math.Round(Convert.ToDecimal(Before4 / Before5), 3);
                }
                if (Before6 != 0)
                {
                    //品质性
                    val3 = Before6 / Before6;
                }
                var OEE1 = Math.Round(val1 * val2 * val3, 3);
                if (item.EquipmentName.Contains("L12"))
                {
                    A0001 = Before6;
                    A0002 = Before2;
                    A0003 = Before3;
                    A0004 = Before4;
                    A0005 = Before5;
                    A0006 = Before6;
                }
                if (item.EquipmentName.Contains("L14"))
                {
                    B0001 = Before6;
                    B0002 = Before2;
                    B0003 = Before3;
                    B0004 = Before4;
                    B0005 = Before5;
                    B0006 = Before6;
                }
                if (item.EquipmentName.Contains("L19"))
                {
                    C0001 = Before6;
                    C0002 = Before2;
                    C0003 = Before3;
                    C0004 = Before4;
                    C0005 = Before5;
                    C0006 = Before6;
                }
                #endregion

                #region 去年
                decimal lastval1 = 0;
                decimal lastval2 = 0;
                decimal lastval3 = 0;
                //var Last1 = Convert.ToDecimal(data1.Sum(p => p.OeeSpeed));//总产量标准时间
                var Last2 = data1.Sum(p => p.Unplannedtime);//非计划停机
                var Last3 = data1.Sum(p => p.Plannedtime);//计划停机
                var Last5 = data1.Sum(p => p.Runtime);//总运行时间
                var Last6 = data1.Sum(p => p.GoodCount);//良品/总产量
                List<decimal> lastBOMTime = new List<decimal>();
                foreach (var DI in data)
                {
                    decimal k = 0;
                    if (DI.OeeSpeed > 0)
                    {
                        k = DI.GoodCount / Convert.ToDecimal(DI.OeeSpeed);
                    }
                    lastBOMTime.Add(k);
                }
                decimal Last4 = lastBOMTime.Sum();//总产量标准时间

                if (Last5 != 0 || Last3 != 0 || Last2 != 0)
                {
                    //有效性
                    lastval1 = Math.Round(Convert.ToDecimal(Last5 / (Last5 + Last3 + Last2)), 3);
                }
                if (Last5 != 0)
                {
                    //表现性
                    lastval2 = Math.Round(Convert.ToDecimal(Last4 / Last5), 3);
                }
                if (Last6 != 0)
                {
                    //品质性
                    lastval3 = Last6 / Last6;
                }
                var OEE2 = lastval1 * lastval2 * lastval3;

                if (item.EquipmentName.Contains("L12"))
                {
                    A001 = Last6;
                    A002 = Last2;
                    A003 = Last3;
                    A004 = Last4;
                    A005 = Last5;
                    A006 = Last6;
                }
                if (item.EquipmentName.Contains("L14"))
                {
                    B001 = Last6;
                    B002 = Last2;
                    B003 = Last3;
                    B004 = Last4;
                    B005 = Last5;
                    B006 = Last6;
                }
                if (item.EquipmentName.Contains("L19"))
                {
                    C001 = Last6;
                    C002 = Last2;
                    C003 = Last3;
                    C004 = Last4;
                    C005 = Last5;
                    C006 = Last6;
                }
                #endregion

                #region 当年
                decimal V1 = 0;
                decimal V2 = 0;
                decimal V3 = 0;
               // var YearOee1 = Convert.ToDecimal(data.Sum(p => p.OeeSpeed));//OEE标准时间
                var YearOee2 = data.Sum(p => p.Unplannedtime);//非计划停机
                var YearOee3 = data.Sum(p => p.Plannedtime);//计划停机
                var YearOee5 = data.Sum(p => p.Runtime);//总运行时间
                var YearOee6 = data.Sum(p => p.GoodCount);//良品数/总产量
                List<decimal> totalBOMTime = new List<decimal>();
                foreach (var DI in data)
                {
                    decimal k = 0;
                    if (DI.OeeSpeed > 0)
                    {
                        k = DI.GoodCount / Convert.ToDecimal(DI.OeeSpeed);
                    }
                    totalBOMTime.Add(k);
                }
                decimal YearOee4 = totalBOMTime.Sum();//总产量标准时间

                SerilogServer.LogDebug($"总产量YearOee6：{YearOee6}" + $"非计划停机YearOee2：{YearOee2}" 
                    +$"计划停机YearOee3：{YearOee3}"
                    +$"总产量标准时间：{YearOee4}"
                    +$"总运行时间YearOee5：{YearOee5}"
                    +$"良品数YearOee6：{YearOee6}"
                    +$"产线名称+ID：{item.EquipmentName+item.ID}",
                    "OEEKBDate");
                if (YearOee5 != 0 || YearOee3 != 0 || YearOee2 != 0)
                {
                    //有效性
                    V1 = Math.Round(Convert.ToDecimal(YearOee5 / (YearOee5 + YearOee3 + YearOee2)), 3);
                }
                if (YearOee5 != 0)
                {
                    //表现性
                    V2 = Math.Round(Convert.ToDecimal(YearOee4 / YearOee5), 3);
                }
                if (YearOee6 != 0)
                {
                    //品质性
                    V3 = YearOee6 / YearOee6;
                }
               
                var OEE3 = V1 * V2 * V3;
                SerilogServer.LogDebug($"有效性V1：{V1}" + $"表现性V2：{V2}"
                   + $"品质性：{V3}"
                   +$"OEE:{OEE3}"
                   + $"产线名称+ID：{item.EquipmentName + item.ID}",
                   "OEEKBDate");
                if (item.EquipmentName.Contains("L12"))
                {
                    A01 = YearOee6;
                    A02 = YearOee2;
                    A03 = YearOee3;
                    A04 = YearOee4;
                    A05 = YearOee5;
                    A06 = YearOee6;
                }
                if (item.EquipmentName.Contains("L14"))
                {
                    B01 = YearOee6;
                    B02 = YearOee2;
                    B03 = YearOee3;
                    B04 = YearOee4;
                    B05 = YearOee5;
                    B06 = YearOee6;
                }
                if (item.EquipmentName.Contains("L19"))
                {
                    C01 = YearOee6;
                    C02 = YearOee2;
                    C03 = YearOee3;
                    C04 = YearOee4;
                    C05 = YearOee5;
                    C06 = YearOee6;
                }
                #endregion
                oEEDateMode.Year = reqModel.dateTime.Year;
                oEEDateMode.LineName = item.EquipmentName;
                oEEDateMode.BeforeYearOee = OEE1;
                oEEDateMode.LastYearOee = OEE2;
                oEEDateMode.YearOee = OEE3;

                #endregion

                var month = 12;
                for (int i = 1; i <= month; i++)
                {
                    int workdays = GetCurrentMonthWorkdays(reqModel.dateTime.Year, i);//获取当前查询月的工作日
                    OEEMonthModel oEEMonth = new OEEMonthModel();
                   var oeedata= data.Where(p => p.Month == i).ToList();
                    #region 单月的需要的数据
                    decimal K1 = 0;//有效性
                    decimal K2 = 0;//表现性
                    decimal K3 = 0;//品质性

                    decimal K4 = oeedata.Sum(p => p.Runtime);//运行时长
                    decimal K5 = oeedata.Sum(p => p.Unplannedtime);//非计划停机
                    decimal K6 = oeedata.Sum(p => p.Plannedtime);//计划停机
                    decimal K7 = oeedata.Sum(p => p.GoodCount);//良品
                    decimal K8 = oeedata.Sum(p => p.GoodCount);//总数
                    //decimal K9 = oeedata.Sum(p => p.TotalCount);//总产量标准时间
                    List<decimal> monthBOMTime = new List<decimal>();
                    foreach (var DI in data)
                    {
                        decimal k = 0;
                        if (DI.OeeSpeed > 0)
                        {
                            k = DI.GoodCount / Convert.ToDecimal(DI.OeeSpeed);
                        }
                        monthBOMTime.Add(k);
                    }
                    decimal K9 = monthBOMTime.Sum();//总产量标准时间

                    SerilogServer.LogDebug($"月份：{i}" + $"非计划停机K5：{K5}"
                   + $"计划停机K6：{K6}"
                   + $"总产量标准时间K9：{K9}"
                   + $"总运行时间K4：{K4}"
                   + $"良品数K7：{K7}"
                   + $"产线名称+ID：{item.EquipmentName + item.ID}",
                   "OEEKBDate");
                    #endregion
                    if (K4 != 0 || K5 != 0 || K6 != 0)
                    {
                        //有效性
                        K1 = Math.Round(Convert.ToDecimal(K4 / (K4 + K5 + K6)), 3);
                    }
                    if (K4 != 0)
                    {
                        //表现性
                        K2 = Math.Round(Convert.ToDecimal(K9 / K4), 3);
                    }
                    if (K8 != 0)
                    {
                        //品质性
                        K3 = Math.Round(Convert.ToDecimal(K7 / K8), 3);
                    }
                    var noGood = 0; //不良品暂时无法获取
                    oEEMonth.Type = i + "月";
                    oEEMonth.RunTime = K4;
                    oEEMonth.UnPlannedTime = K5;
                    oEEMonth.PlannedTime = K6;
                    oEEMonth.StdProductionTime = K9;
                    oEEMonth.DefectiveProducts = noGood;
                    oEEMonth.TotalCount = K8;
                    oEEMonth.Validity = K1;
                    oEEMonth.Expression = K2;
                    oEEMonth.Quality = K3;
                    oEEMonth.OEE = K1 * K2 * K3;

                    int days = DateTime.DaysInMonth(reqModel.dateTime.Year, i);
                    oEEMonth.MCU = (K4 + K5 + K6) / (days * 24);
                    oEEMonth.ACU = (K4 + K5 + K6) / (workdays * 24);
                    #region 汇总L12、L14、L19


                    if (item.EquipmentName.Contains("L12"))
                    {
                        switch (oEEMonth.Type)
                        {
                            case "1月":
                                A4 += oEEMonth.RunTime;
                                A5 += oEEMonth.PlannedTime;
                                A6 += oEEMonth.UnPlannedTime;
                                A7 += oEEMonth.StdProductionTime;
                                Z1 += K7;
                                Z2 += K8;
                                break;
                            case "2月":
                                A8 += oEEMonth.RunTime;
                                A9 += oEEMonth.PlannedTime;
                                A10 += oEEMonth.UnPlannedTime;
                                A11 += oEEMonth.StdProductionTime;
                                Z3 += K7;
                                Z4 += K8;
                                break;
                            case "3月":
                                A12 += oEEMonth.RunTime;
                                A13 += oEEMonth.PlannedTime;
                                A14 += oEEMonth.UnPlannedTime;
                                A15 += oEEMonth.StdProductionTime;
                                Z5 += K7;
                                Z6 += K8;
                                break;
                            case "4月":
                                A16 += oEEMonth.RunTime;
                                A17 += oEEMonth.PlannedTime;
                                A18 += oEEMonth.UnPlannedTime;
                                A19 += oEEMonth.StdProductionTime;
                                Z7 += K7;
                                Z8 += K8;
                                break;
                            case "5月":
                                A20 += oEEMonth.RunTime;
                                A21 += oEEMonth.PlannedTime;
                                A22 += oEEMonth.UnPlannedTime;
                                A23 += oEEMonth.StdProductionTime;
                                Z9 += K7;
                                Z10 += K8;
                                break;
                            case "6月":
                                A24 += oEEMonth.RunTime;
                                A25 += oEEMonth.PlannedTime;
                                A26 += oEEMonth.UnPlannedTime;
                                A27 += oEEMonth.StdProductionTime;
                                Z11 += K7;
                                Z12 += K8;
                                break;
                            case "7月":
                                A28 += oEEMonth.RunTime;
                                A29 += oEEMonth.PlannedTime;
                                A30 += oEEMonth.UnPlannedTime;
                                A31 += oEEMonth.StdProductionTime;
                                Z13 += K7;
                                Z14 += K8;
                                break;
                            case "8月":
                                A32 += oEEMonth.RunTime;
                                A33 += oEEMonth.PlannedTime;
                                A34 += oEEMonth.UnPlannedTime;
                                A35 += oEEMonth.StdProductionTime;
                                Z15 += K7;
                                Z16 += K8;
                                break;
                            case "9月":
                                A36 += oEEMonth.RunTime;
                                A37 += oEEMonth.PlannedTime;
                                A38 += oEEMonth.UnPlannedTime;
                                A39 += oEEMonth.StdProductionTime;
                                Z17 += K7;
                                Z18 += K8;
                                break;
                            case "10月":
                                A40 += oEEMonth.RunTime;
                                A41 += oEEMonth.PlannedTime;
                                A42 += oEEMonth.UnPlannedTime;
                                A43 += oEEMonth.StdProductionTime;
                                Z19 += K7;
                                Z20 += K8;
                                break;
                            case "11月":
                                A44 += oEEMonth.RunTime;
                                A45 += oEEMonth.PlannedTime;
                                A46 += oEEMonth.UnPlannedTime;
                                A47 += oEEMonth.StdProductionTime;
                                Z21 += K7;
                                Z22 += K8;
                                break;
                            case "12月":
                                A48 += oEEMonth.RunTime;
                                A49 += oEEMonth.PlannedTime;
                                A50 += oEEMonth.UnPlannedTime;
                                A51 += oEEMonth.StdProductionTime;
                                Z23 += K7;
                                Z24 += K8;
                                break;
                        }
                    }
                    else if (item.EquipmentName.Contains("L14"))
                    {
                        switch (oEEMonth.Type)
                        {
                            case "1月":
                                B4 += oEEMonth.RunTime;
                                B5 += oEEMonth.PlannedTime;
                                B6 += oEEMonth.UnPlannedTime;
                                B7 += oEEMonth.StdProductionTime;
                                X1 += K7;
                                X2 += K8;
                                break;
                            case "2月":
                                B8 += oEEMonth.RunTime;
                                B9 += oEEMonth.PlannedTime;
                                B10 += oEEMonth.UnPlannedTime;
                                B11 += oEEMonth.StdProductionTime;
                                X3 += K7;
                                X4 += K8;
                                break;
                            case "3月":
                                B12 += oEEMonth.RunTime;
                                B13 += oEEMonth.PlannedTime;
                                B14 += oEEMonth.UnPlannedTime;
                                B15 += oEEMonth.StdProductionTime;
                                X5 += K7;
                                X6 += K8;
                                break;
                            case "4月":
                                B16 += oEEMonth.RunTime;
                                B17 += oEEMonth.PlannedTime;
                                B18 += oEEMonth.UnPlannedTime;
                                B19 += oEEMonth.StdProductionTime;
                                X7 += K7;
                                X8 += K8;
                                break;
                            case "5月":
                                B20 += oEEMonth.RunTime;
                                B21 += oEEMonth.PlannedTime;
                                B22 += oEEMonth.UnPlannedTime;
                                B23 += oEEMonth.StdProductionTime;
                                X19 += K7;
                                X10 += K8;
                                break;
                            case "6月":
                                B24 += oEEMonth.RunTime;
                                B25 += oEEMonth.PlannedTime;
                                B26 += oEEMonth.UnPlannedTime;
                                B27 += oEEMonth.StdProductionTime;
                                X11 += K7;
                                X12 += K8;
                                break;
                            case "7月":
                                B28 += oEEMonth.RunTime;
                                B29 += oEEMonth.PlannedTime;
                                B30 += oEEMonth.UnPlannedTime;
                                B31 += oEEMonth.StdProductionTime;
                                X13 += K7;
                                X14 += K8;
                                break;
                            case "8月":
                                B32 += oEEMonth.RunTime;
                                B33 += oEEMonth.PlannedTime;
                                B34 += oEEMonth.UnPlannedTime;
                                B35 += oEEMonth.StdProductionTime;
                                X15 += K7;
                                X16 += K8;
                                break;
                            case "9月":
                                B36 += oEEMonth.RunTime;
                                B37 += oEEMonth.PlannedTime;
                                B38 += oEEMonth.UnPlannedTime;
                                B39 += oEEMonth.StdProductionTime;
                                X17 += K7;
                                X18 += K8;
                                break;
                            case "10月":
                                B40 += oEEMonth.RunTime;
                                B41 += oEEMonth.PlannedTime;
                                B42 += oEEMonth.UnPlannedTime;
                                B43 += oEEMonth.StdProductionTime;
                                X19 += K7;
                                X20 += K8;
                                break;
                            case "11月":
                                B44 += oEEMonth.RunTime;
                                B45 += oEEMonth.PlannedTime;
                                B46 += oEEMonth.UnPlannedTime;
                                B47 += oEEMonth.StdProductionTime;
                                X21 += K7;
                                X22 += K8;
                                break;
                            case "12月":
                                B48 += oEEMonth.RunTime;
                                B49 += oEEMonth.PlannedTime;
                                B50 += oEEMonth.UnPlannedTime;
                                B51 += oEEMonth.StdProductionTime;
                                X23 += K7;
                                X24 += K8;
                                break;
                        }
                    }
                    else if (item.EquipmentName.Contains("L19"))
                    {
                        switch (oEEMonth.Type)
                        {
                            case "1月":
                                C4 += oEEMonth.RunTime;
                                C5 += oEEMonth.PlannedTime;
                                C6 += oEEMonth.UnPlannedTime;
                                C7 += oEEMonth.StdProductionTime;
                                W1 += K7;
                                W2 += K8;
                                break;
                            case "2月":
                                C8 += oEEMonth.RunTime;
                                C9 += oEEMonth.PlannedTime;
                                C10 += oEEMonth.UnPlannedTime;
                                C11 += oEEMonth.StdProductionTime;
                                W3 += K7;
                                W4 += K8;
                                break;
                            case "3月":
                                C12 += oEEMonth.RunTime;
                                C13 += oEEMonth.PlannedTime;
                                C14 += oEEMonth.UnPlannedTime;
                                C15 += oEEMonth.StdProductionTime;
                                W5 += K7;
                                W6 += K8;
                                break;
                            case "4月":
                                C16 += oEEMonth.RunTime;
                                C17 += oEEMonth.PlannedTime;
                                C18 += oEEMonth.UnPlannedTime;
                                C19 += oEEMonth.StdProductionTime;
                                W7 += K7;
                                W8 += K8;
                                break;
                            case "5月":
                                C20 += oEEMonth.RunTime;
                                C21 += oEEMonth.PlannedTime;
                                C22 += oEEMonth.UnPlannedTime;
                                C23 += oEEMonth.StdProductionTime;
                                W9 += K7;
                                W10 += K8;
                                break;
                            case "6月":
                                C24 += oEEMonth.RunTime;
                                C25 += oEEMonth.PlannedTime;
                                C26 += oEEMonth.UnPlannedTime;
                                C27 += oEEMonth.StdProductionTime;
                                W11 += K7;
                                W12 += K8;
                                break;
                            case "7月":
                                C28 += oEEMonth.RunTime;
                                C29 += oEEMonth.PlannedTime;
                                C30 += oEEMonth.UnPlannedTime;
                                C31 += oEEMonth.StdProductionTime;
                                W13 += K7;
                                W14 += K8;
                                break;
                            case "8月":
                                C32 += oEEMonth.RunTime;
                                C33 += oEEMonth.PlannedTime;
                                C34 += oEEMonth.UnPlannedTime;
                                C35 += oEEMonth.StdProductionTime;
                                W15 += K7;
                                W16 += K8;
                                break;
                            case "9月":
                                C36 += oEEMonth.RunTime;
                                C37 += oEEMonth.PlannedTime;
                                C38 += oEEMonth.UnPlannedTime;
                                C39 += oEEMonth.StdProductionTime;
                                W17 += K7;
                                W18 += K8;
                                break;
                            case "10月":
                                C40 += oEEMonth.RunTime;
                                C41 += oEEMonth.PlannedTime;
                                C42 += oEEMonth.UnPlannedTime;
                                C43 += oEEMonth.StdProductionTime;
                                W19 += K7;
                                W20 += K8;
                                break;
                            case "11月":
                                C44 += oEEMonth.RunTime;
                                C45 += oEEMonth.PlannedTime;
                                C46 += oEEMonth.UnPlannedTime;
                                C47 += oEEMonth.StdProductionTime;
                                W21 += K7;
                                W22 += K8;
                                break;
                            case "12月":
                                C48 += oEEMonth.RunTime;
                                C49 += oEEMonth.PlannedTime;
                                C50 += oEEMonth.UnPlannedTime;
                                C51 += oEEMonth.StdProductionTime;
                                W23 += K7;
                                W24 += K8;
                                break;
                        }
                    }
                    #endregion
                    models.Add(oEEMonth);

                }
                OEEMonthModel oEEMonth1 = new OEEMonthModel();

                //汇总需要等计算逻辑
                oEEMonth1.Type = "汇总";
                oEEMonth1.RunTime = YearOee5;
                oEEMonth1.UnPlannedTime = YearOee2;
                oEEMonth1.PlannedTime = YearOee3;
                oEEMonth1.StdProductionTime = YearOee4;
                oEEMonth1.DefectiveProducts = 0;//不良品暂时不能拿到
                oEEMonth1.TotalCount = YearOee6;
                oEEMonth1.Validity = V1;
                oEEMonth1.Expression = V2;//
                oEEMonth1.Quality = V3;
                oEEMonth1.OEE = OEE3;

                oEEMonth1.MCU = (YearOee5 + YearOee3 + YearOee2) / (year * 24);
                oEEMonth1.ACU = (YearOee5 + YearOee3 + YearOee2) / (WorkDay * 24);
                models.Add(oEEMonth1);
                if (item.EquipmentName.Contains("L12"))
                {
                    A52 += oEEMonth1.RunTime;
                    A53 += oEEMonth1.PlannedTime;
                    A54 += oEEMonth1.UnPlannedTime;
                    A55 += oEEMonth1.StdProductionTime;
                    Z25 += oEEMonth1.DefectiveProducts;
                    Z26 += oEEMonth1.TotalCount;
                }
                else if (item.EquipmentName.Contains("L14"))
                {
                    B52 += oEEMonth1.RunTime;
                    B53 += oEEMonth1.PlannedTime;
                    B54 += oEEMonth1.UnPlannedTime;
                    B55 += oEEMonth1.StdProductionTime;
                    Z25 += oEEMonth1.DefectiveProducts;
                    Z26 += oEEMonth1.TotalCount;
                }
                else if (item.EquipmentName.Contains("L19"))
                {
                    C52 += oEEMonth1.RunTime;
                    C53 += oEEMonth1.PlannedTime;
                    C54 += oEEMonth1.UnPlannedTime;
                    C55 += oEEMonth1.StdProductionTime;
                    Z25 += oEEMonth1.DefectiveProducts;
                    Z26 += oEEMonth1.TotalCount;
                }
                oEEDateMode.oEEMonths = models;
                oEEDateModes.Add(oEEDateMode);
            }

            #region L12汇总组合
            List<L12> l12s = new List<L12>();
            L12 L12Model = new L12();
            L12Model.A1 = A4;
            L12Model.A2 = A5;
            L12Model.A3 = A6;
            L12Model.A4 = A7;
            L12Model.A5 = Z1;
            L12Model.A6 = Z2;
            l12s.Add(L12Model);
            L12 L12Model1 = new L12();
            L12Model1.A1 = A8;
            L12Model1.A2 = A9;
            L12Model1.A3 = A10;
            L12Model1.A4 = A11;
            L12Model1.A5 = Z3;
            L12Model1.A6 = Z4;
            l12s.Add(L12Model1);

            L12 L12Model2 = new L12();
            L12Model2.A1 = A12;
            L12Model2.A2 = A13;
            L12Model2.A3 = A14;
            L12Model2.A4 = A15;
            L12Model2.A5 = Z5;
            L12Model2.A6 = Z6;
            l12s.Add(L12Model2);

            L12 L12Model3 = new L12();
            L12Model3.A1 = A16;
            L12Model3.A2 = A17;
            L12Model3.A3 = A18;
            L12Model3.A4 = A19;
            L12Model3.A5 = Z7;
            L12Model3.A6 = Z8;
            l12s.Add(L12Model3);

            L12 L12Model4 = new L12();
            L12Model4.A1 = A20;
            L12Model4.A2 = A21;
            L12Model4.A3 = A22;
            L12Model4.A4 = A23;
            L12Model4.A5 = Z9;
            L12Model4.A6 = Z10;
            l12s.Add(L12Model4);

            L12 L12Model5 = new L12();
            L12Model5.A1 = A24;
            L12Model5.A2 = A25;
            L12Model5.A3 = A26;
            L12Model5.A4 = A27;
            L12Model5.A5 = Z11;
            L12Model5.A6 = Z12;
            l12s.Add(L12Model5);

            L12 L12Model6 = new L12();
            L12Model6.A1 = A28;
            L12Model6.A2 = A29;
            L12Model6.A3 = A30;
            L12Model6.A4 = A31;
            L12Model6.A5 = Z13;
            L12Model6.A6 = Z14;
            l12s.Add(L12Model6);

            L12 L12Model7 = new L12();
            L12Model7.A1 = A32;
            L12Model7.A2 = A33;
            L12Model7.A3 = A34;
            L12Model7.A4 = A35;
            L12Model7.A5 = Z15;
            L12Model7.A6 = Z16;
            l12s.Add(L12Model7);

            L12 L12Model8 = new L12();
            L12Model8.A1 = A36;
            L12Model8.A2 = A37;
            L12Model8.A3 = A38;
            L12Model8.A4 = A39;
            L12Model8.A5 = Z17;
            L12Model8.A6 = Z18;
            l12s.Add(L12Model7);

            L12 L12Model9 = new L12();
            L12Model9.A1 = A40;
            L12Model9.A2 = A41;
            L12Model9.A3 = A42;
            L12Model9.A4 = A43;
            L12Model9.A5 = Z19;
            L12Model9.A6 = Z20;
            l12s.Add(L12Model7);

            L12 L12Mode20 = new L12();
            L12Mode20.A1 = A44;
            L12Mode20.A2 = A45;
            L12Mode20.A3 = A46;
            L12Mode20.A4 = A47;
            L12Mode20.A5 = Z21;
            L12Mode20.A6 = Z22;
            l12s.Add(L12Model7);

            L12 L12Mode21 = new L12();
            L12Mode21.A1 = A48;
            L12Mode21.A2 = A49;
            L12Mode21.A3 = A50;
            L12Mode21.A4 = A51;
            L12Mode21.A5 = Z23;
            L12Mode21.A6 = Z24;
            l12s.Add(L12Mode21);

            L12 L12Mode22 = new L12();
            L12Mode22.A1 = A52;
            L12Mode22.A2 = A53;
            L12Mode22.A3 = A54;
            L12Mode22.A4 = A55;
            L12Mode22.A5 = Z25;
            L12Mode22.A6 = Z26;
            l12s.Add(L12Mode22);
            #endregion
            #region L14汇总组合
            List<L12> l14s = new List<L12>();
            L12 L14Model = new L12();
            L14Model.A1 = B4;
            L14Model.A2 = B5;
            L14Model.A3 = B6;
            L14Model.A4 = B7;
            L14Model.A5 = X1;
            L14Model.A6 = X2;
            l14s.Add(L14Model);
            L12 L14Model1 = new L12();
            L14Model1.A1 = B8;
            L14Model1.A2 = B9;
            L14Model1.A3 = B10;
            L14Model1.A4 = B11;
            L14Model1.A5 = X3;
            L14Model1.A6 = X4;
            l14s.Add(L14Model1);

            L12 L14Model2 = new L12();
            L14Model2.A1 = B12;
            L14Model2.A2 = B13;
            L14Model2.A3 = B14;
            L14Model2.A4 = B15;
            L14Model2.A5 = X5;
            L14Model2.A6 = X6;
            l14s.Add(L14Model2);

            L12 L14Model3 = new L12();
            L14Model3.A1 = B16;
            L14Model3.A2 = B17;
            L14Model3.A3 = B18;
            L14Model3.A4 = B19;
            L14Model3.A5 = X7;
            L14Model3.A6 = X8;
            l14s.Add(L14Model3);

            L12 L14Model4 = new L12();
            L14Model4.A1 = B20;
            L14Model4.A2 = B21;
            L14Model4.A3 = B22;
            L14Model4.A4 = B23;
            L14Model4.A5 = X9;
            L14Model4.A6 = X10;
            l14s.Add(L14Model4);

            L12 L14Model5 = new L12();
            L14Model5.A1 = B24;
            L14Model5.A2 = B25;
            L14Model5.A3 = B26;
            L14Model5.A4 = B27;
            L14Model5.A5 = X11;
            L14Model5.A6 = X12;
            l14s.Add(L14Model5);

            L12 L14Model6 = new L12();
            L14Model6.A1 = B28;
            L14Model6.A2 = B29;
            L14Model6.A3 = B30;
            L14Model6.A4 = B31;
            L14Model6.A5 = X13;
            L14Model6.A6 = X14;
            l14s.Add(L14Model6);

            L12 L14Model7 = new L12();
            L14Model7.A1 = B32;
            L14Model7.A2 = B33;
            L14Model7.A3 = B34;
            L14Model7.A4 = B35;
            L14Model7.A5 = X15;
            L14Model7.A6 = X16;
            l14s.Add(L14Model7);

            L12 L14Model8 = new L12();
            L14Model8.A1 = B36;
            L14Model8.A2 = B37;
            L14Model8.A3 = B38;
            L14Model8.A4 = B39;
            L14Model8.A5 = X17;
            L14Model8.A6 = X18;
            l14s.Add(L14Model7);

            L12 L14Model9 = new L12();
            L14Model9.A1 = B40;
            L14Model9.A2 = B41;
            L14Model9.A3 = B42;
            L14Model9.A4 = B43;
            L14Model9.A5 = X19;
            L14Model9.A6 = X20;
            l14s.Add(L14Model7);

            L12 L14Mode20 = new L12();
            L14Mode20.A1 = B44;
            L14Mode20.A2 = B45;
            L14Mode20.A3 = B46;
            L14Mode20.A4 = B47;
            L14Mode20.A5 = X21;
            L14Mode20.A6 = X22;
            l14s.Add(L14Model7);

            L12 L14Mode21 = new L12();
            L14Mode21.A1 = B48;
            L14Mode21.A2 = B49;
            L14Mode21.A3 = B50;
            L14Mode21.A4 = B51;
            L14Mode21.A5 = X23;
            L14Mode21.A6 = X24;
            l14s.Add(L14Mode21);

            L12 L14Mode22 = new L12();
            L14Mode22.A1 = B52;
            L14Mode22.A2 = B53;
            L14Mode22.A3 = B54;
            L14Mode22.A4 = B55;
            L14Mode22.A5 = X25;
            L14Mode22.A6 = X26;
            l14s.Add(L14Mode22);
            #endregion
            #region L19汇总组合
            List<L12> l19s = new List<L12>();
            L12 L19Model = new L12();
            L19Model.A1 = C4;
            L19Model.A2 = C5;
            L19Model.A3 = C6;
            L19Model.A4 = C7;
            L19Model.A5 = W1;
            L19Model.A6 = W2;
            l19s.Add(L19Model);

            L12 L19Model1 = new L12();
            L19Model1.A1 = C8;
            L19Model1.A2 = C9;
            L19Model1.A3 = C10;
            L19Model1.A4 = C11;
            L19Model1.A5 = W3;
            L19Model1.A6 = W4;
            l19s.Add(L19Model1);

            L12 L19Model2 = new L12();
            L19Model2.A1 = C12;
            L19Model2.A2 = C13;
            L19Model2.A3 = C14;
            L19Model2.A4 = C15;
            L19Model2.A5 = W5;
            L19Model2.A6 = W6;
            l19s.Add(L19Model2);

            L12 L19Model3 = new L12();
            L19Model3.A1 = C16;
            L19Model3.A2 = C17;
            L19Model3.A3 = C18;
            L19Model3.A4 = C19;
            L19Model3.A5 = W7;
            L19Model3.A6 = W8;
            l19s.Add(L19Model3);

            L12 L19Model4 = new L12();
            L19Model4.A1 = C20;
            L19Model4.A2 = C21;
            L19Model4.A3 = C22;
            L19Model4.A4 = C23;
            L19Model4.A5 = W9;
            L19Model4.A6 = W10;
            l19s.Add(L19Model4);

            L12 L19Model5 = new L12();
            L19Model5.A1 = C24;
            L19Model5.A2 = C25;
            L19Model5.A3 = C26;
            L19Model5.A4 = C27;
            L19Model5.A5 = W11;
            L19Model5.A6 = W12;
            l19s.Add(L19Model5);

            L12 L19Model6 = new L12();
            L19Model6.A1 = C28;
            L19Model6.A2 = C29;
            L19Model6.A3 = C30;
            L19Model6.A4 = C31;
            L19Model6.A5 = W13;
            L19Model6.A6 = W14;
            l19s.Add(L19Model6);

            L12 L19Model7 = new L12();
            L19Model7.A1 = C32;
            L19Model7.A2 = C33;
            L19Model7.A3 = C34;
            L19Model7.A4 = C35;
            L19Model7.A5 = W15;
            L19Model7.A6 = W16;
            l19s.Add(L19Model7);

            L12 L19Model8 = new L12();
            L19Model8.A1 = C36;
            L19Model8.A2 = C37;
            L19Model8.A3 = C38;
            L19Model8.A4 = C39;
            L19Model8.A5 = W17;
            L19Model8.A6 = W18;
            l19s.Add(L19Model7);

            L12 L19Model9 = new L12();
            L19Model9.A1 = C40;
            L19Model9.A2 = C41;
            L19Model9.A3 = C42;
            L19Model9.A4 = C43;
            L19Model9.A5 = W19;
            L19Model9.A6 = W20;
            l19s.Add(L19Model7);

            L12 L19Mode20 = new L12();
            L19Mode20.A1 = C44;
            L19Mode20.A2 = C45;
            L19Mode20.A3 = C46;
            L19Mode20.A4 = C47;
            L19Mode20.A5 = W21;
            L19Mode20.A6 = W22;
            l19s.Add(L19Model7);

            L12 L19Mode21 = new L12();
            L19Mode21.A1 = C48;
            L19Mode21.A2 = C49;
            L19Mode21.A3 = C50;
            L19Mode21.A4 = C51;
            L19Mode21.A5 = W23;
            L19Mode21.A6 = W24;
            l19s.Add(L19Mode21);

            L12 L19Mode22 = new L12();
            L19Mode22.A1 = C52;
            L19Mode22.A2 = C53;
            L19Mode22.A3 = C54;
            L19Mode22.A4 = C55;
            L19Mode22.A5 = W25;
            L19Mode22.A6 = W26;
            l19s.Add(L19Mode22);
            #endregion
            var l12 = oEEDateModes.Where(p => p.LineName.Contains("L12")).ToList();
            var L14 = oEEDateModes.Where(p => p.LineName.Contains("L14")).ToList();
            var L19 = oEEDateModes.Where(p => p.LineName.Contains("L19")).ToList();

            #region L12线汇总
            #region 前年数据汇总
            decimal O1 = 0;
            decimal O2 = 0;
            decimal O3 = 0;
            //有效性
            if (A0005 != 0 || A0002 != 0 || A0003 != 0)
            {
                O1 = A0005 / (A0005 + A0002 + A0003);
            }
            //表现性
            if (A0005 != 0)
            {
                O2 = A0004 / A0005;
            }
            //品质性
            if (A0001 != 0)
            {
                O3 = A0006 / A0001;
            }
            #endregion
            #region 去年数据汇总
            decimal p1 = 0;
            decimal p2 = 0;
            decimal p3 = 0;
            //有效性
            if (A005 != 0 || A002 != 0 || A003 != 0)
            {
                p1 = A005 / (A005 + A002 + A003);
            }
            //表现性
            if (A005 != 0)
            {
                p2 = A004 / A005;
            }
            //品质性
            if (A001 != 0)
            {
                p3 = A006 / A001;
            }
            #endregion
            #region 当前查询年数据汇总
            decimal q1 = 0;
            decimal q2 = 0;
            decimal q3 = 0;
            //有效性
            if (A05 != 0 || A02 != 0 || A03 != 0)
            {
                q1 = A05 / (A05 + A02 + A03);
            }
            //表现性
            if (A05 != 0)
            {
                q2 = A04 / A05;
            }
            //品质性
            if (A01 != 0)
            {
                q3 = A06 / A01;
            }
            #endregion
            OEEDateMode oEEDateMode1 = new OEEDateMode();
            List<OEEMonthModel> models1 = new List<OEEMonthModel>();
            oEEDateMode1.Year = reqModel.dateTime.Year;
            oEEDateMode1.LineName = "L12总计";
            oEEDateMode1.BeforeYearOee = l12.Sum(P => P.BeforeYearOee);
            oEEDateMode1.LastYearOee = l12.Sum(P => P.LastYearOee);
            oEEDateMode1.YearOee = l12.Sum(P => P.YearOee);
            for (int i = 0; i < l12s.Count; i++)
            {
                OEEMonthModel oEEMonth2 = new OEEMonthModel();
                decimal L12oee1 = 0;//有效性
                decimal L12oee2 = 0;//表现性
                decimal L12oee3 = 0;//品质性
                if (i + 1 == 13)
                {
                    oEEMonth2.Type = "汇总";
                    oEEMonth2.RunTime = l12s[i].A1;
                    oEEMonth2.PlannedTime = l12s[i].A2;
                    oEEMonth2.UnPlannedTime = l12s[i].A3;
                    oEEMonth2.StdProductionTime = l12s[i].A4;
                    oEEMonth2.DefectiveProducts = l12s[i].A5;
                    oEEMonth2.TotalCount = l12s[i].A6;

                    if (l12s[i].A1 != 0 || l12s[i].A2 != 0 || l12s[i].A3 != 0)
                    {
                        L12oee1 = l12s[i].A1 / (l12s[i].A1 + l12s[i].A2 + l12s[i].A3);
                    }
                    oEEMonth2.Validity = L12oee1;
                    if (l12s[i].A1 != 0)
                    {
                        L12oee2 = l12s[i].A4 / l12s[i].A1;
                    }
                    oEEMonth2.Expression = L12oee2;
                    if (l12s[i].A6 != 0)
                    {
                        L12oee3 = (l12s[i].A6 - l12s[i].A5) / l12s[i].A6;
                    }
                    oEEMonth2.Quality = L12oee3;
                    oEEMonth2.OEE = L12oee1 * L12oee2 * L12oee3;
                    oEEMonth2.MCU = (l12s[i].A1 + l12s[i].A2 + l12s[i].A3) / (year * 24);
                    oEEMonth2.ACU = (l12s[i].A1 + l12s[i].A2 + l12s[i].A3) / (WorkDay * 24);
                }
                else
                {
                    int days = DateTime.DaysInMonth(reqModel.dateTime.Year, i + 1);
                    int workdays = GetCurrentMonthWorkdays(reqModel.dateTime.Year, i + 1);//获取当前查询月的工作日
                    oEEMonth2.Type = i + 1 + "月";
                    oEEMonth2.RunTime = l12s[i].A1;
                    oEEMonth2.PlannedTime = l12s[i].A2;
                    oEEMonth2.UnPlannedTime = l12s[i].A3;
                    oEEMonth2.StdProductionTime = l12s[i].A4;
                    oEEMonth2.DefectiveProducts = l12s[i].A6 - l12s[i].A5;
                    oEEMonth2.TotalCount = l12s[i].A6;
                    /*decimal L12oee1 = 0;//有效性
                    decimal L12oee2 = 0;//表现性
                    decimal L12oee3 = 0;//品质性*/
                    if (l12s[i].A1 != 0 || l12s[i].A2 != 0 || l12s[i].A3 != 0)
                    {
                        L12oee1 = l12s[i].A1 / (l12s[i].A1 + l12s[i].A2 + l12s[i].A3);
                    }
                    oEEMonth2.Validity = L12oee1;
                    if (l12s[i].A1 != 0)
                    {
                        L12oee2 = l12s[i].A4 / l12s[i].A1;
                    }
                    oEEMonth2.Expression = L12oee2;
                    if (Z2 != 0)
                    {
                        L12oee3 = l12s[i].A5 / l12s[i].A6;
                    }
                    oEEMonth2.Quality = L12oee3;
                    oEEMonth2.OEE = L12oee1 * L12oee2 * L12oee3;
                    oEEMonth2.MCU = (l12s[i].A1 + l12s[i].A2 + l12s[i].A3) / (days * 24);
                    oEEMonth2.ACU = (l12s[i].A1 + l12s[i].A2 + l12s[i].A3) / (workdays * 24);
                }
                models1.Add(oEEMonth2);
            }
            oEEDateMode1.oEEMonths = models1;
            oEEDateModes.Add(oEEDateMode1);
            #endregion
            #region L14线汇总
            #region 前年数据汇总
            decimal r1 = 0;
            decimal r2 = 0;
            decimal r3 = 0;
            //有效性
            if (B0005 != 0 || B0002 != 0 || B0003 != 0)
            {
                r1 = B0005 / (B0005 + B0002 + B0003);
            }
            //表现性
            if (B0005 != 0)
            {
                r2 = B0004 / B0005;
            }
            //品质性
            if (B0001 != 0)
            {
                r3 = B0006 / B0001;
            }
            #endregion
            #region 去年数据汇总
            decimal s1 = 0;
            decimal s2 = 0;
            decimal s3 = 0;
            //有效性
            if (A005 != 0 || A002 != 0 || A003 != 0)
            {
                s1 = A005 / (A005 + A002 + A003);
            }
            //表现性
            if (A005 != 0)
            {
                s2 = A004 / A005;
            }
            //品质性
            if (A001 != 0)
            {
                s3 = A006 / A001;
            }
            #endregion
            #region 当前查询数据汇总
            decimal t1 = 0;
            decimal t2 = 0;
            decimal t3 = 0;
            //有效性
            if (B05 != 0 || B02 != 0 || B03 != 0)
            {
                t1 = B05 / (B05 + B02 + B03);
            }
            //表现性
            if (B05 != 0)
            {
                t2 = B04 / B05;
            }
            //品质性
            if (B01 != 0)
            {
                t3 = B06 / B01;
            }
            #endregion

            /*var B1 = l12.Sum(P => P.YearOee);
            var B2 = l12.Sum(P => P.LastYearOee);
            var B3 = l12.Sum(P => P.BeforeYearOee);*/
            OEEDateMode L14Models = new OEEDateMode();
            List<OEEMonthModel> L14ModelList = new List<OEEMonthModel>();
            L14Models.Year = reqModel.dateTime.Year;
            L14Models.LineName = "L14总计";
            L14Models.BeforeYearOee = L14.Sum(P => P.BeforeYearOee);
            L14Models.LastYearOee = L14.Sum(P => P.LastYearOee);
            L14Models.YearOee = L14.Sum(P => P.YearOee);

            for (int i = 0; i < l14s.Count; i++)
            {
                OEEMonthModel oEEMonth2 = new OEEMonthModel();
                decimal L14oee1 = 0;//有效性
                decimal L14oee2 = 0;//表现性
                decimal L14oee3 = 0;//品质性
                if (i + 1 == 13)
                {
                    oEEMonth2.Type = "汇总";
                    oEEMonth2.RunTime = l14s[i].A1;
                    oEEMonth2.PlannedTime = l14s[i].A2;
                    oEEMonth2.UnPlannedTime = l14s[i].A3;
                    oEEMonth2.StdProductionTime = l14s[i].A4;
                    oEEMonth2.DefectiveProducts = l14s[i].A5;
                    oEEMonth2.TotalCount = l14s[i].A6;

                    if (l14s[i].A1 != 0 || l14s[i].A2 != 0 || l14s[i].A3 != 0)
                    {
                        L14oee1 = l14s[i].A1 / (l14s[i].A1 + l14s[i].A2 + l14s[i].A3);
                    }
                    oEEMonth2.Validity = L14oee1;
                    if (l14s[i].A1 != 0)
                    {
                        L14oee2 = l14s[i].A4 / l14s[i].A1;
                    }
                    oEEMonth2.Expression = L14oee2;
                    if (l14s[i].A6 != 0)
                    {
                        L14oee3 = (l14s[i].A6 - l14s[i].A5) / l14s[i].A6;
                    }
                    oEEMonth2.Quality = L14oee3;
                    oEEMonth2.OEE = L14oee1 * L14oee2 * L14oee3;
                    oEEMonth2.MCU = (l14s[i].A1 + l14s[i].A2 + l14s[i].A3) / (year * 24);
                    oEEMonth2.ACU = (l14s[i].A1 + l14s[i].A2 + l14s[i].A3) / (WorkDay * 24);
                }
                else
                {
                    int days = DateTime.DaysInMonth(reqModel.dateTime.Year, i + 1);
                    int workdays = GetCurrentMonthWorkdays(reqModel.dateTime.Year, i + 1);//获取当前查询月的工作日
                    oEEMonth2.Type = i + 1 + "月";
                    oEEMonth2.RunTime = l14s[i].A1;
                    oEEMonth2.PlannedTime = l14s[i].A2;
                    oEEMonth2.UnPlannedTime = l14s[i].A3;
                    oEEMonth2.StdProductionTime = l14s[i].A4;
                    oEEMonth2.DefectiveProducts = l14s[i].A6 - l14s[i].A5;
                    oEEMonth2.TotalCount = l14s[i].A6;
                    /*decimal L12oee1 = 0;//有效性
                    decimal L12oee2 = 0;//表现性
                    decimal L12oee3 = 0;//品质性*/
                    if (l14s[i].A1 != 0 || l14s[i].A2 != 0 || l14s[i].A3 != 0)
                    {
                        L14oee1 = l14s[i].A1 / (l14s[i].A1 + l14s[i].A2 + l14s[i].A3);
                    }
                    oEEMonth2.Validity = L14oee1;
                    if (l14s[i].A1 != 0)
                    {
                        L14oee2 = l14s[i].A4 / l14s[i].A1;
                    }
                    oEEMonth2.Expression = L14oee2;
                    if (Z2 != 0)
                    {
                        L14oee3 = l14s[i].A5 / l14s[i].A6;
                    }
                    oEEMonth2.Quality = L14oee3;
                    oEEMonth2.OEE = L14oee1 * L14oee2 * L14oee3;
                    oEEMonth2.MCU = (l14s[i].A1 + l14s[i].A2 + l14s[i].A3) / (days * 24);
                    oEEMonth2.ACU = (l14s[i].A1 + l14s[i].A2 + l14s[i].A3) / (workdays * 24);
                }
                L14ModelList.Add(oEEMonth2);
            }
            L14Models.oEEMonths = L14ModelList;
            oEEDateModes.Add(L14Models);
            #endregion
            #region L19线汇总
            #region 前年数据汇总
            decimal u1 = 0;
            decimal u2 = 0;
            decimal u3 = 0;
            //有效性
            if (B0005 != 0 || B0002 != 0 || B0003 != 0)
            {
                u1 = B0005 / (B0005 + B0002 + B0003);
            }
            //表现性
            if (B0005 != 0)
            {
                u2 = B0004 / B0005;
            }
            //品质性
            if (B0001 != 0)
            {
                u3 = B0006 / B0001;
            }
            #endregion
            #region 去年数据汇总
            decimal vv1 = 0;
            decimal vv2 = 0;
            decimal vv3 = 0;
            //有效性
            if (A005 != 0 || A002 != 0 || A003 != 0)
            {
                vv1 = A005 / (A005 + A002 + A003);
            }
            //表现性
            if (A005 != 0)
            {
                vv2 = A004 / A005;
            }
            //品质性
            if (A001 != 0)
            {
                vv3 = A006 / A001;
            }
            #endregion
            #region 当前查询年数据汇总
            decimal ww1 = 0;
            decimal ww2 = 0;
            decimal ww3 = 0;
            //有效性
            if (C05 != 0 || C02 != 0 || C03 != 0)
            {
                ww1 = C05 / (C05 + C02 + C03);
            }
            //表现性
            if (C05 != 0)
            {
                ww2 = C04 / C05;
            }
            //品质性
            if (C01 != 0)
            {
                ww3 = C06 / C01;
            }
            #endregion

            OEEDateMode L19Models = new OEEDateMode();
            List<OEEMonthModel> L19ModelList = new List<OEEMonthModel>();
            L19Models.Year = reqModel.dateTime.Year;
            L19Models.LineName = "L19总计";
            L19Models.BeforeYearOee = L19.Sum(P => P.BeforeYearOee);
            L19Models.LastYearOee = L19.Sum(P => P.LastYearOee);
            L19Models.YearOee = L19.Sum(P => P.YearOee);

            for (int i = 0; i < l19s.Count; i++)
            {
                OEEMonthModel oEEMonth2 = new OEEMonthModel();
                decimal L19oee1 = 0;//有效性pp
                decimal L19oee2 = 0;//表现性
                decimal L19oee3 = 0;//品质性
                if (i + 1 == 13)
                {
                    oEEMonth2.Type = "汇总";
                    oEEMonth2.RunTime = l19s[i].A1;
                    oEEMonth2.PlannedTime = l19s[i].A2;
                    oEEMonth2.UnPlannedTime = l19s[i].A3;
                    oEEMonth2.StdProductionTime = l19s[i].A4;
                    oEEMonth2.DefectiveProducts = l19s[i].A5;
                    oEEMonth2.TotalCount = l19s[i].A6;

                    if (l19s[i].A1 != 0 || l19s[i].A2 != 0 || l19s[i].A3 != 0)
                    {
                        L19oee1 = l19s[i].A1 / (l19s[i].A1 + l19s[i].A2 + l19s[i].A3);
                    }
                    oEEMonth2.Validity = L19oee1;
                    if (l19s[i].A1 != 0)
                    {
                        L19oee2 = l19s[i].A4 / l19s[i].A1;
                    }
                    oEEMonth2.Expression = L19oee2;
                    if (l19s[i].A6 != 0)
                    {
                        L19oee3 = (l19s[i].A6 - l19s[i].A5) / l19s[i].A6;
                    }
                    oEEMonth2.Quality = L19oee3;
                    oEEMonth2.OEE = L19oee1 * L19oee2 * L19oee3;
                    oEEMonth2.MCU = (l19s[i].A1 + l19s[i].A2 + l19s[i].A3) / (year * 24);
                    oEEMonth2.ACU = (l19s[i].A1 + l19s[i].A2 + l19s[i].A3) / (WorkDay * 24);
                }
                else
                {
                    int days = DateTime.DaysInMonth(reqModel.dateTime.Year, i + 1);
                    int workdays = GetCurrentMonthWorkdays(reqModel.dateTime.Year, i + 1);//获取当前查询月的工作日
                    oEEMonth2.Type = i + 1 + "月";
                    oEEMonth2.RunTime = l19s[i].A1;
                    oEEMonth2.PlannedTime = l19s[i].A2;
                    oEEMonth2.UnPlannedTime = l19s[i].A3;
                    oEEMonth2.StdProductionTime = l19s[i].A4;
                    oEEMonth2.DefectiveProducts = l19s[i].A6 - l19s[i].A5;
                    oEEMonth2.TotalCount = l19s[i].A6;
                    /*decimal L12oee1 = 0;//有效性
                    decimal L12oee2 = 0;//表现性
                    decimal L12oee3 = 0;//品质性*/
                    if (l19s[i].A1 != 0 || l19s[i].A2 != 0 || l19s[i].A3 != 0)
                    {
                        L19oee1 = l19s[i].A1 / (l19s[i].A1 + l19s[i].A2 + l19s[i].A3);
                    }
                    oEEMonth2.Validity = L19oee1;
                    if (l19s[i].A1 != 0)
                    {
                        L19oee2 = l19s[i].A4 / l19s[i].A1;
                    }
                    oEEMonth2.Expression = L19oee2;
                    if (Z2 != 0)
                    {
                        L19oee3 = l19s[i].A5 / l19s[i].A6;
                    }
                    oEEMonth2.Quality = L19oee3;
                    oEEMonth2.OEE = L19oee1 * L19oee2 * L19oee3;
                    oEEMonth2.MCU = (l19s[i].A1 + l19s[i].A2 + l19s[i].A3) / (days * 24);
                    oEEMonth2.ACU = (l19s[i].A1 + l19s[i].A2 + l19s[i].A3) / (workdays * 24);
                }
                L19ModelList.Add(oEEMonth2);
            }
            L19Models.oEEMonths = L19ModelList;
            oEEDateModes.Add(L19Models);
            #endregion
            return oEEDateModes;

        }

        public static int GetCurrentMonthWorkdays(int Year, int Month)
        {
            int workdays = 0;
            DateTime startOfMonth = new DateTime(Year, Month, 1);
            DateTime endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            for (DateTime date = startOfMonth; date <= endOfMonth; date = date.AddDays(1))
            {
                if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
                {
                    workdays++;
                }
            }
            return workdays;
        }
        public static int GetWorkdaysInYear(int year)
        {
            DateTime startOfYear = new DateTime(year, 1, 1);
            DateTime endOfYear = new DateTime(year, 12, 31);

            Calendar calendar = CultureInfo.CurrentCulture.Calendar;

            int workdays = 0;
            for (DateTime date = startOfYear; date <= endOfYear; date = date.AddDays(1))
            {
                if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
                {
                    workdays++;
                }
            }

            return workdays;
        }
        public class L12
        {
            public decimal A1 { get; set; }
            public decimal A2 { get; set; }
            public decimal A3 { get; set; }
            public decimal A4 { get; set; }
            public decimal A5 { get; set; }
            public decimal A6 { get; set; }
        }

        public async Task<bool> SaveForm(OeeReportViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}