using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices.WMS;
using SEFA.PPM.Model.Models.WMS;
using SEFA.PPM.Model.ViewModels.WMS;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.PPM.Services.WMS
{
    /// <summary>
    /// 叫料单服务实现
    /// </summary>
    public class CallMaterialSheetServices : BaseServices<CallMaterialSheetEntity>, ICallMaterialSheetServices
    {
        private readonly ICallMaterialSheetRepository _callMaterialSheetRepository;
        private readonly ICallMaterialDetailRepository _callMaterialDetailRepository;
        private readonly IUnitOfWork _unitOfWork;

        public CallMaterialSheetServices(
            ICallMaterialSheetRepository callMaterialSheetRepository,
            ICallMaterialDetailRepository callMaterialDetailRepository,
            IUnitOfWork unitOfWork)
        {
            _callMaterialSheetRepository = callMaterialSheetRepository;
            _callMaterialDetailRepository = callMaterialDetailRepository;
            _unitOfWork = unitOfWork;
            BaseDal = callMaterialSheetRepository;
        }

        /// <summary>
        /// 新增叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddCallMaterialSheet(CallMaterialSheetRequestModel request)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 生成叫料单号
                string callOrderNo = GenerateCallOrderNo();

                // 创建叫料单主表
                var sheet = new CallMaterialSheetEntity
                {
                    CallOrderNo = callOrderNo,
                    ProductionOrderId = request.ProductionOrderId,
                    CallerId = request.CallerId,
                    CallTime = request.CallTime,
                    LineSideWarehouse = request.LineSideWarehouse,
                    CallPoint = request.CallPoint,
                    CallStatus = "1", // 已叫料
                    Remark = request.Remark,
                    Deleted = 0
                };

                sheet.Create(sheet.CreateUserId);
                var sheetResult = await _callMaterialSheetRepository.Add(sheet);

                if (sheetResult <= 0)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 创建叫料单明细
                if (request.Details != null && request.Details.Any())
                {
                    var details = new List<CallMaterialDetailEntity>();
                    int seq = 1;
                    foreach (var detail in request.Details)
                    {
                        var detailEntity = new CallMaterialDetailEntity
                        {
                            CallSheetId = sheet.ID,
                            CallDetailNo = $"{callOrderNo}-{seq++}",
                            MaterialId = detail.MaterialId,
                            MaterialCode = detail.MaterialCode,
                            MaterialName = detail.MaterialName,
                            Specification = detail.Specification,
                            Unit = detail.Unit,
                            RequiredQuantity = detail.RequiredQuantity,
                            ActualQuantity = detail.ActualQuantity,
                            Remark = detail.Remark,
                            Deleted = 0
                        };
                        detailEntity.Create(sheet.CreateUserId);
                        details.Add(detailEntity);
                    }

                    var detailResults = await _callMaterialDetailRepository.Add(details);
                    if (detailResults <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        return false;
                    }
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 修改叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateCallMaterialSheet(CallMaterialSheetRequestModel request)
        {
            if (string.IsNullOrEmpty(request.ID))
                return false;

            _unitOfWork.BeginTran();
            try
            {
                // 更新叫料单主表
                var sheet = await _callMaterialSheetRepository.QueryById(request.ID);
                if (sheet == null)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                sheet.ProductionOrderId = request.ProductionOrderId;
                sheet.CallerId = request.CallerId;
                sheet.CallTime = request.CallTime;
                sheet.LineSideWarehouse = request.LineSideWarehouse;
                sheet.CallPoint = request.CallPoint;
                sheet.CallStatus = request.CallStatus;
                sheet.Remark = request.Remark;
                sheet.Modify(request.ID, sheet.ModifyUserId);

                var sheetResult = await _callMaterialSheetRepository.Update(sheet);
                if (!sheetResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 删除原有明细
                var deleteExpression = Expressionable.Create<CallMaterialDetailEntity>()
                    .And(d => d.CallSheetId == request.ID)
                    .ToExpression();
                await _callMaterialDetailRepository.Delete(deleteExpression);

                // 创建新的明细
                if (request.Details != null && request.Details.Any())
                {
                    var details = new List<CallMaterialDetailEntity>();
                    int seq = 1;
                    foreach (var detail in request.Details)
                    {
                        var detailEntity = new CallMaterialDetailEntity
                        {
                            CallSheetId = sheet.ID,
                            CallDetailNo = $"{sheet.CallOrderNo}-{seq++}",
                            MaterialId = detail.MaterialId,
                            MaterialCode = detail.MaterialCode,
                            MaterialName = detail.MaterialName,
                            Specification = detail.Specification,
                            Unit = detail.Unit,
                            RequiredQuantity = detail.RequiredQuantity,
                            ActualQuantity = detail.ActualQuantity,
                            Remark = detail.Remark,
                            Deleted = 0
                        };
                        detailEntity.Create(sheet.CreateUserId);
                        details.Add(detailEntity);
                    }

                    var detailResults = await _callMaterialDetailRepository.Add(details);
                    if (detailResults <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        return false;
                    }
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除叫料单（逻辑删除）
        /// </summary>
        /// <param name="ids">叫料单ID列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteCallMaterialSheet(string[] ids)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 逻辑删除叫料单主表
                var sheetExpression = Expressionable.Create<CallMaterialSheetEntity>()
                    .And(s => ids.Contains(s.ID))
                    .ToExpression();
                var sheets = await _callMaterialSheetRepository.FindList(sheetExpression);
                foreach (var sheet in sheets)
                {
                    sheet.Deleted = 1;
                    sheet.Modify(sheet.ID, sheet.ModifyUserId);
                }
                await _callMaterialSheetRepository.Update(sheets);

                // 逻辑删除叫料单明细表
                var detailExpression = Expressionable.Create<CallMaterialDetailEntity>()
                    .And(d => ids.Contains(d.CallSheetId))
                    .ToExpression();
                var details = await _callMaterialDetailRepository.FindList(detailExpression);
                foreach (var detail in details)
                {
                    detail.Deleted = 1;
                    detail.Modify(detail.ID, detail.ModifyUserId);
                }
                await _callMaterialDetailRepository.Update(details);

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 分页查询叫料单
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageModel<CallMaterialHeaderView>> GetPageList(CallMaterialHeaderRequestModel request)
        {
            PageModel<CallMaterialHeaderView> result = new PageModel<CallMaterialHeaderView>();
            
            var query = _callMaterialSheetRepository.Db.Queryable<CallMaterialSheetEntity>();
            
            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(request.WorkOrder), s => s.ProductionOrderId.Contains(request.WorkOrder))
                         .WhereIF(!string.IsNullOrEmpty(request.CallOrderNo), s => s.CallOrderNo.Contains(request.CallOrderNo))
                         .WhereIF(!string.IsNullOrEmpty(request.CallerId), s => s.CallerId.Contains(request.CallerId))
                         .WhereIF(!string.IsNullOrEmpty(request.CallPoint), s => s.CallPoint.Contains(request.CallPoint))
                         .WhereIF(!string.IsNullOrEmpty(request.LineSideWarehouse), s => s.LineSideWarehouse.Contains(request.LineSideWarehouse))
                         .WhereIF(request.StartTime != null && request.EndTime != null, 
                                  s => s.CallTime >= request.StartTime && s.CallTime <= request.EndTime)
                         .WhereIF(request.CallMaterialStatus == "0", s => s.CallStatus == "1") // 根据实际状态值调整
                         .Where(s => s.Deleted == 0); // 逻辑删除过滤

            var data = await query.Select(s => new CallMaterialHeaderView
            {
                ID = s.ID,
                CallOrderNo = s.CallOrderNo,
                ProductionOrderId = s.ProductionOrderId,
                CallerId = s.CallerId,
                CallTime = s.CallTime,
                LineSideWarehouse = s.LineSideWarehouse,
                CallPoint = s.CallPoint,
                CallStatus = s.CallStatus,
                Remark = s.Remark
            }).ToPageListAsync(request.pageIndex, request.pageSize, result.dataCount);

            result.data = data;
            return result;
        }

        /// <summary>
        /// 获取叫料单详情
        /// </summary>
        /// <param name="id">叫料单ID</param>
        /// <returns>叫料单详情</returns>
        public async Task<CallMaterialSheetViewModel> GetCallMaterialSheetById(string id)
        {
            var sheet = await _callMaterialSheetRepository.QueryById(id);
            if (sheet == null || sheet.Deleted == 1)
                return null;

            var details = await _callMaterialDetailRepository.FindList(d => d.CallSheetId == id && d.Deleted == 0);

            return new CallMaterialSheetViewModel
            {
                ID = sheet.ID,
                CallOrderNo = sheet.CallOrderNo,
                ProductionOrderId = sheet.ProductionOrderId,
                CallerId = sheet.CallerId,
                CallTime = sheet.CallTime,
                LineSideWarehouse = sheet.LineSideWarehouse,
                CallPoint = sheet.CallPoint,
                CallStatus = sheet.CallStatus,
                Remark = sheet.Remark,
                Details = details
            };
        }

        /// <summary>
        /// 根据叫料单ID获取明细列表
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>明细列表</returns>
        public async Task<List<CallMaterialDetailEntity>> GetCallMaterialDetailsBySheetId(string sheetId)
        {
            return await _callMaterialDetailRepository.FindList(d => d.CallSheetId == sheetId && d.Deleted == 0);
        }

        /// <summary>
        /// 生成叫料单号 JL+日期+3位流水码
        /// </summary>
        /// <returns>叫料单号</returns>
        private string GenerateCallOrderNo()
        {
            string prefix = "JL";
            string datePart = DateTime.Now.ToString("yyyyMMdd");
            
            // 查询当天最大的叫料单号，获取流水码部分
            var todayCallOrders = _callMaterialSheetRepository.Db.Queryable<CallMaterialSheetEntity>()
                .Where(c => c.CallOrderNo.StartsWith($"{prefix}{datePart}") && c.Deleted == 0)
                .Select(c => c.CallOrderNo)
                .ToList();
            
            int maxSequence = 0;
            if (todayCallOrders.Any())
            {
                // 从已有的叫料单号中提取最大的流水码
                foreach (var orderNo in todayCallOrders)
                {
                    if (orderNo.Length == 11 && int.TryParse(orderNo.Substring(10, 3), out int sequence))
                    {
                        if (sequence > maxSequence)
                            maxSequence = sequence;
                    }
                }
            }
            
            // 流水码加1
            string sequencePart = (maxSequence + 1).ToString("D3");
            return $"{prefix}{datePart}{sequencePart}";
        }
    }
}