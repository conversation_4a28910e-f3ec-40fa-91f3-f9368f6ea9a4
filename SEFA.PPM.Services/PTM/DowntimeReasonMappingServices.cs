
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using AutoMapper;
using System.Linq;

namespace SEFA.PPM.Services
{
	public class DowntimeReasonMappingServices : BaseServices<DowntimeReasonMappingEntity>, IDowntimeReasonMappingServices
	{
		private readonly IBaseRepository<DowntimeReasonMappingEntity> _dal;
		private readonly IUser _user;
		private readonly IMapper _mapper;
		public DowntimeReasonMappingServices(IBaseRepository<DowntimeReasonMappingEntity> dal, IUser user, IMapper mapper)
		{
			this._dal = dal;
			base.BaseDal = dal;
			this._user = user;
			this._mapper = mapper;
		}

		public async Task<List<DowntimeReasonMappingEntity>> GetList(DowntimeReasonMappingRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<DowntimeReasonMappingEntity>()
				  .AndIF(!string.IsNullOrEmpty(reqModel.MachineId), a => a.MachineId == reqModel.MachineId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.IsDefault), a => a.IsDefault == reqModel.IsDefault)
				  .ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}


		public async Task<List<DowntimeReasonMappingEntity>> GetListBySortOrder(string sort, string eqMentId)
		{
			var whereExpression = Expressionable.Create<DowntimeReasonMappingEntity>()
					 .And(p => p.MachineId.Contains(eqMentId))
					 .ToExpression();
			if (sort == "up")
			{

				var data = await _dal.Db.Queryable<DowntimeReasonMappingEntity>().Where(whereExpression).OrderBy(p => p.SortOrder).ToListAsync();
				return data;
			}
			else
			{
				var data = await _dal.Db.Queryable<DowntimeReasonMappingEntity>().Where(whereExpression).OrderByDescending(p => p.SortOrder).ToListAsync();
				return data;
			}
		}
		public async Task<PageModel<DowntimeReasonMappingEntity>> GetPageList(DowntimeReasonMappingRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<DowntimeReasonMappingEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<bool> SaveForm(DowntimeReasonMappingEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				entity.CreateCustomGuid(_user.Name);
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}

		public async Task<MessageModel<string>> SaveFormWithReasonList(DowntimeReasonMappingSaveListEntity entity)
		{
			var result = new MessageModel<string>() { success = false, msg = "保存失败！" };
			List<DowntimeReasonMappingEntity> insertList = new List<DowntimeReasonMappingEntity>();
			var list = await _dal.FindList(x => x.MachineId == entity.MachineId);
			foreach (var item in entity.ReasonIdList)
			{
				if (list.Any(x => x.ReasonId == item))
				{
					result.msg += "存在重复数据";
					return result;
				}
				var model = _mapper.Map<DowntimeReasonMappingEntity>(entity);
				model.ReasonId = item;
				model.CreateCustomGuid(_user.Name);
				insertList.Add(model);
			}
			await this.Add(insertList);
			result.success = true;
			result.msg = "保存成功！";
			return result;

		}
	}
}