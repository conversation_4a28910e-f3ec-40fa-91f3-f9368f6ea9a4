using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.IServices.PTM;

namespace SEFA.PPM.Services.PTM
{
    public class BasePropertyServices : BaseServices<BasePropertyEntity>, IBasePropertyServices
    {
        private readonly IBaseRepository<BasePropertyEntity> _dal;
        public BasePropertyServices(IBaseRepository<BasePropertyEntity> dal)
        {
            _dal = dal;
            BaseDal = dal;
        }

        public async Task<List<BasePropertyEntity>> GetList(BasePropertyRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BasePropertyEntity>> GetPageList(BasePropertyRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(BasePropertyEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await Add(entity) > 0;
            }
            else
            {
                return await Update(entity);
            }
        }
    }
}