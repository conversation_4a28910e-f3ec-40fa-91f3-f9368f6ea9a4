using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Repository.Interface.WMS;
using SqlSugar;

namespace SEFA.PPM.Services.Interface.WMS
{
    /// <summary>
    /// WMS接口调用日志服务实现类
    /// </summary>
    public class WMSInterfaceLogServices : IWMSInterfaceLogServices
    {
        private readonly IWMSInterfaceLogRepository _logRepository;
        private readonly IUnitOfWork _unitOfWork;

        public WMSInterfaceLogServices(IWMSInterfaceLogRepository logRepository, IUnitOfWork unitOfWork)
        {
            _logRepository = logRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 记录接口调用日志
        /// </summary>
        /// <param name="logEntity">日志信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddAsync(WMSInterfaceLogEntity logEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 设置基础信息
                if (string.IsNullOrEmpty(logEntity.ID))
                {
                    logEntity.ID = Guid.NewGuid().ToString();
                }

                if (logEntity.CreateDate == default(DateTime))
                {
                    logEntity.CreateDate = DateTime.Now;
                }

                if (string.IsNullOrEmpty(logEntity.CreateUserId))
                {
                    logEntity.CreateUserId = "SYSTEM";
                }

                logEntity.Deleted = 0;

                var result = await _logRepository.Add(logEntity) > 0;
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 获取日志列表（分页）
        /// </summary>
        /// <param name="interfaceName">接口名称</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>日志列表和总记录数</returns>
        public async Task<PageModel<WMSInterfaceLogEntity>> GetPageListAsync(
            string interfaceName, bool? isSuccess,
            string startTime, string endTime,
            int pageIndex, int pageSize)
        {
            PageModel<WMSInterfaceLogEntity> result = new PageModel<WMSInterfaceLogEntity>();

            var query = _logRepository.Db.Queryable<WMSInterfaceLogEntity>();

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(interfaceName), s => s.InterfaceName.Contains(interfaceName))
                .WhereIF(isSuccess != null, s => s.IsSuccess == isSuccess)
                .WhereIF(!string.IsNullOrEmpty(startTime) && !string.IsNullOrEmpty(endTime),
                    s => s.CreateDate >= Convert.ToDateTime(startTime) &&
                         s.CreateDate <= Convert.ToDateTime(endTime));

            var data = await query.Select(s => new WMSInterfaceLogEntity
            {
                ID = s.ID,
                InterfaceName = s.InterfaceName,
                RequestData = s.RequestData,
                ResponseData = s.ResponseData,
                IsSuccess = s.IsSuccess,
                CreateDate = s.CreateDate
            }).ToPageListAsync(pageIndex, pageSize, result.dataCount);

            result.data = data;
            return result;
        }

        /// <summary>
        /// 获取日志列表（不分页）
        /// </summary>
        /// <param name="interfaceName">接口名称</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志列表</returns>
        public async Task<List<WMSInterfaceLogEntity>> GetListAsync(
            string interfaceName, bool? isSuccess,
            string startTime, string endTime)
        {
            var query = _logRepository.Db.Queryable<WMSInterfaceLogEntity>();

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(interfaceName), s => s.InterfaceName.Contains(interfaceName))
                .WhereIF(isSuccess != null, s => s.IsSuccess == isSuccess)
                .WhereIF(!string.IsNullOrEmpty(startTime) && !string.IsNullOrEmpty(endTime),
                    s => s.CreateDate >= Convert.ToDateTime(startTime) &&
                         s.CreateDate <= Convert.ToDateTime(endTime));

            var data = await query.Select(s => new WMSInterfaceLogEntity
            {
                ID = s.ID,
                InterfaceName = s.InterfaceName,
                RequestData = s.RequestData,
                ResponseData = s.ResponseData,
                IsSuccess = s.IsSuccess,
                CreateDate = s.CreateDate
            }).ToListAsync();

            return data;
        }

        /// <summary>
        /// 根据ID获取日志详情
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>日志信息</returns>
        public async Task<WMSInterfaceLogEntity> GetByIdAsync(string id)
        {
            return await _logRepository.FindEntity(id);
        }
    }
}