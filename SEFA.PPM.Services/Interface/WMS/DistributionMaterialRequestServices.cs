using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.MKM.Dto;
using SEFA.PPM.Repository.Interface.WMS;

namespace SEFA.PPM.Services.Interface.WMS
{
    /// <summary>
    /// 叫料申请服务实现类
    /// </summary>
    public class DistributionMaterialRequestServices : IDistributionMaterialRequestServices
    {
        private readonly IDistributionMaterialRequestRepository _requestRepository;
        private readonly IDistributionMaterialDetailRepository _detailRepository;
        private readonly IUnitOfWork _unitOfWork;

        public DistributionMaterialRequestServices(
            IDistributionMaterialRequestRepository requestRepository,
            IDistributionMaterialDetailRepository detailRepository,
            IUnitOfWork unitOfWork)
        {
            _requestRepository = requestRepository;
            _detailRepository = detailRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 新增叫料申请
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddAsync(DistributionMaterialRequestEntity requestEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 设置主表基础信息
                if (string.IsNullOrEmpty(requestEntity.ID))
                {
                    requestEntity.ID = Guid.NewGuid().ToString();
                }

                if (requestEntity.CreateDate == default(DateTime))
                {
                    requestEntity.CreateDate = DateTime.Now;
                }

                requestEntity.Deleted = 0;

                // 添加主表
                var requestResult = await _requestRepository.Add(requestEntity);
                if (requestResult <= 0)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 添加明细表
                if (requestEntity.Details != null && requestEntity.Details.Count > 0)
                {
                    foreach (var detail in requestEntity.Details)
                    {
                        // 设置明细表基础信息
                        if (string.IsNullOrEmpty(detail.ID))
                        {
                            detail.ID = Guid.NewGuid().ToString();
                        }

                        detail.RequestId = requestEntity.ID;
                        if (detail.CreateDate == default(DateTime))
                        {
                            detail.CreateDate = DateTime.Now;
                        }

                        if (string.IsNullOrEmpty(detail.CreateUserId))
                        {
                            detail.CreateUserId = requestEntity.CreateUserId;
                        }

                        detail.Deleted = 0;

                        var detailResult = await _detailRepository.Add(detail) > 0;
                        if (!detailResult)
                        {
                            _unitOfWork.RollbackTran();
                            return false;
                        }
                    }
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取叫料申请
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请信息</returns>
        public async Task<DistributionMaterialRequestEntity> GetByIdAsync(string id)
        {
            return await _requestRepository.FindEntity(id);
        }

        /// <summary>
        /// 获取叫料申请列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        public async Task<PageModel<DistributionMaterialRequestEntity>> GetPageListAsync(
            DistributionMaterialRequestQueryDto queryDto, int pageIndex, int pageSize)
        {
            PageModel<DistributionMaterialRequestEntity> result = new PageModel<DistributionMaterialRequestEntity>();

            var query = _requestRepository.Db.Queryable<DistributionMaterialRequestEntity>();

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(queryDto?.RequestSheetNo),
                    s => s.RequestSheetNo.Contains(queryDto.RequestSheetNo))
                .WhereIF(!string.IsNullOrEmpty(queryDto?.LineWarehouseCode),
                    s => s.LineWarehouseCode.Contains(queryDto.LineWarehouseCode))
                .WhereIF(queryDto?.StartTime != null && queryDto?.EndTime != null,
                    s => s.CreateDate >= queryDto.StartTime && s.CreateDate <= queryDto.EndTime)
                .Where(s => s.Deleted == 0); // 逻辑删除过滤

            var data = await query.Select(s => new DistributionMaterialRequestEntity
            {
                ID = s.ID,
                RequestSheetNo = s.RequestSheetNo,
                LineWarehouseCode = s.LineWarehouseCode,
                CreateDate = s.CreateDate,
                CreateUserId = s.CreateUserId,
                ModifyDate = s.ModifyDate,
                ModifyUserId = s.ModifyUserId
            }).ToPageListAsync(pageIndex, pageSize, result.dataCount);

            result.data = data;
            return result;
        }

        /// <summary>
        /// 获取叫料申请列表（不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        public async Task<List<DistributionMaterialRequestEntity>> GetListAsync(
            DistributionMaterialRequestQueryDto queryDto)
        {
            var query = _requestRepository.Db.Queryable<DistributionMaterialRequestEntity>();

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(queryDto?.RequestSheetNo),
                    s => s.RequestSheetNo.Contains(queryDto.RequestSheetNo))
                .WhereIF(!string.IsNullOrEmpty(queryDto?.LineWarehouseCode),
                    s => s.LineWarehouseCode.Contains(queryDto.LineWarehouseCode))
                .WhereIF(queryDto?.StartTime != null && queryDto?.EndTime != null,
                    s => s.CreateDate >= queryDto.StartTime && s.CreateDate <= queryDto.EndTime)
                .Where(s => s.Deleted == 0); // 逻辑删除过滤

            var data = await query.Select(s => new DistributionMaterialRequestEntity
            {
                ID = s.ID,
                RequestSheetNo = s.RequestSheetNo,
                LineWarehouseCode = s.LineWarehouseCode,
                CreateDate = s.CreateDate,
                CreateUserId = s.CreateUserId,
                ModifyDate = s.ModifyDate,
                ModifyUserId = s.ModifyUserId,
                Deleted = s.Deleted
            }).ToListAsync();

            return data;
        }

        /// <summary>
        /// 获取叫料申请列表（包含明细，分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        public async Task<PageModel<DistributionMaterialRequestEntity>> GetPageListWithDetailsAsync(
            DistributionMaterialRequestQueryDto queryDto, int pageIndex, int pageSize)
        {
            PageModel<DistributionMaterialRequestEntity> result = new PageModel<DistributionMaterialRequestEntity>();

            var query = _requestRepository.Db.Queryable<DistributionMaterialRequestEntity>()
                .Includes(x => x.Details.Where(d => d.Deleted == 0));

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(queryDto?.RequestSheetNo),
                    s => s.RequestSheetNo.Contains(queryDto.RequestSheetNo))
                .WhereIF(!string.IsNullOrEmpty(queryDto?.LineWarehouseCode),
                    s => s.LineWarehouseCode.Contains(queryDto.LineWarehouseCode))
                .WhereIF(queryDto?.StartTime != null && queryDto?.EndTime != null,
                    s => s.CreateDate >= queryDto.StartTime && s.CreateDate <= queryDto.EndTime)
                .Where(s => s.Deleted == 0); // 逻辑删除过滤

            var data = await query.ToPageListAsync(pageIndex, pageSize, result.dataCount);

            result.data = data;
            return result;
        }

        /// <summary>
        /// 获取叫料申请列表（包含明细，不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        public async Task<List<DistributionMaterialRequestEntity>> GetListWithDetailsAsync(
            DistributionMaterialRequestQueryDto queryDto)
        {
            var query = _requestRepository.Db.Queryable<DistributionMaterialRequestEntity>()
                .Includes(x => x.Details.Where(d => d.Deleted == 0));

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(queryDto?.RequestSheetNo),
                    s => s.RequestSheetNo.Contains(queryDto.RequestSheetNo))
                .WhereIF(!string.IsNullOrEmpty(queryDto?.LineWarehouseCode),
                    s => s.LineWarehouseCode.Contains(queryDto.LineWarehouseCode))
                .WhereIF(queryDto?.StartTime != null && queryDto?.EndTime != null,
                    s => s.CreateDate >= queryDto.StartTime && s.CreateDate <= queryDto.EndTime)
                .Where(s => s.Deleted == 0); // 逻辑删除过滤

            var data = await query.ToListAsync();

            return data;
        }

        /// <summary>
        /// 更新叫料申请
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAsync(DistributionMaterialRequestEntity requestEntity)
        {
            if (string.IsNullOrEmpty(requestEntity.ID))
                return false;

            _unitOfWork.BeginTran();
            try
            {
                // 更新主表
                var updateResult = await _requestRepository.Update(requestEntity);
                if (!updateResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 删除原有明细
                var deleteResult = await _detailRepository.Delete(a => a.RequestId == requestEntity.ID);
                if (!deleteResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 添加新明细
                if (requestEntity.Details != null && requestEntity.Details.Count > 0)
                {
                    foreach (var detail in requestEntity.Details)
                    {
                        detail.RequestId = requestEntity.ID;
                        var detailResult = await _detailRepository.Add(detail) > 0;
                        if (!detailResult)
                        {
                            _unitOfWork.RollbackTran();
                            return false;
                        }
                    }
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除叫料申请（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(string id)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 删除主表
                var requestResult = await _requestRepository.DeleteById(id);
                if (!requestResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 删除明细表
                var detailResult = await _detailRepository.Delete(a => a.RequestId == id);
                if (!detailResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }
    }
}