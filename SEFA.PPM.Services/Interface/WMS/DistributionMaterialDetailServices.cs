using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Repository.Interface.WMS;

namespace SEFA.PPM.Services.Interface.WMS
{
    /// <summary>
    /// 叫料申请明细服务实现类
    /// </summary>
    public class DistributionMaterialDetailServices : IDistributionMaterialDetailServices
    {
        private readonly IDistributionMaterialDetailRepository _detailRepository;
        private readonly IUnitOfWork _unitOfWork;

        public DistributionMaterialDetailServices(
            IDistributionMaterialDetailRepository detailRepository,
            IUnitOfWork unitOfWork)
        {
            _detailRepository = detailRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 根据主表ID获取叫料申请明细列表
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>叫料申请明细列表</returns>
        public async Task<List<DistributionMaterialDetailEntity>> GetByRequestIdAsync(string requestId)
        {
            return await _detailRepository.FindList(requestId);
        }

        /// <summary>
        /// 根据ID获取叫料申请明细
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请明细信息</returns>
        public async Task<DistributionMaterialDetailEntity> GetByIdAsync(string id)
        {
            return await _detailRepository.FindEntity(id);
        }

        /// <summary>
        /// 新增叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddAsync(DistributionMaterialDetailEntity detailEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Add(detailEntity) > 0;
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 批量新增叫料申请明细
        /// </summary>
        /// <param name="details">叫料申请明细列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddRangeAsync(List<DistributionMaterialDetailEntity> details)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Add(details) > 0;
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 更新叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAsync(DistributionMaterialDetailEntity detailEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Update(detailEntity);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(string id)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.DeleteById(id);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 根据主表ID删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByRequestIdAsync(string requestId)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Delete(a => a.RequestId == requestId);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }
    }
}