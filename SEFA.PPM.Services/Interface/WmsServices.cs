using System;
using System.Threading.Tasks;
using MongoDB.Bson;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.Services.Interface;

public class WmsServices : IWmsServices
{
    private static readonly string CallMaterialUrl = Appsettings.app("WMS_Interface_Url", "CallMaterialUrl");
    private static readonly string SendDistributionMaterialUrl = Appsettings.app("WMS_Interface_Url", "SendDistributionMaterialUrl");
    private static readonly string PalletReleaseUrl = Appsettings.app("WMS_Interface_Url", "PalletReleaseUrl");
    private static readonly string PalletReturnRequestUrl = Appsettings.app("WMS_Interface_Url", "PalletReturnRequestUrl");
    private static readonly string BarCodePrintUrl = Appsettings.app("WMS_Interface_Url", "BarCodePrintUrl");
    private static readonly string EmptyIbcReturnUrl = Appsettings.app("WMS_Interface_Url", "EmptyIBCReturnUrl");
    private static readonly string IbcMaterialInBoundUrl = Appsettings.app("AppSettings:WMS_Interface_Url", "IBCMaterialInBoundUrl");
    private static readonly string LineWarehouseMaterialOutBoundUrl = Appsettings.app("WMS_Interface_Url", "LineWarehouseMaterialOutBoundUrl");
    
    #region WMS叫料
    /// <summary>
    /// 异步调用叫料接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    // public async Task<MessageModel<String>> CallMaterialAsync(CallMaterialSheet request)
    // {
    //     MessageModel<String> result = new MessageModel<String>();
    //     result.success = true;
    //     result.msg = "成功";
    //     result.response = "成功";
    //     try
    //     {
    //         // 调用HTTP辅助类发送POST请求到指定接口
    //         var apiResult = await HttpHelper.PostAsync<string>("WMS", CallMaterialUrl, null, request);
    //         // 记录返回内容的调试日志
    //         SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "CallMaterial");
    //         // 检查返回结果是否为空
    //         if (apiResult == null)
    //         {
    //             // 如果返回为空，设置结果为失败并记录错误消息
    //             result.success = false;
    //             result.msg = "叫料接口返回为空！";
    //             SerilogServer.LogDebug(result.msg, "CallMaterial");
    //             return result;
    //         }
    //
    //         // 将接口返回的结果赋值给本地结果对象
    //         result.success = apiResult.success;
    //         result.msg = apiResult.msg;
    //         // 记录接口返回消息的调试日志
    //         SerilogServer.LogDebug(result.msg, "CallMaterial");
    //     }
    //     catch (Exception ex)
    //     {
    //         // 捕获异常，设置结果为失败并记录异常堆栈信息
    //         result.success = false;
    //         result.msg = "叫料接口调用异常：" + ex.Message;
    //         SerilogServer.LogDebug(result.msg, "CallMaterial");
    //         return result;
    //     }
    //
    //     return result;
    // }

    #endregion

    #region WMS物料到达通知
    /// <summary>
    /// 异步调用物料到达通知接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<MessageModel<String>> SendDistributionMaterialAsync(DistributionMaterialRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "物料到达通知成功";
        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<bool>("WMS", SendDistributionMaterialUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "SendDistributionMaterial");
            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "物料到达通知接口返回为空！";
                SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "物料到达通知接口调用异常：" + ex.Message;
            SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
            return result;
        }

        return result;
    }
    #endregion

    #region WMS托盘解绑
    /// <summary>
    /// 异步调用托盘解绑接口
    /// </summary>
    /// <param name="request">托盘解绑请求参数</param>
    /// <returns>解绑结果</returns>
    public async Task<MessageModel<String>> PalletReleaseAsync(PalletReleaseRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "托盘解绑成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", PalletReleaseUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "PalletRelease");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "托盘解绑接口返回为空！";
                result.response = "托盘解绑失败";
                SerilogServer.LogDebug(result.msg, "PalletRelease");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "PalletRelease");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "托盘解绑接口调用异常：" + ex.Message;
            result.response = "托盘解绑失败";
            SerilogServer.LogDebug(result.msg, "PalletRelease");
            return result;
        }

        return result;
    }
    #endregion

    #region WMS打印标签
    /// <summary>
    /// 异步调用打印标签接口
    /// </summary>
    /// <param name="request">打印标签请求参数</param>
    /// <returns>打印结果</returns>
    public async Task<MessageModel<String>> BarCodePrintAsync(BarCodePrintRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "打印标签成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", BarCodePrintUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "BarCodePrint");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "打印标签接口返回为空！";
                result.response = "打印标签失败";
                SerilogServer.LogDebug(result.msg, "BarCodePrint");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "BarCodePrint");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "打印标签接口调用异常：" + ex.Message;
            result.response = "打印标签失败";
            SerilogServer.LogDebug(result.msg, "BarCodePrint");
            return result;
        }

        return result;
    }
    #endregion

    #region WMS组托退库申请
    /// <summary>
    /// 异步调用组托退库申请接口
    /// </summary>
    /// <param name="request">组托退库申请请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> PalletReturnRequestAsync(PalletReturnRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "组托退库申请成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", PalletReturnRequestUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "PalletReturnRequest");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "组托退库申请接口返回为空！";
                result.response = "组托退库申请失败";
                SerilogServer.LogDebug(result.msg, "PalletReturnRequest");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "PalletReturnRequest");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "组托退库申请接口调用异常：" + ex.Message;
            result.response = "组托退库申请失败";
            SerilogServer.LogDebug(result.msg, "PalletReturnRequest");
            return result;
        }

        return result;
    }
    #endregion

    #region IBC桶空桶退库申请
    /// <summary>
    /// 异步调用IBC桶空桶退库申请接口
    /// </summary>
    /// <param name="request">退库请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> EmptyIBCReturnAsync(EmptyIBCReturnRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "IBC桶空桶退库申请成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", EmptyIbcReturnUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "EmptyIBCReturn");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "IBC桶空桶退库申请接口返回为空！";
                result.response = "IBC桶空桶退库申请失败";
                SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "IBC桶空桶退库申请接口调用异常：" + ex.Message;
            result.response = "IBC桶空桶退库申请失败";
            SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");
            return result;
        }

        return result;
    }
    #endregion

    #region IBC桶配置成品入库申请
    /// <summary>
    /// 异步调用IBC桶配置成品入库申请接口
    /// </summary>
    /// <param name="request">入库请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> IBCMaterialInBoundAsync(IBCMaterialInBoundRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "IBC桶配置成品入库申请成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", IbcMaterialInBoundUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "IBCMaterialInBound");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "IBC桶配置成品入库申请接口返回为空！";
                result.response = "IBC桶配置成品入库申请失败";
                SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "IBC桶配置成品入库申请接口调用异常：" + ex.Message;
            result.response = "IBC桶配置成品入库申请失败";
            SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");
            return result;
        }

        return result;
    }
    #endregion

    #region 线边物料出库
    /// <summary>
    /// 异步调用线边物料出库接口
    /// </summary>
    /// <param name="request">出库请求参数</param>
    /// <returns>出库结果</returns>
    public async Task<MessageModel<String>> LineWarehouseMaterialOutBoundAsync(LineWarehouseMaterialOutBoundRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "线边物料出库成功";

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", LineWarehouseMaterialOutBoundUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "LineWarehouseMaterialOutBound");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "线边物料出库接口返回为空！";
                result.response = "线边物料出库失败";
                SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "线边物料出库接口调用异常：" + ex.Message;
            result.response = "线边物料出库失败";
            SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");
            return result;
        }

        return result;
    }
    #endregion
    
    
}