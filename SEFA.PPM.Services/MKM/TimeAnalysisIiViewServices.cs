
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class TimeAnalysisIiViewServices : BaseServices<TimeAnalysisIiViewEntity>, ITimeAnalysisIiViewServices
    {
        private readonly IBaseRepository<TimeAnalysisIiViewEntity> _dal;
        public TimeAnalysisIiViewServices(IBaseRepository<TimeAnalysisIiViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<TimeAnalysisIiViewEntity>> GetList(TimeAnalysisIiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TimeAnalysisIiViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<TimeAnalysisIiViewEntity>> GetPageList(TimeAnalysisIiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TimeAnalysisIiViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(TimeAnalysisIiViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}