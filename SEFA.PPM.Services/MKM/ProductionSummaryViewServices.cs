
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using StackExchange.Profiling.Internal;

namespace SEFA.MKM.Services
{
    public class ProductionSummaryViewServices : BaseServices<ProductionSummaryViewEntity>, IProductionSummaryViewServices
    {
        private readonly IBaseRepository<ProductionSummaryViewEntity> _dal;
        public ProductionSummaryViewServices(IBaseRepository<ProductionSummaryViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ProductionSummaryViewEntity>> GetList(ProductionSummaryViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProductionSummaryViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }
        /// <summary>
        /// 产出数据汇总记录
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<ProductionSummaryViewEntity>> GetPageList(ProductionSummaryViewRequestModel reqModel)
        {

            PageModel<ProductionSummaryViewEntity> result = new PageModel<ProductionSummaryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ProductionSummaryViewEntity>()
              .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.LineName != null && a.LineName.Contains(reqModel.LineName))
              .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine != null && a.Machine.Contains(reqModel.Machine))
              .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder != null && a.ProcessOrder.Contains(reqModel.ProcessOrder))
              .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode != null && a.MaterialCode.Contains(reqModel.Material))
              .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
              .AndIF(!string.IsNullOrEmpty(reqModel.ExecutionStatus), a => a.ExecutionStatus != null && a.ExecutionStatus.Contains(reqModel.ExecutionStatus))
              .AndIF(!string.IsNullOrEmpty(reqModel.Planned), a => a.Planned != null && a.Planned.ToString().Contains(reqModel.Planned))
              .AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
                a.LineName.Contains(reqModel.Search) ||
                a.Machine.Contains(reqModel.Search) ||
                a.ProcessOrder.Contains(reqModel.Search) ||
                a.MaterialCode.Contains(reqModel.Search) ||
                a.ShiftName.Contains(reqModel.Search)||
                a.Formula.Contains(reqModel.Search)||
                a.Planned.ToString().Contains(reqModel.Search) 
                )
              .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
              .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
            .ToExpression();
       

            var data2 = await _dal.Db.Queryable<ProductionSummaryViewEntity>()
                .Where(whereExpression).ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;

        }

        public async Task<bool> SaveForm(ProductionSummaryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}