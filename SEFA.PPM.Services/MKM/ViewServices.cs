
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.ViewModels;
using System.Reflection;
using System;

namespace SEFA.PPM.Services
{
    public class ViewServices : BaseServices<ViewEntity>, IViewServices
    {
        private readonly IBaseRepository<ViewEntity> _dal;

        public ViewServices(IBaseRepository<ViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ViewEntity>> GetList(ViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<ViewEntity>> GetPageList(ViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(ViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        
    }
}