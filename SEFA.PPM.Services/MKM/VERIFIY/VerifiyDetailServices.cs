
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using System;
using System.Linq;
using SEFA.DFM.Model.ViewModels;
using StackExchange.Profiling.Internal;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Reactive;
using System.Security.Cryptography;
using SEFA.PPM.Model.ViewModels.MKM.View;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Collections;
using static SEFA.PPM.Services.KpitgtServices;
using SEFA.PPM.Model.ViewModels.MKM.PrintView;

namespace SEFA.PPM.Services
{
    public class VerifiyDetailServices : BaseServices<VerifiyDetailEntity>, IVerifiyDetailServices
    {
        private readonly IBaseRepository<VerifiyDetailEntity> _dal;
        private readonly IBaseRepository<VerifiyListEntity> _dalVerifiyListEntity;//主界面查询 
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<VerifiyDetailViewEntity> _dalView;
        private readonly IBaseRepository<InventorylistingViewEntity> _dalInventorylistingViewEntity;
        private readonly IBaseRepository<MaterialInventoryEntity> _dalMaterialInventoryEntity;
        private readonly IBaseRepository<MaterialTransferEntity> _dalMaterialTransferEntity;



        private readonly IBaseRepository<MaterialSubLotEntity> _dalMaterialSubLotEntity;
        private readonly IBaseRepository<MaterialLotEntity> _dalMaterialLotEntity;
        private readonly IBaseRepository<TransferHistoryViewEntity> _dalTransferHistoryViewEntity;

        private readonly IBaseRepository<EquipmentEntity> _dalEquipmentEntity;
        private readonly IBaseRepository<WorkorderthroatEntity> _dalWorkorderthroatEntity;
        private readonly IBaseRepository<ProductionOrderEntity> _dalProductionOrderEntity;


        public IUser _user;
        public VerifiyDetailServices(IBaseRepository<VerifiyDetailEntity> dal, IBaseRepository<VerifiyListEntity> dalVerifiyListEntity, IUser user, IUnitOfWork unitOfWork, IBaseRepository<VerifiyDetailViewEntity> dalView, IBaseRepository<InventorylistingViewEntity> dalInventorylistingViewEntity, IBaseRepository<MaterialInventoryEntity> dalMaterialInventoryEntity, IBaseRepository<MaterialTransferEntity> dalMaterialTransferEntity, IMapper mapper, IBaseRepository<MaterialSubLotEntity> dalMaterialSubLotEntity, IBaseRepository<MaterialLotEntity> dalMaterialLotEntity, IBaseRepository<TransferHistoryViewEntity> dalTransferHistoryViewEntity, IBaseRepository<EquipmentEntity> dalEquipmentEntity, IBaseRepository<WorkorderthroatEntity> dalWorkorderthroatEntity, IBaseRepository<ProductionOrderEntity> dalProductionOrderEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dalVerifiyListEntity = dalVerifiyListEntity;
            _user = user;
            _unitOfWork = unitOfWork;
            _dalView = dalView;
            _dalInventorylistingViewEntity = dalInventorylistingViewEntity;
            _dalMaterialInventoryEntity = dalMaterialInventoryEntity;
            _dalMaterialTransferEntity = dalMaterialTransferEntity;
            _mapper = mapper;
            _dalMaterialSubLotEntity = dalMaterialSubLotEntity;
            _dalMaterialLotEntity = dalMaterialLotEntity;
            _dalTransferHistoryViewEntity = dalTransferHistoryViewEntity;
            _dalEquipmentEntity = dalEquipmentEntity;
            _dalWorkorderthroatEntity = dalWorkorderthroatEntity;
            _dalProductionOrderEntity = dalProductionOrderEntity;
        }


        #region 使用函数

        #region 原料（每日盘点）

        #region 主界面查询

        public async Task<PageModel<VerifiyListEntity>> GetPageList_YL(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyListEntity> result = new PageModel<VerifiyListEntity>();

            if (reqModel.TypeS == "PDA")
            {
                DateTime endTime = DateTime.Now.AddDays(15);
                DateTime startTime = DateTime.Now.AddDays(-15);
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                      .And(a => a.CreateDate >= Convert.ToDateTime(startTime))
                 .And(a => a.CreateDate <= Convert.ToDateTime(endTime))
                            .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "原料").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;
            }
            else
            {
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                          .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                     .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "原料").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;

            }
        }

        #endregion

        #region 操作

        public async Task<MessageModel<string>> Add_YL(VerifiyListRequestModel entity)
        {

            var result = new MessageModel<string>();
            result.success = false;

            if (string.IsNullOrEmpty(entity.Plandate))
            {
                result.msg = "请选择盘点时间";
                return result;
            }

            //判断当前是否有未结束的数据
            var d = await _dalVerifiyListEntity.FindList(p => p.TaskStatus != "已同步" && p.MaterialType == "原料");
            if (d != null && d.Count > 0)
            {
                result.msg = "当前存在尚未结束";
                return result;
            }

            try
            {
                string lID = string.Empty;
                List<VerifiyListEntity> vList = new List<VerifiyListEntity>();
                List<VerifiyDetailEntity> dList = new List<VerifiyDetailEntity>();
                bool vResult = true;
                bool dResult = true;

                VerifiyListEntity model = new VerifiyListEntity();
                model.CreateCustomGuid(_user.Name);
                model.Plandate = Convert.ToDateTime(entity.Plandate);
                //model.TaskStatus = entity.TaskStatus;
                model.MaterialType = "原料";
                model.TaskStatus = "已创建";
                lID = model.ID;
                vList.Add(model);

                #region 平移库存

                var inventData = await _dalInventorylistingViewEntity.FindList(p => (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)) && (p.LocationS.Contains("MFG3") || p.LocationS.Contains("SUR3")));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "SUR3" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04"
                 && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4").ToList();
                inventData = inventData.Where(P => P.Quantity != null && P.MaterialCode != "7300030001" && P.IsThorat != "1").ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    for (int i = 0; i < inventData.Count; i++)
                    {

                        #region 新增数据

                        VerifiyDetailEntity dmodel = new VerifiyDetailEntity();

                        if (inventData[i].Quantity == null)
                        {

                            dmodel.CurrentQuantity = 0;
                            dmodel.ActualQuantity = 0;
                        }
                        else
                        {
                            dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                            dmodel.ActualQuantity = inventData[i].Quantity.Value;
                        }


                        dmodel.CreateCustomGuid(_user.Name);
                        dmodel.MaterialId = inventData[i].MaterialId;
                        dmodel.SublotId = inventData[i].SlotId;
                        dmodel.BatchId = inventData[i].LotId;
                        dmodel.EquipmentId = inventData[i].EquipmentId;
                        dmodel.Result = " ";

                        dmodel.UnitId = inventData[i].UId;
                        dmodel.Difference = 0;
                        dmodel.Reason = " ";
                        dmodel.VerifiylistId = lID;

                        dList.Add(dmodel);

                        #endregion
                    }
                }

                #endregion

                _unitOfWork.BeginTran();

                if (vList.Count > 0)
                {
                    vResult = await _dalVerifiyListEntity.Add(vList) > 0;
                }

                if (dList.Count > 0)
                {
                    dResult = await _dal.Add(dList) > 0;
                }

                if (dResult && vResult)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "创建盘点任务成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建盘点任务失败";
                    return result;
                }


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "创建盘点任务失败" + ex.Message + ex.StackTrace;
                return result;

            }

        }

        public async Task<MessageModel<string>> Delete_YL(VerifiyListRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.IDS == null && entity.IDS.Length <= 0)
            {
                result.msg = "请选中删除的数据";
                return result;
            }

            var vList = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>().In(p => p.ID, entity.IDS).Where(p => p.TaskStatus == "已调整" || p.TaskStatus == "已同步").ToListAsync();

            if (vList != null && vList.Count > 0)
            {
                result.msg = "删除失败，存在已调整或已同步计划";
                return result;
            }

            bool v = true;
            bool vD = true;
            try
            {
                _unitOfWork.BeginTran();
                //查询明细数据
                var dData = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.VerifiylistId, entity.IDS).ToListAsync();
                string[] dIDS = dData.GroupBy(P => P.ID).Select(P => P.Key).ToArray();
                if (dIDS != null && dIDS.Length > 0)
                {
                    vD = await _dal.DeleteByIds(dIDS);
                }
                v = await _dalVerifiyListEntity.DeleteByIds(entity.IDS);
                if (v && vD)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "删除成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "删除失败";
                    return result;
                }

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "删除失败" + ex;
                return result;
            }
        }


        public async Task<PageModel<VerifiyDetailViewEntity>> GetYL_DetailByID(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyDetailViewEntity> result = new PageModel<VerifiyDetailViewEntity>();


            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                       .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key) || a.Code.Contains(reqModel.key) || a.EquipmentName.Contains(reqModel.key))
                       .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                       .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
            var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).ToListAsync();

            //如果为侧边弹框,判断是否需要添加库存车
            if (!string.IsNullOrEmpty(reqModel.sscc))
            {
                //如果不存在数据
                if (data == null || data.Count <= 0)
                {

                    //查询当前

                    //多查询一次库存信息
                    var inventList = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc && p.Quantity == 0 && string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrEmpty(p.BatchId2));
                    //  var inventList = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc);
                    if (inventList != null && inventList.Count > 0)
                    {
                        if (reqModel.DataType == "YL")
                        {
                            inventList = inventList.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "SUR3" && p.LocationS != "RSBK" && p.LocationS != "WP04"
                            && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4" && p.IsThorat != "1").ToList();
                        }
                        else if (reqModel.DataType == "WL")
                        {
                            inventList = inventList.Where(a => a.LocationS.Contains("PKG3")).ToList();
                        }
                        else if (reqModel.DataType == "HT")
                        {
                            int count = 0;
                            if (inventList != null && inventList.Count > 0)
                            {
                                count++;
                            }
                            inventList = inventList.Where(a => a.LocationS.Contains("SUR3")).ToList();
                            if (inventList == null || inventList.Count <= 0)
                            {
                                if (count > 0)
                                {
                                    //查询一次喉头表(sscc对应多个工单号)
                                    var throatList = await _dalWorkorderthroatEntity.FindList(p => p.SSCC == reqModel.sscc && !string.IsNullOrEmpty(p.OrderId));
                                    if (throatList != null || throatList.Count > 0)
                                    {
                                        string[] orderIDS = throatList.GroupBy(P => P.OrderId).Select(P => P.Key).ToArray();

                                        if (orderIDS != null && orderIDS.Length > 0)
                                        {
                                            var proList = await _dalProductionOrderEntity.Db.Queryable<ProductionOrderEntity>().In(P => P.ID, orderIDS).ToListAsync();
                                            string proCos = string.Empty;

                                            for (int i = 0; i < proList.Count; i++)
                                            {

                                                if (i == proList.Count - 1)
                                                {
                                                    proCos += proList[i].ProductionOrderNo + ",";
                                                }
                                                else
                                                {
                                                    proCos += proList[i].ProductionOrderNo;
                                                }
                                            }
                                            List<VerifiyDetailViewEntity> list = new List<VerifiyDetailViewEntity>();
                                            VerifiyDetailViewEntity model = new VerifiyDetailViewEntity();
                                            model.Inventtype = "该喉头已绑定工单" + proCos;
                                            list.Add(model);
                                            result.dataCount = -2;
                                            result.data = list;
                                            return result;
                                        }

                                    }
                                }
                            }
                        }
                        if (inventList != null && inventList.Count > 0)
                        {
                            //
                            var invetData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc);
                            if (invetData == null || invetData.Count <= 0)
                            {
                                VerifiyDetailViewEntity models = new VerifiyDetailViewEntity();
                                models.VerifiylistId = reqModel.id;
                                models.Mcode = inventList[0].MaterialCode;
                                models.Mname = inventList[0].MaterialName;
                                models.ID = inventList[0].ID;
                                models.ActualQuantity = 0;
                                models.Uname = inventList[0].MaxUnit;
                                models.CurrentQuantity = 0;
                                models.Code = inventList[0].Sscc;
                                data.Add(models);

                                int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                var rData1 = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
                                result.dataCount = -1;
                                result.data = rData1;

                                return result;

                            }

                        }
                    }
                    else
                    {
                        #region 这里多查询一次追溯码(存在创建库存数据)

                        var subLotList = await _dalMaterialSubLotEntity.FindList(p => p.SubLotId == reqModel.sscc.Trim());
                        if (subLotList == null || subLotList.Count <= 0)
                        {

                        }
                        else
                        {
                            //这里拿ID
                            string subLotID = subLotList[0].ID;
                            string subSSCC = subLotList[0].SubLotId.Trim();
                            //拿创建历史记录
                            var transferData = await _dalTransferHistoryViewEntity.FindList(P => P.TType == "创建库存" && P.NewSubLotId == reqModel.sscc.Trim());

                            if (reqModel.DataType == "YL")
                            {
                                transferData = transferData.Where(p => p.NewEcode != "FG09" && p.NewEcode != "RS01" && p.NewEcode != "SUR3" && p.NewEcode != "RSBK" && p.NewEcode != "WP04").ToList();
                            }
                            else if (reqModel.DataType == "WL")
                            {
                                transferData = transferData.Where(a => a.NewEcode.Contains("PKG3")).ToList();
                            }
                            else if (reqModel.DataType == "HT")
                            {
                                transferData = transferData.Where(a => a.NewEcode.Contains("SUR3")).ToList();
                            }
                            //如果没有数据弹出信息
                            if (transferData == null || transferData.Count <= 0)
                            {

                            }
                            else
                            {
                                var invetData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc);
                                if (invetData == null || invetData.Count <= 0)
                                {
                                    //这里是用历史数据来创建
                                    string guid = Guid.NewGuid().ToString();
                                    VerifiyDetailViewEntity models = new VerifiyDetailViewEntity();
                                    models.VerifiylistId = reqModel.id;
                                    models.Mcode = transferData[0].NewMaterialCode;
                                    models.Mname = transferData[0].NewMaterialName;
                                    models.ID = transferData[0].ID;
                                    models.ActualQuantity = 0;
                                    models.Uname = transferData[0].HUnit;
                                    models.CurrentQuantity = 0;
                                    models.Code = subSSCC;
                                    data.Add(models);

                                    int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                    var rData1 = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
                                    result.dataCount = -1;
                                    result.data = rData1;

                                    return result;
                                }
                            }
                        }

                        #region 查询历史记录

                        #endregion

                        #endregion

                    }
                }

            }

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data.Count;
            result.data = rDat;

            return result;
        }

        //第二界面查询
        public async Task<PageModel<VerifiyDetailViewEntity>> GetHT_DetailByID(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyDetailViewEntity> result = new PageModel<VerifiyDetailViewEntity>();


            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                       .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key) || a.Code.Contains(reqModel.key) || a.EquipmentName.Contains(reqModel.key) || a.Sapformula.Contains(reqModel.key))
                       .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                       .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
            var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).ToListAsync();

            //如果为侧边弹框,判断是否需要添加库存车
            if (!string.IsNullOrEmpty(reqModel.sscc))
            {
                //如果不存在数据
                if (data == null || data.Count <= 0)
                {

                    //查询当前

                    //多查询一次库存信息
                    var inventListBase = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc && string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrEmpty(p.BatchId2));

                    var inventList = inventListBase.Where(p => p.Quantity == 0).ToList();

                    if (inventList != null && inventList.Count > 0)
                    {
                        if (reqModel.DataType == "HT")
                        {
                            int count = 0;
                            if (inventList != null && inventList.Count > 0)
                            {
                                count++;
                            }
                            inventList = inventList.Where(a => a.LocationS.Contains("SUR3")).ToList();
                            if (inventList == null || inventList.Count <= 0)
                            {
                                if (count > 0)
                                {
                                    //查询一次喉头表(sscc对应多个工单号)
                                    var throatList = await _dalWorkorderthroatEntity.FindList(p => p.SSCC == reqModel.sscc && !string.IsNullOrEmpty(p.OrderId));
                                    if (throatList != null || throatList.Count > 0)
                                    {
                                        string[] orderIDS = throatList.GroupBy(P => P.OrderId).Select(P => P.Key).ToArray();

                                        if (orderIDS != null && orderIDS.Length > 0)
                                        {
                                            var proList = await _dalProductionOrderEntity.Db.Queryable<ProductionOrderEntity>().In(P => P.ID, orderIDS).ToListAsync();
                                            string proCos = string.Empty;

                                            for (int i = 0; i < proList.Count; i++)
                                            {

                                                if (i == proList.Count - 1)
                                                {
                                                    proCos += proList[i].ProductionOrderNo + ",";
                                                }
                                                else
                                                {
                                                    proCos += proList[i].ProductionOrderNo;
                                                }
                                            }
                                            List<VerifiyDetailViewEntity> list = new List<VerifiyDetailViewEntity>();
                                            VerifiyDetailViewEntity model = new VerifiyDetailViewEntity();
                                            model.Inventtype = "该喉头已绑定工单" + proCos;
                                            list.Add(model);
                                            result.dataCount = -2;
                                            result.data = list;
                                            return result;
                                        }

                                    }
                                }
                            }
                        }
                        if (inventList != null && inventList.Count > 0)
                        {
                            var invetData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc);
                            if (invetData == null || invetData.Count <= 0)
                            {
                                VerifiyDetailViewEntity models = new VerifiyDetailViewEntity();
                                models.VerifiylistId = reqModel.id;
                                models.Mcode = inventList[0].MaterialCode;
                                models.Mname = inventList[0].MaterialName;
                                models.ID = inventList[0].ID;
                                models.ActualQuantity = 0;
                                models.Uname = inventList[0].MaxUnit;
                                models.CurrentQuantity = 0;
                                models.Code = inventList[0].Sscc;
                                data.Add(models);

                                int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                var rData1 = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
                                result.dataCount = -1;
                                result.data = rData1;

                                return result;
                            }

                        }
                    }
                    else
                    {
                        int count = 0;
                        if (inventListBase != null && inventListBase.Count > 0)
                        {
                            count++;
                        }
                        var inventList1 = inventListBase.Where(a => a.LocationS.Contains("SUR3")).ToList();
                        if (inventList1 == null || inventList1.Count <= 0)
                        {
                            if (count > 0)
                            {
                                //查询一次喉头表(sscc对应多个工单号)
                                var throatList = await _dalWorkorderthroatEntity.FindList(p => p.SSCC == reqModel.sscc && !string.IsNullOrEmpty(p.OrderId));
                                if (throatList != null || throatList.Count > 0)
                                {
                                    string[] orderIDS = throatList.GroupBy(P => P.OrderId).Select(P => P.Key).ToArray();

                                    if (orderIDS != null && orderIDS.Length > 0)
                                    {
                                        var proList = await _dalProductionOrderEntity.Db.Queryable<ProductionOrderEntity>().In(P => P.ID, orderIDS).ToListAsync();
                                        string proCos = string.Empty;

                                        for (int i = 0; i < proList.Count; i++)
                                        {
                                            string proNO = proList[i].ProductionOrderNo;
                                            if (string.IsNullOrEmpty(proNO))
                                            {
                                                continue;
                                            }
                                            if (i == proList.Count - 1)
                                            {
                                                proCos += proNO + ",";
                                            }
                                            else
                                            {
                                                proCos += proNO;
                                            }
                                        }
                                        List<VerifiyDetailViewEntity> list = new List<VerifiyDetailViewEntity>();
                                        VerifiyDetailViewEntity model = new VerifiyDetailViewEntity();
                                        model.Inventtype = "该喉头已绑定工单" + proCos;
                                        list.Add(model);
                                        result.dataCount = -2;
                                        result.data = list;
                                        return result;
                                    }

                                }
                            }
                        }

                        #region 这里多查询一次追溯码(存在创建库存数据)

                        var subLotList = await _dalMaterialSubLotEntity.FindList(p => p.SubLotId == reqModel.sscc.Trim());
                        if (subLotList == null || subLotList.Count <= 0)
                        {

                        }
                        else
                        {
                            //这里拿ID
                            string subLotID = subLotList[0].ID;
                            string subSSCC = subLotList[0].SubLotId.Trim();
                            //拿创建历史记录
                            var transferData = await _dalTransferHistoryViewEntity.FindList(P => P.TType == "创建库存" && P.NewSubLotId == reqModel.sscc.Trim());

                            if (reqModel.DataType == "HT")
                            {
                                transferData = transferData.Where(a => a.NewEcode.Contains("SUR3")).ToList();
                            }
                            //如果没有数据弹出信息
                            if (transferData == null || transferData.Count <= 0)
                            {

                            }
                            else
                            {
                                var invetData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == reqModel.sscc);
                                if (invetData == null || invetData.Count <= 0)
                                {

                                    //这里是用历史数据来创建
                                    string guid = Guid.NewGuid().ToString();
                                    VerifiyDetailViewEntity models = new VerifiyDetailViewEntity();
                                    models.VerifiylistId = reqModel.id;
                                    models.Mcode = transferData[0].NewMaterialCode;
                                    models.Mname = transferData[0].NewMaterialName;
                                    models.ID = transferData[0].ID;
                                    models.ActualQuantity = 0;
                                    models.Uname = transferData[0].HUnit;
                                    models.CurrentQuantity = 0;
                                    models.Code = subSSCC;
                                    data.Add(models);

                                    int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                    var rData1 = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
                                    result.dataCount = -1;
                                    result.data = rData1;

                                    return result;
                                }
                            }
                        }

                        #region 查询历史记录

                        #endregion

                        #endregion

                    }
                }

            }

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data.Count;
            result.data = rDat;

            return result;
        }
        public async Task<List<VerifiyViewExport>> GetVerifiyExport(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyViewExport> result = new PageModel<VerifiyViewExport>();


            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                       .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key))
                       .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                       .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
            var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).ToListAsync();


            var dataR = (from a in data
                         select new VerifiyViewExport
                         {
                             Mcode = a.Mcode,
                             Mname = a.Mname,
                             LotId = a.LotId,
                             SubLotId = a.SubLotId,
                             Sapformula=a.Sapformula,
                             CurrentQuantity = a.CurrentQuantity,
                             Uname = a.Uname,
                             EquipmentName = a.EquipmentName,
                             Code = a.Code,
                             Movetime = a.Movetime,
                             ActualQuantity = a.ActualQuantity,
                             Result = a.Result,
                             Difference = a.Difference,
                             CreateDate = a.CreateDate,
                             CreateUserId = a.CreateUserId,
                             Reason = a.Reason,
                             Differencenumber = a.Differencenumber
                         }).ToList();

            return dataR;
        }
        public async Task<List<VerifiyDetailViewEntity>> GetYL_DetailBySSCC(string sscc)
        {
            List<VerifiyDetailViewEntity> result = new List<VerifiyDetailViewEntity>();
            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(sscc), a => a.SubLotId == sscc).ToExpression();
            var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

            return data;
        }


        public async Task<MessageModel<string>> AddDetail_YL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entity.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                if (string.IsNullOrEmpty(entity.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entity.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续盘点", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entity.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
            && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4").ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entity.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }

                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = " ";
                            model.CurrentQuantity = inventData[0].Quantity.Value;
                            model.ActualQuantity = inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = 0;
                            model.Reason = " ";
                            model.VerifiylistId = entity.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            VerifiyListEntity verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entity.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "更新失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "更新成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "更新失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entity.SSCC;
                        return result;
                    }
                }
                else
                {
                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "添加失败";
                return result;
            }



        }

        public async Task<MessageModel<string>> ModifyDetail_YL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }

            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {

                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }
                if (entity.types == "新增")
                {
                    model.Result = "新增";
                }

                #region 这里处理逻辑（新增、差异）

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                //if (model.Result == "新增")
                //{
                //    //查询当前数据是否存在
                //    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.Sscc == entity.SSCC);
                //    if (inventoryModel == null)
                //    {
                //        #region 创建库存信息

                //        MaterialInventoryEntity request = new MaterialInventoryEntity();
                //        request.Quantity = Math.Round(Convert.ToDecimal(entity.Qty), 3);
                //        request.Create(_user.Name.ToString());
                //        request.QuantityUomId = inventoryModel.UId;
                //        request.EquipmentId = inventoryModel.EquipmentId;
                //        request.SublotId = inventoryModel.SlotId;
                //        request.LotId = inventoryModel.LotId;

                //        #endregion

                //        #region 写入转移历史

                //        var lotEntity = await _dalMaterialLotEntity.FindList(p => p.ID == Verifiy.BatchId);
                //        if (lotEntity == null || lotEntity.Count <= 0)
                //        {

                //        }
                //        else
                //        {
                //            insetList.Add(request);
                //            //写入历史记录
                //            MaterialTransferEntity trans = new MaterialTransferEntity();
                //            trans.Create(_user.Name.ToString());
                //            //trans.ID = Guid.NewGuid().ToString();
                //            //  trans.OldStorageLocation = storage_location;
                //            // trans.NewStorageLocation = storage_location;
                //            trans.OldLotId = "";
                //            trans.OldSublotId = "";
                //            trans.OldExpirationDate = null;
                //            trans.NewExpirationDate = lotEntity[0].ExpirationDate;
                //            trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                //            trans.QuantityUomId = request.QuantityUomId;
                //            //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //            trans.Type = "Create Inventory";
                //            trans.Comment = "库存盘存-创建";
                //            //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.TransferGroupId
                //            trans.OldEquipmentId = "";
                //            trans.NewEquipmentId = request.EquipmentId;
                //            trans.OldContainerId = "";
                //            trans.NewContainerId = "";
                //            trans.OldLotExternalStatus = "";
                //            trans.OldSublotExternalStatus = "";
                //            trans.NewMaterialId = lotEntity[0].MaterialId;
                //            trans.OldMaterialId = lotEntity[0].MaterialId;
                //            trans.NewLotExternalStatus = "2";
                //            trans.NewSublotId = Verifiy.SublotId;
                //            trans.NewLotId = Verifiy.BatchId;
                //            trans.NewSublotExternalStatus = "3";
                //            transferList.Add(trans);
                //        }


                //        #endregion
                //    }
                //    else
                //    {
                //        #region 更新

                //        //更新数量
                //        MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                //        upInvent.Modify(upInvent.ID, _user.Name);
                //        upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                //        InventListup.Add(upInvent);

                //        #region 写入转移历史    

                //        //写入历史记录
                //        MaterialTransferEntity trans = new MaterialTransferEntity();
                //        trans.Create(_user.Name.ToString());
                //        //trans.ID = Guid.NewGuid().ToString();
                //        trans.OldStorageLocation = inventoryModel.LocationF;
                //        trans.NewStorageLocation = inventoryModel.LocationF;
                //        trans.OldLotId = inventoryModel.LotId;
                //        trans.NewLotId = inventoryModel.LotId;
                //        trans.OldSublotId = inventoryModel.SlotId;
                //        trans.NewSublotId = inventoryModel.SlotId;
                //        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                //        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                //        trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                //        trans.QuantityUomId = inventoryModel.QuantityUomId;
                //        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //        trans.Type = "Inventory Update Quantity";
                //        trans.Comment = "库存-盘存新增更新数量";
                //        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        //trans.TransferGroupId
                //        trans.OldEquipmentId = inventoryModel.EquipmentId;
                //        trans.NewEquipmentId = inventoryModel.EquipmentId;
                //        trans.OldContainerId = inventoryModel.ContainerId;
                //        trans.NewContainerId = inventoryModel.ContainerId;                    //status
                //        trans.OldMaterialId = inventoryModel.MaterialId;
                //        trans.NewMaterialId = inventoryModel.MaterialId;
                //        trans.OldLotExternalStatus = inventoryModel.StatusF;
                //        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                //        trans.NewLotExternalStatus = inventoryModel.StatusF;
                //        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                //        trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                //        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                //        transferList.Add(trans);
                //        #endregion

                //        #endregion
                //    }
                //}
                //else 

                if (model.Result == "差异")
                {
                    //查询当前数据是否存在
                    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == Verifiy.SublotId);

                    if (inventoryModel == null)
                    {
                        result.msg = "请确认库存信息是否存在";
                        return result;
                    }

                    #region 更新

                    //更新数量
                    MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                    upInvent.Modify(upInvent.ID, _user.Name);
                    upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                    InventListup.Add(upInvent);

                    #region 写入转移历史    

                    #region 记录差值

                    decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    decimal oldQty = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);

                    string stringQty = string.Empty;
                    decimal cha = 0;

                    if (newQty > oldQty)
                    {
                        cha = newQty - oldQty;
                        stringQty = oldQty + "+" + cha;
                    }
                    else if (newQty < oldQty)
                    {
                        cha = oldQty - newQty;
                        stringQty = oldQty + "-" + cha;
                    }

                    if (cha == 0)
                    {
                        stringQty = string.Empty;
                    }

                    #endregion


                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Inventory Update Quantity";
                    trans.Comment = "库存-盘存新增更新数量" + stringQty;
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                    transferList.Add(trans);
                    #endregion

                    #endregion
                }

                #endregion


                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }

                bool r = await _dal.Update(model);

                #region 这里执行新增和更新库存信息

                bool r2 = true;
                bool r3 = true;
                bool r4 = true;

                if (InventListup.Count > 0)
                {
                    r2 = await _dalMaterialInventoryEntity.Update(InventListup);
                }
                if (transferList.Count > 0)
                {
                    r3 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                }
                if (insetList.Count > 0)
                {
                    r4 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                }
                #endregion

                if (r == true && v && r2 && r3 && r4)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }

        }

        //原逻辑，不操作库存表
        public async Task<MessageModel<string>> ModifyDetail_YLOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }

            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {

                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }
                if (entity.types == "新增")
                {
                    model.Result = "新增";
                }


                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }



                bool r = await _dal.Update(model);
                if (r == true && v)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }

        }


        #endregion

        #endregion

        #region 物料（每月盘点）

        #region 主界面查询

        public async Task<PageModel<VerifiyListEntity>> GetPageList_WL(VerifiyListRequestModel reqModel)
        {
            if (reqModel.TypeS == "PDA")
            {
                DateTime endTime = DateTime.Now.AddDays(30);
                DateTime startTime = DateTime.Now.AddDays(-31);
                PageModel<VerifiyListEntity> result = new PageModel<VerifiyListEntity>();
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                           .And(a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                      .And(a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                 .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "物料").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;
            }
            else
            {
                PageModel<VerifiyListEntity> result = new PageModel<VerifiyListEntity>();
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                          .AndIF(!string.IsNullOrEmpty(reqModel.tType), a => a.Tasktype.Contains(reqModel.tType))
                            .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                      .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                 .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "物料").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;
            }
        }

        #endregion

        #region 函数


        public async Task<MessageModel<string>> Add_WL(VerifiyListRequestModel entity)
        {

            var result = new MessageModel<string>();
            result.success = false;

            if (string.IsNullOrEmpty(entity.Plandate))
            {
                result.msg = "请选择盘点时间";
                return result;
            }
            if (string.IsNullOrEmpty(entity.Tasktype))
            {
                result.msg = "请选择类型";
                return result;
            }


            //判断当前是否有未结束的数据
            var d = await _dalVerifiyListEntity.FindList(p => p.TaskStatus != "已同步" && p.MaterialType == "物料" && p.Tasktype == entity.Tasktype);
            if (d != null && d.Count > 0)
            {
                result.msg = "当前存在尚未结束";
                return result;
            }

            try
            {
                string lID = string.Empty;
                List<VerifiyListEntity> vList = new List<VerifiyListEntity>();
                List<VerifiyDetailEntity> dList = new List<VerifiyDetailEntity>();
                List<VerifiyListEntity> updateList = new List<VerifiyListEntity>();
                bool vResult = true;
                bool dResult = true;
                bool upResult = true;

                VerifiyListEntity model = new VerifiyListEntity();
                model.CreateCustomGuid(_user.Name);
                model.Plandate = Convert.ToDateTime(entity.Plandate);
                //model.TaskStatus = entity.TaskStatus;
                model.MaterialType = "物料";
                model.TaskStatus = "已创建";
                model.Tasktype = entity.Tasktype;
                lID = model.ID;
                vList.Add(model);

                if (model.Tasktype == "大盘点")
                {
                    DateTime dateTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
                    DateTime end = dateTime.AddDays(1).AddSeconds(-1);


                    //判断当天是否存在退库盘点并且结束了
                    //var tkd = await _dalVerifiyListEntity.FindList(p => p.TaskStatus == "已同步" && p.MaterialType == "物料" && p.
                    //Tasktype == "退库盘点" && p.Plandate >= dateTime && p.Plandate <= end);
                    //if (d == null || tkd.Count <= 0)
                    //{
                    //    //这里直接结束盘点任务
                    //    result.msg = "请确认当天是否进行了退库盘点";
                    //    return result;
                    //}
                    #region 这里退库盘点判断
                    //查询当前是否存在退库盘点任务，存在直接结束掉
                    var tkd = await _dalVerifiyListEntity.FindList(p => p.MaterialType == "物料" && p.Tasktype == "退库盘点" && p.Plandate >= dateTime && p.Plandate <= end);
                    if (tkd != null || tkd.Count > 0)
                    {
                        //退库盘点如果存在执行数据执行添加数据
                        string taskStatus = string.Empty;
                        for (int i = 0; i < tkd.Count; i++)
                        {
                            taskStatus = tkd[i].TaskStatus;
                            if (taskStatus != "已同步")
                            {
                                result.msg = "当前存在未完成的退库任务，无法创建大盘点任务";
                                return result;

                                //string id = tkd[i].ID;
                                //VerifiyListEntity upVModel = tkd[i];
                                //upVModel.Modify(id, _user.Name);
                                //upVModel.TaskStatus = "已同步";
                                //updateList.Add(upVModel);
                            }
                        }
                    }

                    #endregion



                    #region 平移库存

                    var inventData = await _dalInventorylistingViewEntity.FindList(p => (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrEmpty(p.BatchId2)) && p.LocationS.Contains("PKG3"));
                    inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
                    ).ToList();
                    inventData = inventData.Where(P => P.Quantity != null && (P.ClassCode == "ZPKG"|| P.ClassCode == "ZLBS")).ToList(); //ZPKG 物料  /ZLBS物料标签类
                    if (inventData != null && inventData.Count > 0)
                    {
                        for (int i = 0; i < inventData.Count; i++)
                        {
                            #region 新增数据

                            VerifiyDetailEntity dmodel = new VerifiyDetailEntity();

                            if (inventData[i].Quantity == null)
                            {
                                dmodel.CurrentQuantity = 0;
                                dmodel.ActualQuantity = 0;
                            }
                            else
                            {
                                dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                                dmodel.ActualQuantity = inventData[i].Quantity.Value;
                            }

                            dmodel.CreateCustomGuid(_user.Name);
                            dmodel.MaterialId = inventData[i].MaterialId;
                            dmodel.SublotId = inventData[i].SlotId;
                            dmodel.BatchId = inventData[i].LotId;
                            dmodel.EquipmentId = inventData[i].EquipmentId;
                            dmodel.Result = " ";
                            //dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                            //dmodel.ActualQuantity = inventData[i].Quantity.Value;
                            dmodel.UnitId = inventData[i].UId;
                            dmodel.Difference = 0;
                            dmodel.Reason = " ";
                            dmodel.VerifiylistId = lID;
                            dList.Add(dmodel);

                            #endregion
                        }
                    }

                    #endregion
                }
                _unitOfWork.BeginTran();

                //完成退库盘点
                //if (updateList.Count > 0)
                //{
                //    upResult = await _dalVerifiyListEntity.Update(updateList);
                //}

                if (vList.Count > 0)
                {
                    vResult = await _dalVerifiyListEntity.Add(vList) > 0;
                }

                if (dList.Count > 0)
                {
                    dResult = await _dal.Add(dList) > 0;
                }

                if (upResult && dResult && vResult)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "创建盘点任务成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建盘点任务失败";
                    return result;
                }


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "创建盘点任务失败" + ex;
                return result;

            }

        }

        public async Task<MessageModel<string>> Add_WLOLD(VerifiyListRequestModel entity)
        {

            var result = new MessageModel<string>();
            result.success = false;

            if (string.IsNullOrEmpty(entity.Plandate))
            {
                result.msg = "请选择盘点时间";
                return result;
            }
            if (string.IsNullOrEmpty(entity.Tasktype))
            {
                result.msg = "请选择类型";
                return result;
            }


            //判断当前是否有未结束的数据
            var d = await _dalVerifiyListEntity.FindList(p => p.TaskStatus != "已同步" && p.MaterialType == "物料");
            if (d != null && d.Count > 0)
            {
                result.msg = "当前存在尚未结束";
                return result;
            }

            try
            {
                string lID = string.Empty;
                List<VerifiyListEntity> vList = new List<VerifiyListEntity>();
                List<VerifiyDetailEntity> dList = new List<VerifiyDetailEntity>();
                bool vResult = true;
                bool dResult = true;

                VerifiyListEntity model = new VerifiyListEntity();
                model.CreateCustomGuid(_user.Name);
                model.Plandate = Convert.ToDateTime(entity.Plandate);
                //model.TaskStatus = entity.TaskStatus;
                model.MaterialType = "物料";
                model.TaskStatus = "已创建";
                model.Tasktype = "大盘点";
                lID = model.ID;
                vList.Add(model);

                #region 平移库存

                var inventData = await _dalInventorylistingViewEntity.FindList(p => (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)) && p.LocationS.Contains("PKG3"));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
                ).ToList();
                inventData = inventData.Where(P => P.Quantity != null).ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    for (int i = 0; i < inventData.Count; i++)
                    {
                        #region 新增数据

                        VerifiyDetailEntity dmodel = new VerifiyDetailEntity();

                        if (inventData[i].Quantity == null)
                        {
                            dmodel.CurrentQuantity = 0;
                            dmodel.ActualQuantity = 0;
                        }
                        else
                        {
                            dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                            dmodel.ActualQuantity = inventData[i].Quantity.Value;
                        }

                        dmodel.CreateCustomGuid(_user.Name);
                        dmodel.MaterialId = inventData[i].MaterialId;
                        dmodel.SublotId = inventData[i].SlotId;
                        dmodel.BatchId = inventData[i].LotId;
                        dmodel.EquipmentId = inventData[i].EquipmentId;
                        dmodel.Result = " ";
                        //dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                        //dmodel.ActualQuantity = inventData[i].Quantity.Value;
                        dmodel.UnitId = inventData[i].UId;
                        dmodel.Difference = 0;
                        dmodel.Reason = " ";
                        dmodel.VerifiylistId = lID;
                        dList.Add(dmodel);

                        #endregion
                    }
                }

                #endregion


                if (vList.Count > 0)
                {
                    vResult = await _dalVerifiyListEntity.Add(vList) > 0;
                }

                if (dList.Count > 0)
                {
                    dResult = await _dal.Add(dList) > 0;
                }

                if (dResult && vResult)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "创建盘点任务成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建盘点任务失败";
                    return result;
                }


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "创建盘点任务失败" + ex;
                return result;

            }

        }

        public async Task<MessageModel<string>> Delete_WL(VerifiyListRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.IDS == null && entity.IDS.Length <= 0)
            {
                result.msg = "请选中删除的数据";
                return result;
            }

            var vList = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>().In(p => p.ID, entity.IDS).Where(p => p.TaskStatus == "已调整" || p.TaskStatus == "已同步").ToListAsync();

            if (vList != null && vList.Count > 0)
            {
                result.msg = "删除失败，存在已调整或已同步计划";
                return result;
            }

            bool v = true;
            bool vD = true;
            try
            {
                _unitOfWork.BeginTran();
                //查询明细数据
                var dData = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.VerifiylistId, entity.IDS).ToListAsync();
                string[] dIDS = dData.GroupBy(P => P.ID).Select(P => P.Key).ToArray();
                if (dIDS != null && dIDS.Length > 0)
                {
                    vD = await _dal.DeleteByIds(dIDS);
                }
                v = await _dalVerifiyListEntity.DeleteByIds(entity.IDS);
                if (v && vD)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "删除成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "删除失败";
                    return result;
                }

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "删除失败" + ex;
                return result;
            }
        }



        public async Task<List<VerifiyDetailViewEntity>> GetWL_DetailByID(string id)
        {
            List<VerifiyDetailViewEntity> result = new List<VerifiyDetailViewEntity>();
            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(id), a => a.VerifiylistId == id).ToExpression();
            var data = await _dal.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

            return data;
        }

        public async Task<MessageModel<string>> AddDetail_WL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entity.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                if (string.IsNullOrEmpty(entity.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entity.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续盘点", listModel.TaskStatus);
                    return result;
                }


                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entity.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)));
                inventData = inventData.Where(a => a.LocationS.Contains("PKG3")).ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entity.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = " ";
                            model.CurrentQuantity = inventData[0].Quantity.Value;
                            model.ActualQuantity = !string.IsNullOrEmpty(entity.Qty) ? 0 : Math.Round(Convert.ToDecimal(entity.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = 0;
                            model.Reason = entity.Reason;
                            model.VerifiylistId = entity.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            var verifiy = _dalVerifiyListEntity.FindEntity(p => p.ID == entity.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = new VerifiyListEntity();
                                vModel.Modify(entity.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else
                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entity.SSCC;
                        return result;
                    }
                }
                else
                {
                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "添加失败";
                return result;
            }



        }

        public async Task<MessageModel<string>> ModifyDetail_WL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }


            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {
                string types = Verifiy.Inventtype;

                if (types == "已退仓")
                {
                    result.msg = "当前数据已退仓，无法执行当前操作";
                    return result;
                }


                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }

                #region 这里处理逻辑（新增、差异）

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                //if (model.Result == "新增")
                //{
                //    //查询当前数据是否存在
                //    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.Sscc == entity.SSCC);
                //    if (inventoryModel == null)
                //    {
                //        #region 创建库存信息

                //        MaterialInventoryEntity request = new MaterialInventoryEntity();
                //        request.Quantity = Math.Round(Convert.ToDecimal(entity.Qty), 3);
                //        request.Create(_user.Name.ToString());
                //        request.QuantityUomId = inventoryModel.UId;
                //        request.EquipmentId = inventoryModel.EquipmentId;
                //        request.SublotId = inventoryModel.SlotId;
                //        request.LotId = inventoryModel.LotId;

                //        #endregion

                //        #region 写入转移历史

                //        var lotEntity = await _dalMaterialLotEntity.FindList(p => p.ID == Verifiy.BatchId);
                //        if (lotEntity == null || lotEntity.Count <= 0)
                //        {

                //        }
                //        else
                //        {
                //            insetList.Add(request);
                //            //写入历史记录
                //            MaterialTransferEntity trans = new MaterialTransferEntity();
                //            trans.Create(_user.Name.ToString());
                //            //trans.ID = Guid.NewGuid().ToString();
                //            //  trans.OldStorageLocation = storage_location;
                //            // trans.NewStorageLocation = storage_location;
                //            trans.OldLotId = "";
                //            trans.OldSublotId = "";
                //            trans.OldExpirationDate = null;
                //            trans.NewExpirationDate = lotEntity[0].ExpirationDate;
                //            trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                //            trans.QuantityUomId = request.QuantityUomId;
                //            //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //            trans.Type = "Create Inventory";
                //            trans.Comment = "库存盘存-创建";
                //            //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.TransferGroupId
                //            trans.OldEquipmentId = "";
                //            trans.NewEquipmentId = request.EquipmentId;
                //            trans.OldContainerId = "";
                //            trans.NewContainerId = "";
                //            trans.OldLotExternalStatus = "";
                //            trans.OldSublotExternalStatus = "";
                //            trans.NewMaterialId = lotEntity[0].MaterialId;
                //            trans.OldMaterialId = lotEntity[0].MaterialId;
                //            trans.NewLotExternalStatus = "2";
                //            trans.NewSublotId = Verifiy.SublotId;
                //            trans.NewLotId = Verifiy.BatchId;
                //            trans.NewSublotExternalStatus = "3";
                //            transferList.Add(trans);
                //        }


                //        #endregion
                //    }
                //    else
                //    {
                //        #region 更新

                //        //更新数量
                //        MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                //        upInvent.Modify(upInvent.ID, _user.Name);
                //        upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                //        InventListup.Add(upInvent);

                //        #region 写入转移历史    

                //        //写入历史记录
                //        MaterialTransferEntity trans = new MaterialTransferEntity();
                //        trans.Create(_user.Name.ToString());
                //        //trans.ID = Guid.NewGuid().ToString();
                //        trans.OldStorageLocation = inventoryModel.LocationF;
                //        trans.NewStorageLocation = inventoryModel.LocationF;
                //        trans.OldLotId = inventoryModel.LotId;
                //        trans.NewLotId = inventoryModel.LotId;
                //        trans.OldSublotId = inventoryModel.SlotId;
                //        trans.NewSublotId = inventoryModel.SlotId;
                //        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                //        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                //        trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                //        trans.QuantityUomId = inventoryModel.QuantityUomId;
                //        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //        trans.Type = "Inventory Update Quantity";
                //        trans.Comment = "库存-盘存新增更新数量";
                //        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        //trans.TransferGroupId
                //        trans.OldEquipmentId = inventoryModel.EquipmentId;
                //        trans.NewEquipmentId = inventoryModel.EquipmentId;
                //        trans.OldContainerId = inventoryModel.ContainerId;
                //        trans.NewContainerId = inventoryModel.ContainerId;                    //status
                //        trans.OldMaterialId = inventoryModel.MaterialId;
                //        trans.NewMaterialId = inventoryModel.MaterialId;
                //        trans.OldLotExternalStatus = inventoryModel.StatusF;
                //        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                //        trans.NewLotExternalStatus = inventoryModel.StatusF;
                //        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                //        trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                //        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                //        transferList.Add(trans);
                //        #endregion

                //        #endregion
                //    }
                //}
                //else 

                if (model.Result == "差异")
                {
                    //查询当前数据是否存在
                    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == Verifiy.SublotId);
                    #region 更新

                    if (inventoryModel == null)
                    {
                        result.msg = "请确认库存信息是否存在";
                        return result;
                    }

                    //更新数量
                    MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                    upInvent.Modify(upInvent.ID, _user.Name);
                    upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                    InventListup.Add(upInvent);

                    #region 写入转移历史    

                    #region 记录差值

                    decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    decimal oldQty = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);

                    string stringQty = string.Empty;
                    decimal cha = 0;

                    if (newQty > oldQty)
                    {
                        cha = newQty - oldQty;
                        stringQty = oldQty + "+" + cha;
                    }
                    else if (newQty < oldQty)
                    {
                        cha = oldQty - newQty;
                        stringQty = oldQty + "-" + cha;
                    }

                    if (cha == 0)
                    {
                        stringQty = string.Empty;
                    }

                    #endregion

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Inventory Update Quantity" + stringQty;
                    trans.Comment = "库存-盘存新增更新数量" + stringQty;
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                    transferList.Add(trans);
                    #endregion

                    #endregion
                }

                #endregion

                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); // entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }



                bool r = await _dal.Update(model);

                #region 这里执行新增和更新库存信息

                bool r2 = true;
                bool r3 = true;
                bool r4 = true;

                if (InventListup.Count > 0)
                {
                    r2 = await _dalMaterialInventoryEntity.Update(InventListup);
                }
                if (transferList.Count > 0)
                {
                    r3 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                }
                if (insetList.Count > 0)
                {
                    r4 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                }
                #endregion

                if (r == true && v && r2 && r3 && r4)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }


        }

        public async Task<MessageModel<string>> ModifyDetail_WLOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }

            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {

                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }

                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); // entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }



                bool r = await _dal.Update(model);
                if (r == true && v)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }


        }

        #endregion

        #endregion

        #region 主页保存

        /// <summary>
        /// 这里是更新和删除当前盘点任务下库存数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> UpInVentAll(VerifiyListRequestModel reqModel)
        {
            //查询对应数据是否存在
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.IDS == null && reqModel.IDS.Length <= 0)
                {
                    result.msg = "请选择需要保存的数据";
                    return result;
                }
                //查询所有数据
                var data = await _dal.Db.Queryable<VerifiyListEntity>().In(p => p.ID, reqModel.IDS).ToListAsync();

                int count = data.Where(p => p.TaskStatus == "盘点中").Count();
                if (count <= 0)
                {
                    result.msg = "请确认选中任务状态是否为盘点中";
                    return result;
                }

                var detailData = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.VerifiylistId, reqModel.IDS).ToListAsync();

                #region 操作数据库

                if (detailData == null || detailData.Count <= 0)
                {
                    result.msg = "无需同步修改";
                    return result;
                }
                var search = detailData.Where(p => p.Result.Trim() == string.Empty || p.Result == null).Count();
                if (search > 0)
                {
                    result.msg = "盘点计划未完成";
                    return result;
                }

                List<VerifiyListEntity> VlistUp = new List<VerifiyListEntity>();
                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<string> listIDS = new List<string>();
                //改任务状态
                for (int i = 0; i < data.Count; i++)
                {
                    string id = data[i].ID;
                    //更新状态
                    VerifiyListEntity upModel = data[i];
                    upModel.Modify(id, _user.Name);
                    upModel.TaskStatus = "已调整";
                    upModel.Plandate = data[i].Plandate;
                    upModel.MaterialType = data[i].MaterialType;
                    VlistUp.Add(upModel);

                    //查询数据(筛选明细数据) 差异更新，缺失删除
                    var detailDataByIDList = detailData.Where(p => p.VerifiylistId == id && (p.Result == "差异" || p.Result == "缺失" || p.Result == "新增")).ToList();
                    if (detailDataByIDList != null && detailDataByIDList.Count > 0)
                    {
                        string[] sLotID = detailDataByIDList.GroupBy(P => P.SublotId).Select(g => g.Key).ToArray();

                        //查询库存信息
                        var inventData = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(p => p.SlotId, sLotID).ToListAsync();

                        var vData = await _dalMaterialInventoryEntity.Db.Queryable<MaterialInventoryEntity>().In(p => p.SublotId, sLotID).ToListAsync();
                        string resultType = string.Empty;
                        //循环构造
                        for (int j = 0; j < detailDataByIDList.Count; j++)
                        {
                            string sbID = detailDataByIDList[j].SublotId;

                            var inventoryModel = inventData.Where(p => p.SlotId == sbID).FirstOrDefault();

                            resultType = detailDataByIDList[j].Result;

                            if (inventoryModel != null)
                            {
                                if (resultType == "差异")
                                {
                                    #region 更新

                                    //更新数量
                                    MaterialInventoryEntity upInvent = vData.Where(p => p.SublotId == sbID).FirstOrDefault();
                                    upInvent.Modify(inventoryModel.ID, _user.Name);
                                    upInvent.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                                    InventListup.Add(upInvent);

                                    #region 写入转移历史    

                                    #region 记录差值

                                    decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                                    decimal oldQty = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);

                                    string stringQty = string.Empty;
                                    decimal cha = 0;

                                    if (newQty > oldQty)
                                    {
                                        cha = newQty - oldQty;
                                        stringQty = oldQty + "+" + cha;
                                    }
                                    else if (newQty < oldQty)
                                    {
                                        cha = oldQty - newQty;
                                        stringQty = oldQty + "-" + cha;
                                    }

                                    if (cha == 0)
                                    {
                                        stringQty = string.Empty;
                                    }

                                    #endregion

                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    trans.OldStorageLocation = inventoryModel.LocationF;
                                    trans.NewStorageLocation = inventoryModel.LocationF;
                                    trans.OldLotId = inventoryModel.LotId;
                                    trans.NewLotId = inventoryModel.LotId;
                                    trans.OldSublotId = inventoryModel.SlotId;
                                    trans.NewSublotId = inventoryModel.SlotId;
                                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                                    trans.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);
                                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Inventory Update Quantity" + stringQty;
                                    trans.Comment = "库存-盘存更新数量";
                                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                                    trans.OldContainerId = inventoryModel.ContainerId;
                                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                                    trans.OldMaterialId = inventoryModel.MaterialId;
                                    trans.NewMaterialId = inventoryModel.MaterialId;
                                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                                    transferList.Add(trans);
                                    #endregion

                                    #endregion
                                }
                                else if (resultType == "缺失")
                                {
                                    #region 删除
                                    if (!string.IsNullOrEmpty(inventoryModel.ID))
                                    {
                                        listIDS.Add(inventoryModel.ID);
                                    }

                                    #region 写入转移历史    

                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    trans.OldStorageLocation = inventoryModel.LocationF;
                                    trans.NewStorageLocation = inventoryModel.LocationF;
                                    trans.OldLotId = inventoryModel.LotId;
                                    trans.NewLotId = inventoryModel.LotId;
                                    trans.OldSublotId = inventoryModel.SlotId;
                                    trans.NewSublotId = inventoryModel.SlotId;
                                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                                    trans.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);
                                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Inventory Delete";
                                    trans.Comment = "盘存-删除库存";
                                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                                    trans.OldContainerId = inventoryModel.ContainerId;
                                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                                    trans.OldMaterialId = inventoryModel.MaterialId;
                                    trans.NewMaterialId = inventoryModel.MaterialId;
                                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                                    transferList.Add(trans);
                                    #endregion

                                    #endregion
                                }
                                else if (resultType == "新增")
                                {
                                    //新增 如果存在增加数量，如果不存在创建
                                    #region 更新

                                    //更新数量
                                    MaterialInventoryEntity upInvent = vData.Where(p => p.SublotId == sbID).FirstOrDefault();
                                    upInvent.Modify(inventoryModel.ID, _user.Name);
                                    upInvent.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                                    InventListup.Add(upInvent);

                                    #region 写入转移历史    

                                    #region 记录差值

                                    decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                                    decimal oldQty = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);

                                    string stringQty = string.Empty;
                                    decimal cha = 0;

                                    if (newQty > oldQty)
                                    {
                                        cha = newQty - oldQty;
                                        stringQty = oldQty + "+" + cha;
                                    }
                                    else if (newQty < oldQty)
                                    {
                                        cha = oldQty - newQty;
                                        stringQty = oldQty + "-" + cha;
                                    }

                                    if (cha == 0)
                                    {
                                        stringQty = string.Empty;
                                    }

                                    #endregion

                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    trans.OldStorageLocation = inventoryModel.LocationF;
                                    trans.NewStorageLocation = inventoryModel.LocationF;
                                    trans.OldLotId = inventoryModel.LotId;
                                    trans.NewLotId = inventoryModel.LotId;
                                    trans.OldSublotId = inventoryModel.SlotId;
                                    trans.NewSublotId = inventoryModel.SlotId;
                                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                                    trans.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);
                                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Inventory Update Quantity";
                                    trans.Comment = "库存-盘存新增更新数量" + stringQty;
                                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                                    trans.OldContainerId = inventoryModel.ContainerId;
                                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                                    trans.OldMaterialId = inventoryModel.MaterialId;
                                    trans.NewMaterialId = inventoryModel.MaterialId;
                                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                                    transferList.Add(trans);
                                    #endregion

                                    #endregion

                                }
                            }
                            else
                            {
                                if (resultType == "新增")
                                {
                                    #region 创建库存信息

                                    MaterialInventoryEntity request = new MaterialInventoryEntity();
                                    request.Quantity = Math.Round(Convert.ToDecimal(detailDataByIDList[j].ActualQuantity), 3);
                                    request.Create(_user.Name.ToString());
                                    request.QuantityUomId = detailDataByIDList[j].UnitId;
                                    request.EquipmentId = detailDataByIDList[j].EquipmentId;
                                    request.SublotId = detailDataByIDList[j].SublotId;
                                    request.LotId = detailDataByIDList[j].BatchId;

                                    #endregion

                                    #region 写入转移历史

                                    var lotEntity = await _dalMaterialLotEntity.FindList(p => p.ID == detailDataByIDList[j].BatchId);
                                    if (lotEntity == null || lotEntity.Count <= 0)
                                    {
                                        continue;
                                    }
                                    else
                                    {
                                        insetList.Add(request);
                                        //写入历史记录
                                        MaterialTransferEntity trans = new MaterialTransferEntity();
                                        trans.Create(_user.Name.ToString());
                                        //trans.ID = Guid.NewGuid().ToString();
                                        //  trans.OldStorageLocation = storage_location;
                                        // trans.NewStorageLocation = storage_location;
                                        trans.OldLotId = "";
                                        trans.OldSublotId = "";
                                        trans.OldExpirationDate = null;
                                        trans.NewExpirationDate = lotEntity[0].ExpirationDate;
                                        trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                                        trans.QuantityUomId = request.QuantityUomId;
                                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                        trans.Type = "Create Inventory";
                                        trans.Comment = "库存盘存-创建";
                                        //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                        //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                        //trans.TransferGroupId
                                        trans.OldEquipmentId = "";
                                        trans.NewEquipmentId = request.EquipmentId;
                                        trans.OldContainerId = "";
                                        trans.NewContainerId = "";
                                        trans.OldLotExternalStatus = "";
                                        trans.OldSublotExternalStatus = "";
                                        trans.NewMaterialId = lotEntity[0].MaterialId;
                                        trans.OldMaterialId = lotEntity[0].MaterialId;
                                        trans.NewLotExternalStatus = "2";
                                        trans.NewSublotId = detailDataByIDList[j].SublotId;
                                        trans.NewLotId = detailDataByIDList[j].BatchId;
                                        trans.NewSublotExternalStatus = "3";
                                        transferList.Add(trans);
                                    }


                                    #endregion
                                }
                            }
                        }
                    }
                }
                try
                {
                    _unitOfWork.BeginTran();

                    bool r1 = true;
                    bool r2 = true;
                    bool r3 = true;
                    bool r4 = true;
                    bool r5 = true;
                    if (VlistUp.Count > 0)
                    {
                        r1 = await _dalVerifiyListEntity.Update(VlistUp);
                    }
                    if (InventListup.Count > 0)
                    {
                        //           r2 = await _dalMaterialInventoryEntity.Update(InventListup);
                    }
                    if (transferList.Count > 0)
                    {
                        //         r3 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                    }
                    if (listIDS.Count > 0)
                    {
                        //      string[] dIDS = listIDS.ToArray();
                        //          r4 = await _dalMaterialInventoryEntity.DeleteByIds(dIDS);
                    }
                    if (insetList.Count > 0)
                    {
                        //        r5 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                    }

                    if (r1 && r2 && r3 && r4 && r5)
                    {
                        _unitOfWork.CommitTran();
                        result.success = true;
                        result.msg = "更新库存成功";
                        return result;
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "更新库存信息失败";
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "更新库存信息失败" + ex.Message;
                    return result;
                }



                #endregion
            }
            catch (Exception)
            {

                throw;
            }
        }

        //报工到sap
        public async Task<MessageModel<string>> SapReportWork(VerifiyListRequestModel reqModel)
        {
            //查询对应数据是否存在
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.IDS == null && reqModel.IDS.Length <= 0)
                {
                    result.msg = "请选择需要保存的数据";
                    return result;
                }
                //查询所有数据
                var data = await _dal.Db.Queryable<VerifiyListEntity>().In(p => p.ID, reqModel.IDS).ToListAsync();

                int count = data.Where(p => p.TaskStatus != "已调整").Count();
                if (count > 0)
                {
                    result.msg = "请确认选中任务是否都为已调整数据";
                    return result;
                }

                var detailData = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.VerifiylistId, reqModel.IDS).ToListAsync();

                #region 操作数据库

                if (detailData == null || detailData.Count <= 0)
                {
                    result.msg = "无需同步修改";
                    return result;
                }
                List<VerifiyListEntity> VlistUp = new List<VerifiyListEntity>();
                //改任务状态
                for (int i = 0; i < data.Count; i++)
                {
                    string id = data[i].ID;
                    //更新状态
                    VerifiyListEntity upModel = data[i];
                    upModel.Modify(id, _user.Name);
                    upModel.TaskStatus = "已同步";
                    VlistUp.Add(upModel);
                }
                try
                {
                    _unitOfWork.BeginTran();

                    bool r1 = true;
                    bool r2 = true;
                    bool r3 = true;
                    if (VlistUp.Count > 0)
                    {
                        r1 = await _dalVerifiyListEntity.Update(VlistUp);
                    }
                    if (r1 && r2 && r3)
                    {
                        _unitOfWork.CommitTran();
                        result.success = true;
                        result.msg = "更新库存成功";
                        return result;
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "更新库存信息失败";
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "更新库存信息失败" + ex.Message;
                    return result;
                }



                #endregion
            }
            catch (Exception)
            {

                throw;
            }
        }

        #endregion

        #region 新增保存

        #region 自动创建


        public async Task<MessageModel<string>> NewAddDetail_WL(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)));
                inventData = inventData.Where(a => a.LocationS.Contains("PKG3")).ToList();

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();

                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            bool r1 = true;
                            bool r2 = true;

                            #region 更新数据
                            var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == model.SublotId);
                            #region 更新

                            //更新数量
                            MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == model.SublotId);
                            upInvent.Modify(upInvent.ID, _user.Name);
                            upInvent.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                            InventListup.Add(upInvent);


                            #region 记录差值

                            decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                            decimal oldQty = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);

                            string stringQty = string.Empty;
                            decimal cha = 0;

                            if (newQty > oldQty)
                            {
                                cha = newQty - oldQty;
                                stringQty = oldQty + "+" + cha;
                            }
                            else if (newQty < oldQty)
                            {
                                cha = oldQty - newQty;
                                stringQty = oldQty + "-" + cha;
                            }

                            if (cha == 0)
                            {
                                stringQty = string.Empty;
                            }

                            #endregion

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Update Quantity";
                            trans.Comment = "库存-盘存新增更新数量" + stringQty;
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);
                            #endregion

                            #endregion

                            #endregion

                            _unitOfWork.BeginTran();

                            if (InventListup.Count > 0)
                            {
                                r1 = await _dalMaterialInventoryEntity.Update(InventListup);
                            }
                            if (transferList.Count > 0)
                            {
                                r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                            }

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult && r1 && r2)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }
                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;
                                bool r1 = true;
                                bool r2 = true;

                                #region 创建库存

                                #region 创建库存信息

                                MaterialInventoryEntity request = new MaterialInventoryEntity();
                                request.Quantity = Math.Round(Convert.ToDecimal(entit.Qty), 3);
                                request.Create(_user.Name.ToString());
                                request.QuantityUomId = model.UnitId;
                                request.EquipmentId = model.EquipmentId;
                                request.SublotId = model.SublotId;
                                request.LotId = model.BatchId;

                                #region 写入转移历史

                                var lotEntitys = await _dalMaterialLotEntity.FindList(p => p.ID == request.LotId);
                                if (lotEntitys == null || lotEntitys.Count <= 0)
                                {

                                }
                                else
                                {
                                    insetList.Add(request);
                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    //  trans.OldStorageLocation = storage_location;
                                    // trans.NewStorageLocation = storage_location;
                                    trans.OldLotId = "";
                                    trans.OldSublotId = "";
                                    trans.OldExpirationDate = null;
                                    trans.NewExpirationDate = lotEntitys[0].ExpirationDate;
                                    trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                                    trans.QuantityUomId = request.QuantityUomId;
                                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Create Inventory";
                                    trans.Comment = "库存盘存-创建";
                                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = "";
                                    trans.NewEquipmentId = request.EquipmentId;
                                    trans.OldContainerId = "";
                                    trans.NewContainerId = "";
                                    trans.OldLotExternalStatus = "";
                                    trans.OldSublotExternalStatus = "";
                                    trans.NewMaterialId = lotEntitys[0].MaterialId;
                                    trans.OldMaterialId = lotEntitys[0].MaterialId;
                                    trans.NewLotExternalStatus = "2";
                                    trans.NewSublotId = request.SublotId;
                                    trans.NewLotId = request.LotId;
                                    trans.NewSublotExternalStatus = "3";
                                    transferList.Add(trans);
                                }


                                #endregion

                                #endregion


                                #endregion

                                _unitOfWork.BeginTran();

                                if (insetList.Count > 0)
                                {
                                    r1 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                                }
                                if (transferList.Count > 0)
                                {
                                    r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                                }

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult && r1 && r2)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "添加失败";
                return result;
            }



        }

        public async Task<MessageModel<string>> NewAddDetail_YL(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
                && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4" && p.IsThorat != "1").ToList();

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();

                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);// entit.Difference;
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            bool r1 = true;
                            bool r2 = true;

                            #region 更新数据
                            var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == model.SublotId);
                            #region 更新

                            //更新数量
                            MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == model.SublotId);
                            upInvent.Modify(upInvent.ID, _user.Name);
                            upInvent.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                            InventListup.Add(upInvent);




                            #region 记录差值

                            decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                            decimal oldQty = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);

                            string stringQty = string.Empty;
                            decimal cha = 0;

                            if (newQty > oldQty)
                            {
                                cha = newQty - oldQty;
                                stringQty = oldQty + "+" + cha;
                            }
                            else if (newQty < oldQty)
                            {
                                cha = oldQty - newQty;
                                stringQty = oldQty + "-" + cha;
                            }

                            if (cha == 0)
                            {
                                stringQty = string.Empty;
                            }

                            #endregion

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Update Quantity";
                            trans.Comment = "库存-盘存新增更新数量" + stringQty;
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);
                            #endregion

                            #endregion

                            #endregion

                            _unitOfWork.BeginTran();



                            if (InventListup.Count > 0)
                            {
                                r1 = await _dalMaterialInventoryEntity.Update(InventListup);
                            }
                            if (transferList.Count > 0)
                            {
                                r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                            }

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult && r1 && r2)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else
                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;
                                bool r1 = true;
                                bool r2 = true;

                                #region 创建库存

                                #region 创建库存信息

                                MaterialInventoryEntity request = new MaterialInventoryEntity();
                                request.Quantity = Math.Round(Convert.ToDecimal(entit.Qty), 3);
                                request.Create(_user.Name.ToString());
                                request.QuantityUomId = model.UnitId;
                                request.EquipmentId = model.EquipmentId;
                                request.SublotId = model.SublotId;
                                request.LotId = model.BatchId;

                                #region 写入转移历史

                                var lotEntitys = await _dalMaterialLotEntity.FindList(p => p.ID == request.LotId);
                                if (lotEntitys == null || lotEntitys.Count <= 0)
                                {

                                }
                                else
                                {
                                    insetList.Add(request);
                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    //  trans.OldStorageLocation = storage_location;
                                    // trans.NewStorageLocation = storage_location;
                                    trans.OldLotId = "";
                                    trans.OldSublotId = "";
                                    trans.OldExpirationDate = null;
                                    trans.NewExpirationDate = lotEntitys[0].ExpirationDate;
                                    trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                                    trans.QuantityUomId = request.QuantityUomId;
                                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Create Inventory";
                                    trans.Comment = "库存盘存-创建";
                                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = "";
                                    trans.NewEquipmentId = request.EquipmentId;
                                    trans.OldContainerId = "";
                                    trans.NewContainerId = "";
                                    trans.OldLotExternalStatus = "";
                                    trans.OldSublotExternalStatus = "";
                                    trans.NewMaterialId = lotEntitys[0].MaterialId;
                                    trans.OldMaterialId = lotEntitys[0].MaterialId;
                                    trans.NewLotExternalStatus = "2";
                                    trans.NewSublotId = request.SublotId;
                                    trans.NewLotId = request.LotId;
                                    trans.NewSublotExternalStatus = "3";
                                    transferList.Add(trans);
                                }


                                #endregion

                                #endregion


                                #endregion

                                _unitOfWork.BeginTran();


                                if (insetList.Count > 0)
                                {
                                    r1 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                                }
                                if (transferList.Count > 0)
                                {
                                    r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                                }

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult && r1 && r2)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "添加失败";
                return result;
            }



        }


        public async Task<MessageModel<string>> NewAddDetail_HT(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS == "SUR3").ToList();

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();

                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);// entit.Difference;
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            bool r1 = true;
                            bool r2 = true;

                            #region 更新数据
                            var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == model.SublotId);
                            #region 更新

                            //更新数量
                            MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == model.SublotId);
                            upInvent.Modify(upInvent.ID, _user.Name);
                            upInvent.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                            InventListup.Add(upInvent);


                            #region 记录差值

                            decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                            decimal oldQty = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);

                            string stringQty = string.Empty;
                            decimal cha = 0;

                            if (newQty > oldQty)
                            {
                                cha = newQty - oldQty;
                                stringQty = oldQty + "+" + cha;
                            }
                            else if (newQty < oldQty)
                            {
                                cha = oldQty - newQty;
                                stringQty = oldQty + "-" + cha;
                            }

                            if (cha == 0)
                            {
                                stringQty = string.Empty;
                            }

                            #endregion

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(model.ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Update Quantity";
                            trans.Comment = "库存-盘存新增更新数量" + stringQty;
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);
                            #endregion

                            #endregion

                            #endregion

                            _unitOfWork.BeginTran();

                            if (InventListup.Count > 0)
                            {
                                r1 = await _dalMaterialInventoryEntity.Update(InventListup);
                            }
                            if (transferList.Count > 0)
                            {
                                r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                            }

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult && r1 && r2)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;
                                bool r1 = true;
                                bool r2 = true;

                                #region 创建库存

                                #region 创建库存信息

                                MaterialInventoryEntity request = new MaterialInventoryEntity();
                                request.Quantity = Math.Round(Convert.ToDecimal(entit.Qty), 3);
                                request.Create(_user.Name.ToString());
                                request.QuantityUomId = model.UnitId;
                                request.EquipmentId = model.EquipmentId;
                                request.SublotId = model.SublotId;
                                request.LotId = model.BatchId;

                                #region 写入转移历史

                                var lotEntitys = await _dalMaterialLotEntity.FindList(p => p.ID == request.LotId);
                                if (lotEntitys == null || lotEntitys.Count <= 0)
                                {

                                }
                                else
                                {
                                    insetList.Add(request);
                                    //写入历史记录
                                    MaterialTransferEntity trans = new MaterialTransferEntity();
                                    trans.Create(_user.Name.ToString());
                                    //trans.ID = Guid.NewGuid().ToString();
                                    //  trans.OldStorageLocation = storage_location;
                                    // trans.NewStorageLocation = storage_location;
                                    trans.OldLotId = "";
                                    trans.OldSublotId = "";
                                    trans.OldExpirationDate = null;
                                    trans.NewExpirationDate = lotEntitys[0].ExpirationDate;
                                    trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                                    trans.QuantityUomId = request.QuantityUomId;
                                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                    trans.Type = "Create Inventory";
                                    trans.Comment = "库存盘存-创建";
                                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                    //trans.TransferGroupId
                                    trans.OldEquipmentId = "";
                                    trans.NewEquipmentId = request.EquipmentId;
                                    trans.OldContainerId = "";
                                    trans.NewContainerId = "";
                                    trans.OldLotExternalStatus = "";
                                    trans.OldSublotExternalStatus = "";
                                    trans.NewMaterialId = lotEntitys[0].MaterialId;
                                    trans.OldMaterialId = lotEntitys[0].MaterialId;
                                    trans.NewLotExternalStatus = "2";
                                    trans.NewSublotId = request.SublotId;
                                    trans.NewLotId = request.LotId;
                                    trans.NewSublotExternalStatus = "3";
                                    transferList.Add(trans);
                                }


                                #endregion

                                #endregion


                                #endregion

                                _unitOfWork.BeginTran();

                                if (insetList.Count > 0)
                                {
                                    r1 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                                }
                                if (transferList.Count > 0)
                                {
                                    r2 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                                }

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult && r1 && r2)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "添加失败";
                return result;
            }



        }

        #endregion


        public async Task<MessageModel<string>> NewAddDetail_WLOLD(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)));
                inventData = inventData.Where(a => a.LocationS.Contains("PKG3")).ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }
                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;

                                _unitOfWork.BeginTran();

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "添加失败";
                return result;
            }



        }

        public async Task<MessageModel<string>> NewAddDetail_YLOLD(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
                && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4").ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);// entit.Difference;
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;

                                _unitOfWork.BeginTran();

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "添加失败";
                return result;
            }



        }


        public async Task<MessageModel<string>> NewAddDetail_HTOLD(VerifiyDetailRequestModel entit)
        {
            //SSCC
            //VerifiyListID
            //Qty
            //Reason
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entit.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                entit.ID = entit.VerifiyListID;
                if (string.IsNullOrEmpty(entit.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entit.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续创建", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entit.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS == "SUR3").ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entit.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }
                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = entit.Reason;
                            model.CurrentQuantity = 0;
                            model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);// entit.Difference;
                            model.Reason = entit.Reason;
                            model.VerifiyColor = "red";
                            model.Differencenumber = model.ActualQuantity;
                            model.VerifiylistId = entit.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entit.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "添加成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entit.SSCC;
                        return result;
                    }
                }
                else
                {
                    #region 这里处理下MES系统创建的库存

                    //这里拿slotID
                    var lotEntity = await _dalMaterialSubLotEntity.FindEntity(P => P.SubLotId == entit.SSCC.Trim());
                    if (lotEntity != null)
                    {                        //拿数据
                        var transferData = await _dalMaterialTransferEntity.FindList(P => P.Comment == "创建库存" && P.NewSublotId == lotEntity.ID);
                        if (transferData != null && transferData.Count == 1)
                        {
                            #region 新增

                            //这里判断当前是否存在于当前任务下
                            var data = await _dal.FindList(p => p.SublotId == lotEntity.ID && p.VerifiylistId == entit.ID);
                            if (data != null && data.Count > 0)
                            {
                                result.msg = "当前追溯码已存在,无需重复添加";
                                return result;
                            }
                            try
                            {
                                #region 新增数据

                                //查询物料
                                VerifiyDetailEntity model = new VerifiyDetailEntity();
                                model.CreateCustomGuid(_user.Name);
                                model.MaterialId = transferData[0].NewMaterialId;
                                model.SublotId = transferData[0].NewSublotId;
                                model.BatchId = transferData[0].NewLotId;
                                model.EquipmentId = transferData[0].NewEquipmentId;
                                model.Result = entit.Reason;
                                model.CurrentQuantity = 0;
                                model.ActualQuantity = string.IsNullOrEmpty(entit.Qty) ? 0 : Math.Round(Convert.ToDecimal(entit.Qty), 3);// inventData[0].Quantity.Value;
                                model.UnitId = transferData[0].QuantityUomId;
                                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entit.Difference) == null ? 0 : entit.Difference);
                                model.Reason = entit.Reason;
                                model.VerifiyColor = "red";
                                model.Differencenumber = model.ActualQuantity;
                                model.VerifiylistId = entit.ID;

                                bool vResult = false;
                                bool dResult = false;

                                _unitOfWork.BeginTran();

                                var verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entit.ID);
                                if (verifiy != null)
                                {
                                    VerifiyListEntity vModel = verifiy;
                                    vModel.Modify(entit.ID, _user.Name);
                                    vModel.TaskStatus = "盘点中";
                                    vResult = await _dalVerifiyListEntity.Update(vModel);
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败，未找到盘点计划";
                                    return result;
                                }
                                dResult = await _dal.Add(model) > 0;

                                if (dResult && vResult)
                                {
                                    _unitOfWork.CommitTran();
                                    result.success = true;
                                    result.msg = "添加成功";
                                    return result;
                                }
                                else
                                {
                                    _unitOfWork.RollbackTran();
                                    result.msg = "添加失败";
                                    return result;
                                }

                                #endregion
                            }
                            catch (Exception ex)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "添加失败" + ex;
                                return result;
                            }

                            #endregion
                        }
                    }
                    #endregion

                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "添加失败";
                return result;
            }



        }

        #endregion

        #region 喉头盘点

        #region 主界面查询

        public async Task<PageModel<VerifiyListEntity>> GetPageList_HT(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyListEntity> result = new PageModel<VerifiyListEntity>();

            if (reqModel.TypeS == "PDA")
            {
                DateTime endTime = DateTime.Now.AddDays(15);
                DateTime startTime = DateTime.Now.AddDays(-15);
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                      .And(a => a.CreateDate >= Convert.ToDateTime(startTime))
                 .And(a => a.CreateDate <= Convert.ToDateTime(endTime))
                            .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderBy(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "喉头").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;
            }
            else
            {
                var whereExpression = Expressionable.Create<VerifiyListEntity>()
                          .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                     .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                .ToExpression();
                var data = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>()
                              .Where(whereExpression).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Plandate).ThenByDescending(p => p.ModifyDate).Where(P => P.MaterialType == "喉头").Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;

            }
        }

        #endregion

        #region 操作

        public async Task<MessageModel<string>> Add_HT(VerifiyListRequestModel entity)
        {

            var result = new MessageModel<string>();
            result.success = false;

            if (string.IsNullOrEmpty(entity.Plandate))
            {
                result.msg = "请选择盘点时间";
                return result;
            }

            //判断当前是否有未结束的数据
            var d = await _dalVerifiyListEntity.FindList(p => p.TaskStatus != "已同步" && p.MaterialType == "喉头");
            if (d != null && d.Count > 0)
            {
                result.msg = "当前存在尚未结束";
                return result;
            }

            try
            {
                string lID = string.Empty;
                List<VerifiyListEntity> vList = new List<VerifiyListEntity>();
                List<VerifiyDetailEntity> dList = new List<VerifiyDetailEntity>();
                bool vResult = true;
                bool dResult = true;

                VerifiyListEntity model = new VerifiyListEntity();
                model.CreateCustomGuid(_user.Name);
                model.Plandate = Convert.ToDateTime(entity.Plandate);
                //model.TaskStatus = entity.TaskStatus;
                model.MaterialType = "喉头";
                model.TaskStatus = "已创建";
                lID = model.ID;
                vList.Add(model);

                #region 平移库存

                var inventData = await _dalInventorylistingViewEntity.FindList(p => (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.BatchId2)) && (p.LocationS.Contains("SUR3")));

                inventData = inventData.Where(P => P.Quantity != null).ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    for (int i = 0; i < inventData.Count; i++)
                    {
                        #region 新增数据

                        VerifiyDetailEntity dmodel = new VerifiyDetailEntity();

                        if (inventData[i].Quantity == null)
                        {

                            dmodel.CurrentQuantity = 0;
                            dmodel.ActualQuantity = 0;
                        }
                        else
                        {
                            dmodel.CurrentQuantity = inventData[i].Quantity.Value;
                            dmodel.ActualQuantity = inventData[i].Quantity.Value;
                        }

                        dmodel.Sapformula= inventData[i].Sapformula;
                        dmodel.CreateCustomGuid(_user.Name);
                        dmodel.MaterialId = inventData[i].MaterialId;
                        dmodel.SublotId = inventData[i].SlotId;
                        dmodel.BatchId = inventData[i].LotId;
                        dmodel.EquipmentId = inventData[i].EquipmentId;
                        dmodel.Result = " ";

                        dmodel.UnitId = inventData[i].UId;
                        dmodel.Difference = 0;
                        dmodel.Reason = " ";
                        dmodel.VerifiylistId = lID;

                        dList.Add(dmodel);

                        #endregion
                    }
                }

                #endregion

                _unitOfWork.BeginTran();

                if (vList.Count > 0)
                {
                    vResult = await _dalVerifiyListEntity.Add(vList) > 0;
                }

                if (dList.Count > 0)
                {
                    dResult = await _dal.Add(dList) > 0;
                }

                if (dResult && vResult)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "创建盘点任务成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建盘点任务失败";
                    return result;
                }


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "创建盘点任务失败" + ex.Message + ex.StackTrace;
                return result;

            }

        }

        public async Task<MessageModel<string>> Delete_HT(VerifiyListRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.IDS == null && entity.IDS.Length <= 0)
            {
                result.msg = "请选中删除的数据";
                return result;
            }

            var vList = await _dalVerifiyListEntity.Db.Queryable<VerifiyListEntity>().In(p => p.ID, entity.IDS).Where(p => p.TaskStatus == "已调整" || p.TaskStatus == "已同步").ToListAsync();

            if (vList != null && vList.Count > 0)
            {
                result.msg = "删除失败，存在已调整或已同步计划";
                return result;
            }

            bool v = true;
            bool vD = true;
            try
            {
                _unitOfWork.BeginTran();
                //查询明细数据
                var dData = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.VerifiylistId, entity.IDS).ToListAsync();
                string[] dIDS = dData.GroupBy(P => P.ID).Select(P => P.Key).ToArray();
                if (dIDS != null && dIDS.Length > 0)
                {
                    vD = await _dal.DeleteByIds(dIDS);
                }
                v = await _dalVerifiyListEntity.DeleteByIds(entity.IDS);
                if (v && vD)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "删除成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "删除失败";
                    return result;
                }

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "删除失败" + ex;
                return result;
            }
        }

        public async Task<List<VerifiyDetailViewEntity>> GetHT_DetailBySSCC(string sscc)
        {
            List<VerifiyDetailViewEntity> result = new List<VerifiyDetailViewEntity>();
            var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                       .AndIF(!string.IsNullOrEmpty(sscc), a => a.SubLotId == sscc).ToExpression();
            var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                          .Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

            return data;
        }


        public async Task<MessageModel<string>> AddDetail_HT(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (string.IsNullOrEmpty(entity.SSCC))
                {
                    result.msg = "追溯码为空";
                    return result;
                }
                if (string.IsNullOrEmpty(entity.ID))
                {
                    result.msg = "盘存ID为空";
                    return result;
                }

                //这里判断已经OK的数据无需添加盘存
                var listModel = await _dalVerifiyListEntity.FindEntity(entity.ID);
                if (listModel.TaskStatus == "已调整" || listModel.TaskStatus == "已同步")
                {
                    result.msg = string.Format(@"状态:{0}计划无法继续盘点", listModel.TaskStatus);
                    return result;
                }

                var inventData = await _dalInventorylistingViewEntity.FindList(p => p.Sscc == entity.SSCC && (string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrWhiteSpace(p.ProductionRequestId)));
                inventData = inventData.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && p.LocationS != "SUR3"
            && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4").ToList();
                if (inventData != null && inventData.Count > 0)
                {
                    if (inventData.Count == 1)
                    {
                        //这里判断当前是否存在于当前任务下
                        var data = await _dal.FindList(p => p.SublotId == inventData[0].SlotId && p.VerifiylistId == entity.ID);
                        if (data != null && data.Count > 0)
                        {
                            result.msg = "当前追溯码已存在,无需重复添加";
                            return result;
                        }

                        try
                        {
                            #region 新增数据

                            VerifiyDetailEntity model = new VerifiyDetailEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.MaterialId = inventData[0].MaterialId;
                            model.SublotId = inventData[0].SlotId;
                            model.BatchId = inventData[0].LotId;
                            model.EquipmentId = inventData[0].EquipmentId;
                            model.Result = " ";
                            model.CurrentQuantity = inventData[0].Quantity.Value;
                            model.ActualQuantity = inventData[0].Quantity.Value;
                            model.UnitId = inventData[0].UId;
                            model.Difference = 0;
                            model.Reason = " ";
                            model.VerifiylistId = entity.ID;

                            bool vResult = false;
                            bool dResult = false;

                            _unitOfWork.BeginTran();

                            VerifiyListEntity verifiy = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.ID);
                            if (verifiy != null)
                            {
                                VerifiyListEntity vModel = verifiy;
                                vModel.Modify(entity.ID, _user.Name);
                                vModel.TaskStatus = "盘点中";
                                vResult = await _dalVerifiyListEntity.Update(vModel);
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "更新失败，未找到盘点计划";
                                return result;
                            }
                            dResult = await _dal.Add(model) > 0;

                            if (dResult && vResult)
                            {
                                _unitOfWork.CommitTran();
                                result.success = true;
                                result.msg = "更新成功";
                                return result;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "更新失败";
                                return result;
                            }

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "添加失败" + ex;
                            return result;
                        }

                    }
                    else

                    {
                        result.msg = "添加失败,追溯码存在重复数据" + entity.SSCC;
                        return result;
                    }
                }
                else
                {
                    result.msg = "添加失败,请确认该追溯码是否存在";
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "添加失败";
                return result;
            }



        }

        public async Task<MessageModel<string>> ModifyDetail_HT(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }

            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {

                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }
                if (entity.types == "新增")
                {
                    model.Result = "新增";
                }

                #region 这里处理逻辑（新增、差异）

                List<MaterialInventoryEntity> InventListup = new List<MaterialInventoryEntity>();
                List<MaterialInventoryEntity> insetList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                //if (model.Result == "新增")
                //{
                //    //查询当前数据是否存在
                //    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.Sscc == entity.SSCC);
                //    if (inventoryModel == null)
                //    {
                //        #region 创建库存信息

                //        MaterialInventoryEntity request = new MaterialInventoryEntity();
                //        request.Quantity = Math.Round(Convert.ToDecimal(entity.Qty), 3);
                //        request.Create(_user.Name.ToString());
                //        request.QuantityUomId = inventoryModel.UId;
                //        request.EquipmentId = inventoryModel.EquipmentId;
                //        request.SublotId = inventoryModel.SlotId;
                //        request.LotId = inventoryModel.LotId;

                //        #endregion

                //        #region 写入转移历史

                //        var lotEntity = await _dalMaterialLotEntity.FindList(p => p.ID == Verifiy.BatchId);
                //        if (lotEntity == null || lotEntity.Count <= 0)
                //        {

                //        }
                //        else
                //        {
                //            insetList.Add(request);
                //            //写入历史记录
                //            MaterialTransferEntity trans = new MaterialTransferEntity();
                //            trans.Create(_user.Name.ToString());
                //            //trans.ID = Guid.NewGuid().ToString();
                //            //  trans.OldStorageLocation = storage_location;
                //            // trans.NewStorageLocation = storage_location;
                //            trans.OldLotId = "";
                //            trans.OldSublotId = "";
                //            trans.OldExpirationDate = null;
                //            trans.NewExpirationDate = lotEntity[0].ExpirationDate;
                //            trans.Quantity = request.Quantity; // Convert.ToDecimal(Quantity);
                //            trans.QuantityUomId = request.QuantityUomId;
                //            //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //            trans.Type = "Create Inventory";
                //            trans.Comment = "库存盘存-创建";
                //            //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //            //trans.TransferGroupId
                //            trans.OldEquipmentId = "";
                //            trans.NewEquipmentId = request.EquipmentId;
                //            trans.OldContainerId = "";
                //            trans.NewContainerId = "";
                //            trans.OldLotExternalStatus = "";
                //            trans.OldSublotExternalStatus = "";
                //            trans.NewMaterialId = lotEntity[0].MaterialId;
                //            trans.OldMaterialId = lotEntity[0].MaterialId;
                //            trans.NewLotExternalStatus = "2";
                //            trans.NewSublotId = Verifiy.SublotId;
                //            trans.NewLotId = Verifiy.BatchId;
                //            trans.NewSublotExternalStatus = "3";
                //            transferList.Add(trans);
                //        }


                //        #endregion
                //    }
                //    else
                //    {
                //        #region 更新

                //        //更新数量
                //        MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                //        upInvent.Modify(upInvent.ID, _user.Name);
                //        upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                //        InventListup.Add(upInvent);

                //        #region 写入转移历史    

                //        //写入历史记录
                //        MaterialTransferEntity trans = new MaterialTransferEntity();
                //        trans.Create(_user.Name.ToString());
                //        //trans.ID = Guid.NewGuid().ToString();
                //        trans.OldStorageLocation = inventoryModel.LocationF;
                //        trans.NewStorageLocation = inventoryModel.LocationF;
                //        trans.OldLotId = inventoryModel.LotId;
                //        trans.NewLotId = inventoryModel.LotId;
                //        trans.OldSublotId = inventoryModel.SlotId;
                //        trans.NewSublotId = inventoryModel.SlotId;
                //        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                //        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                //        trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                //        trans.QuantityUomId = inventoryModel.QuantityUomId;
                //        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                //        trans.Type = "Inventory Update Quantity";
                //        trans.Comment = "库存-盘存新增更新数量";
                //        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //        //trans.TransferGroupId
                //        trans.OldEquipmentId = inventoryModel.EquipmentId;
                //        trans.NewEquipmentId = inventoryModel.EquipmentId;
                //        trans.OldContainerId = inventoryModel.ContainerId;
                //        trans.NewContainerId = inventoryModel.ContainerId;                    //status
                //        trans.OldMaterialId = inventoryModel.MaterialId;
                //        trans.NewMaterialId = inventoryModel.MaterialId;
                //        trans.OldLotExternalStatus = inventoryModel.StatusF;
                //        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                //        trans.NewLotExternalStatus = inventoryModel.StatusF;
                //        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                //        trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                //        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                //        transferList.Add(trans);
                //        #endregion

                //        #endregion
                //    }
                //}
                //else 

                if (model.Result == "差异")
                {
                    //查询当前数据是否存在
                    var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.SlotId == Verifiy.SublotId);
                    if (inventoryModel == null)
                    {
                        result.msg = "请确认库存信息是否存在";
                        return result;
                    }
                    #region 更新

                    //更新数量
                    MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.SublotId == Verifiy.SublotId);
                    upInvent.Modify(upInvent.ID, _user.Name);
                    upInvent.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3); // detailDataByIDList[j].ActualQuantity;//更新数量
                    InventListup.Add(upInvent);


                    #region 记录差值

                    decimal newQty = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    decimal oldQty = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);

                    string stringQty = string.Empty;
                    decimal cha = 0;

                    if (newQty > oldQty)
                    {
                        cha = newQty - oldQty;
                        stringQty = oldQty + "+" + cha;
                    }
                    else if (newQty < oldQty)
                    {
                        cha = oldQty - newQty;
                        stringQty = oldQty + "-" + cha;
                    }

                    if (cha == 0)
                    {
                        stringQty = string.Empty;
                    }

                    #endregion

                    #region 写入转移历史    

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy.ActualQuantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Inventory Update Quantity";
                    trans.Comment = "库存-盘存新增更新数量" + stringQty;
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                    transferList.Add(trans);
                    #endregion

                    #endregion
                }

                #endregion


                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }



                bool r = await _dal.Update(model);

                #region 这里执行新增和更新库存信息

                bool r2 = true;
                bool r3 = true;
                bool r4 = true;

                if (InventListup.Count > 0)
                {
                    r2 = await _dalMaterialInventoryEntity.Update(InventListup);
                }
                if (transferList.Count > 0)
                {
                    r3 = await _dalMaterialTransferEntity.Add(transferList) > 0;
                }
                if (insetList.Count > 0)
                {
                    r4 = await _dalMaterialInventoryEntity.Add(insetList) > 0;
                }
                #endregion

                if (r == true && v && r2 && r3 && r4)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }

        }

        public async Task<MessageModel<string>> ModifyDetail_HTOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (entity.ActualQuantity < 0)
            {
                result.msg = "盘点实数为零请重新填入";
                return result;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                result.msg = "盘存计划ID为空";
                return result;
            }

            var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
            if (vModels == null)
            {
                result.msg = "盘存计划为空";
                return result;
            }

            var Verifiy = await _dal.FindEntity(p => p.ID == entity.ID);
            if (Verifiy != null)
            {

                #region 新增数据
                VerifiyDetailEntity model = Verifiy;// new VerifiyDetailEntity();
                model.Modify(model.ID, _user.Name);
                model.ActualQuantity = entity.ActualQuantity; //更改数据

                if (entity.ActualQuantity != Verifiy.CurrentQuantity)
                {
                    model.Result = "差异";
                }
                else
                {
                    model.Result = "通过";
                }

                if (entity.types == "缺失")
                {
                    model.Result = "缺失";
                }
                if (entity.types == "新增")
                {
                    model.Result = "新增";
                }


                model.Differencenumber = entity.ActualQuantity - Verifiy.CurrentQuantity;
                model.Difference = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference);  //entity.Difference;
                model.Reason = entity.Reason;
                //判断差异决定颜色
                decimal value = Convert.ToDecimal(string.IsNullOrEmpty(entity.Difference) == null ? 0 : entity.Difference); //entity.Difference;// ((entity.ActualQuantity - Verifiy.CurrentQuantity) / Verifiy.CurrentQuantity) * 100;

                if (Verifiy.CurrentQuantity != 0)
                {
                    if (value > Convert.ToDecimal(5) || value < Convert.ToDecimal(-5))
                    {
                        model.VerifiyColor = "red";
                    }
                    else
                    {
                        model.VerifiyColor = "green";
                    }
                }
                else
                {
                    model.VerifiyColor = "red";
                }


                _unitOfWork.BeginTran();

                bool v = true;

                if (vModels.TaskStatus == "已创建")
                {
                    vModels.Modify(entity.VID, _user.Name);
                    vModels.TaskStatus = "盘点中";
                    v = await _dalVerifiyListEntity.Update(vModels);
                }



                bool r = await _dal.Update(model);
                if (r == true && v)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "确认成功";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "确认失败";
                    return result;
                }

                #endregion

            }
            else
            {
                result.msg = "确认失败,请确认该追溯码是否存在";
                return result;
            }

        }

        #endregion

        #endregion


        #endregion


        #region 缺失

        public async Task<MessageModel<string>> ModifyDetailQS_YL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }

                if (vModels.TaskStatus == "已同步" || vModels.TaskStatus == "已调整")
                {

                    result.msg = "当前任务已经完成调整";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    List<string> listIDS = new List<string>();
                    List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                    //查询库存信息
                    string[] sLotID = Verifiy.GroupBy(P => P.SublotId).Select(g => g.Key).ToArray();
                    var inventData = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(p => p.SlotId, sLotID).ToListAsync();

                    for (int i = 0; i < Verifiy.Count; i++)
                    {


                        #region 删除

                        //这里拿数据
                        string sbID = Verifiy[i].SublotId;
                        var inventoryModel = inventData.Where(p => p.SlotId == sbID).FirstOrDefault();

                        if (!string.IsNullOrEmpty(inventoryModel.ID))
                        {
                            listIDS.Add(inventoryModel.ID);

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy[i].ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Delete";
                            trans.Comment = "盘存-删除库存";
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);

                            #endregion
                        }



                        #endregion

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();

                        bool v = true;
                        bool delete = true;
                        bool transfer = true;

                        #region 删除

                        if (transferList.Count > 0)
                        {
                            transfer = await _dalMaterialTransferEntity.Add(transferList) > 0;
                        }
                        if (listIDS.Count > 0)
                        {
                            string[] dIDS = listIDS.ToArray();
                            delete = await _dalMaterialInventoryEntity.DeleteByIds(dIDS);
                        }

                        #endregion


                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v && delete && transfer)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }

        public async Task<MessageModel<string>> ModifyDetailQS_WL(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }
                if (vModels.TaskStatus == "已同步" || vModels.TaskStatus == "已调整")
                {

                    result.msg = "当前任务已经完成调整";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    List<string> listIDS = new List<string>();
                    List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                    //查询库存信息
                    string[] sLotID = Verifiy.GroupBy(P => P.SublotId).Select(g => g.Key).ToArray();
                    var inventData = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(p => p.SlotId, sLotID).ToListAsync();


                    for (int i = 0; i < Verifiy.Count; i++)
                    {
                        #region 删除

                        //这里拿数据
                        string sbID = Verifiy[i].SublotId;
                        var inventoryModel = inventData.Where(p => p.SlotId == sbID).FirstOrDefault();

                        if (!string.IsNullOrEmpty(inventoryModel.ID))
                        {
                            listIDS.Add(inventoryModel.ID);

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy[i].ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Delete";
                            trans.Comment = "盘存-删除库存";
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);

                            #endregion
                        }



                        #endregion

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();
                        bool v = true;
                        bool delete = true;
                        bool transfer = true;
                        #region 删除

                        if (transferList.Count > 0)
                        {
                            transfer = await _dalMaterialTransferEntity.Add(transferList) > 0;
                        }
                        if (listIDS.Count > 0)
                        {
                            string[] dIDS = listIDS.ToArray();
                            delete = await _dalMaterialInventoryEntity.DeleteByIds(dIDS);
                        }

                        #endregion
                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }

        public async Task<MessageModel<string>> ModifyDetailQS_HT(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }
                if (vModels.TaskStatus == "已同步" || vModels.TaskStatus == "已调整")
                {

                    result.msg = "当前任务已经完成调整";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    List<string> listIDS = new List<string>();
                    List<MaterialTransferEntity> transferList = new List<MaterialTransferEntity>();
                    //查询库存信息
                    string[] sLotID = Verifiy.GroupBy(P => P.SublotId).Select(g => g.Key).ToArray();
                    var inventData = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(p => p.SlotId, sLotID).ToListAsync();

                    for (int i = 0; i < Verifiy.Count; i++)
                    {
                        #region 删除

                        //这里拿数据
                        string sbID = Verifiy[i].SublotId;
                        var inventoryModel = inventData.Where(p => p.SlotId == sbID).FirstOrDefault();

                        if (!string.IsNullOrEmpty(inventoryModel.ID))
                        {
                            listIDS.Add(inventoryModel.ID);

                            #region 写入转移历史    

                            //写入历史记录
                            MaterialTransferEntity trans = new MaterialTransferEntity();
                            trans.Create(_user.Name.ToString());
                            //trans.ID = Guid.NewGuid().ToString();
                            trans.OldStorageLocation = inventoryModel.LocationF;
                            trans.NewStorageLocation = inventoryModel.LocationF;
                            trans.OldLotId = inventoryModel.LotId;
                            trans.NewLotId = inventoryModel.LotId;
                            trans.OldSublotId = inventoryModel.SlotId;
                            trans.NewSublotId = inventoryModel.SlotId;
                            trans.OldExpirationDate = inventoryModel.ExpirationDate;
                            trans.NewExpirationDate = inventoryModel.ExpirationDate;
                            trans.Quantity = Math.Round(Convert.ToDecimal(Verifiy[i].ActualQuantity), 3);
                            trans.QuantityUomId = inventoryModel.QuantityUomId;
                            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans.Type = "Inventory Delete";
                            trans.Comment = "盘存-删除库存";
                            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            trans.OldEquipmentId = inventoryModel.EquipmentId;
                            trans.NewEquipmentId = inventoryModel.EquipmentId;
                            trans.OldContainerId = inventoryModel.ContainerId;
                            trans.NewContainerId = inventoryModel.ContainerId;                    //status
                            trans.OldMaterialId = inventoryModel.MaterialId;
                            trans.NewMaterialId = inventoryModel.MaterialId;
                            trans.OldLotExternalStatus = inventoryModel.StatusF;
                            trans.OldSublotExternalStatus = inventoryModel.StatusS;
                            trans.NewLotExternalStatus = inventoryModel.StatusF;
                            trans.NewSublotExternalStatus = inventoryModel.StatusS;
                            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                            transferList.Add(trans);

                            #endregion
                        }



                        #endregion

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();
                        bool v = true;
                        bool delete = true;
                        bool transfer = true;

                        #region 删除

                        if (transferList.Count > 0)
                        {
                            transfer = await _dalMaterialTransferEntity.Add(transferList) > 0;
                        }
                        if (listIDS.Count > 0)
                        {
                            string[] dIDS = listIDS.ToArray();
                            delete = await _dalMaterialInventoryEntity.DeleteByIds(dIDS);
                        }

                        #endregion
                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }


        public async Task<MessageModel<string>> ModifyDetailQS_YLOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    for (int i = 0; i < Verifiy.Count; i++)
                    {

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();
                        bool v = true;

                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }

        public async Task<MessageModel<string>> ModifyDetailQS_WLOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    for (int i = 0; i < Verifiy.Count; i++)
                    {

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();
                        bool v = true;

                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }

        public async Task<MessageModel<string>> ModifyDetailQS_HTOLD(VerifiyDetailRequestModel entity)
        {
            var result = new MessageModel<string>();
            result.success = false;


            try
            {
                if (entity.IDS == null || entity.IDS.Length <= 0)
                {
                    result.msg = "请选中缺失的料";
                    return result;
                }

                if (entity.ActualQuantity < 0)
                {
                    result.msg = "盘点实数为零请重新填入";
                    return result;
                }


                var vModels = await _dalVerifiyListEntity.FindEntity(p => p.ID == entity.VID);
                if (vModels == null)
                {
                    result.msg = "盘存计划为空";
                    return result;
                }

                var Verifiy = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, entity.IDS).ToListAsync();
                if (Verifiy != null && Verifiy.Count > 0)
                {
                    List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                    for (int i = 0; i < Verifiy.Count; i++)
                    {

                        VerifiyDetailEntity model = Verifiy[i];// new VerifiyDetailEntity();
                        model.Modify(model.ID, _user.Name);
                        model.Result = "缺失";
                        model.Differencenumber = 0;
                        model.Difference = 0;
                        model.Reason = "缺失";
                        model.VerifiyColor = "green";
                        upList.Add(model);

                    }
                    #region 新增数据          


                    if (upList.Count > 0)
                    {
                        _unitOfWork.BeginTran();
                        bool v = true;

                        if (vModels.TaskStatus == "已创建")
                        {
                            vModels.Modify(entity.VID, _user.Name);
                            vModels.TaskStatus = "盘点中";
                            v = await _dalVerifiyListEntity.Update(vModels);
                        }

                        bool r = await _dal.Update(upList);
                        if (r == true && v)
                        {
                            _unitOfWork.CommitTran();
                            result.success = true;
                            result.msg = "一键缺失成功";
                            return result;
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "一键缺失失败";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "不存在操作数据";
                        return result;
                    }

                    #endregion

                }
                else
                {
                    result.msg = "一键缺失失败不存在明显信息";
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.msg = "一键缺失失败失败原因:" + ex.Message;
                return result;
            }
        }

        #endregion

        #region  退库物料盘点(网页和PDA)

        /// <summary>
        /// 退库盘点/大盘点
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<bool> CreateDataTK(string type)
        {
            //try
            //{
            //    //查询是否存在未结束的盘点任务
            //    var result = await _dalVerifiyListEntity.FindList(P => P.MaterialType == "物料" && P.Tasktype == type && (P.TaskStatus == "已创建" || P.TaskStatus == "盘点中" || P.TaskStatus == "已调整"));
            //    if (result == null || result.Count <= 0)
            //    {
            //        VerifiyListEntity model = new VerifiyListEntity();
            //        model.CreateCustomGuid(_user.Name);
            //        model.Plandate = Convert.ToDateTime(DateTime.Now);
            //        //model.TaskStatus = entity.TaskStatus;
            //        model.MaterialType = "物料";
            //        model.TaskStatus = "已创建";
            //        model.Tasktype = "退库盘点";
            //        bool dResult = await _dalVerifiyListEntity.Add(model) > 0;
            //        if (dResult == false) 
            //        {
            //            return false;
            //        }
            //    }

            //    return true;
            //}
            //catch (Exception)
            //{
            //    return false;
            //}

            return true;

        }


        //第二界面查询(退仓)
        public async Task<PageModel<VerifiyDetailViewEntity>> GetReturn_DetailByID(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyDetailViewEntity> result = new PageModel<VerifiyDetailViewEntity>();

            try
            {
                if (string.IsNullOrEmpty(reqModel.id))
                {
                    List<VerifiyDetailViewEntity> Vlist = new List<VerifiyDetailViewEntity>();
                    int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                    var rDat1 = Vlist;
                    result.dataCount = 1;
                    result.data = rDat1;
                    return result;
                }


                var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                   .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                   .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key) || a.Code.Contains(reqModel.key) || a.EquipmentName.Contains(reqModel.key))
                   .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                   .AndIF(!string.IsNullOrEmpty(reqModel.Inventtype), a => a.Inventtype.Contains(reqModel.Inventtype))
                   .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
                var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                              .Where(whereExpression).ToListAsync();

                //如果为侧边弹框,判断是否需要添加库存车
                if (!string.IsNullOrEmpty(reqModel.sscc))
                {
                    //如果不存在数据
                    if (data == null || data.Count <= 0)
                    {
                        //转移目的地
                        var inventoryModel = await _dalInventorylistingViewEntity.FindEntity(p => p.Sscc == reqModel.sscc && string.IsNullOrEmpty(p.ProductionRequestId) && string.IsNullOrEmpty(p.BatchId2));

                        if (inventoryModel == null)
                        {
                            result.data = new List<VerifiyDetailViewEntity>();
                            result.dataCount = -1;//该追溯码库存信息不存在
                            return result;
                        }
                        else
                        {
                            string equpmentID = inventoryModel.EquipmentId;
                            if (string.IsNullOrEmpty(equpmentID))
                            {
                                result.data = new List<VerifiyDetailViewEntity>();
                                result.dataCount = -2;//该追溯码不存在位置                        
                                return result;
                            }

                            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
                            List<VerifiyDetailEntity> addDetailList = new List<VerifiyDetailEntity>();
                            List<VerifiyListEntity> upVerifiyList = new List<VerifiyListEntity>();

                            List<MaterialInventoryEntity> upinventList = new List<MaterialInventoryEntity>();

                            var equpmentEntity = await _dalEquipmentEntity.FindEntity(P => P.ID == equpmentID);
                            if (equpmentEntity.EquipmentCode != "PSA_PKG3")
                            {
                                var PSA_PKG3Model = await _dalEquipmentEntity.FindEntity(P => P.EquipmentCode == "PSA_PKG3");
                                if (PSA_PKG3Model != null)
                                {
                                    equpmentID = PSA_PKG3Model.ID;
                                }
                                //写转移历史
                                #region 写入转移历史

                                //写入历史记录
                                MaterialTransferEntity trans = new MaterialTransferEntity();
                                trans.Create(_user.Name);
                                //trans.ID = Guid.NewGuid().ToString();
                                trans.OldStorageLocation = inventoryModel.LocationF;
                                //trans.NewStorageLocation = name;
                                trans.OldLotId = inventoryModel.LotId;
                                trans.NewLotId = inventoryModel.LotId;
                                trans.OldSublotId = inventoryModel.SlotId;
                                trans.NewSublotId = inventoryModel.SlotId;
                                trans.OldExpirationDate = inventoryModel.ExpirationDate;
                                trans.NewExpirationDate = inventoryModel.ExpirationDate;
                                trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                                trans.QuantityUomId = inventoryModel.QuantityUomId;
                                trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                                trans.Type = "Transfer Inventory";
                                trans.Comment = "退库盘点-转移";
                                trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                                //trans.TransferGroupId
                                trans.OldEquipmentId = inventoryModel.EquipmentId;
                                trans.NewEquipmentId = equpmentID;
                                trans.OldContainerId = inventoryModel.ContainerId;
                                trans.NewContainerId = inventoryModel.ContainerId;                            //status
                                trans.OldMaterialId = inventoryModel.MaterialId;
                                trans.NewMaterialId = inventoryModel.MaterialId;
                                trans.OldLotExternalStatus = inventoryModel.StatusF;
                                trans.OldSublotExternalStatus = inventoryModel.StatusS;
                                trans.NewLotExternalStatus = inventoryModel.StatusF;
                                trans.NewSublotExternalStatus = inventoryModel.StatusS;
                                trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                                trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                                addTransfer.Add(trans);

                                #endregion


                                #region 更新库存位置(加入数据)

                                //进行仓库转移(更新位置)
                                MaterialInventoryEntity upInvent = await _dalMaterialInventoryEntity.FindEntity(p => p.ID == inventoryModel.ID);
                                upInvent.Modify(upInvent.ID, _user.Name);
                                upInvent.EquipmentId = equpmentID;
                                upinventList.Add(upInvent);

                                #endregion
                            }
                            else
                            {


                            }

                            //这里直接创建明细
                            if (data == null || data.Count <= 0)
                            {
                                //首先判断当前计划状态，如果已调整和已经同步弹出提示信息
                                var verifiyListModel = await _dalVerifiyListEntity.FindEntity(p => p.ID == reqModel.id);
                                if (verifiyListModel.TaskStatus == "已调整" || verifiyListModel.TaskStatus == "已同步")
                                {
                                    result.data = new List<VerifiyDetailViewEntity>();
                                    result.dataCount = -3;//该批次已完成调整，无法进行退库盘点
                                    return result;
                                }
                                else
                                {

                                    bool v = true;
                                    bool v1 = true;
                                    bool v2 = true;
                                    bool v3 = true;

                                    if (verifiyListModel.TaskStatus == "已创建")
                                    {
                                        verifiyListModel.Modify(verifiyListModel.ID, _user.Name);
                                        verifiyListModel.TaskStatus = "盘点中";
                                        upVerifiyList.Add(verifiyListModel);
                                    }

                                    //创建明细
                                    VerifiyDetailEntity dmodel = new VerifiyDetailEntity();
                                    if (inventoryModel.Quantity == null)
                                    {
                                        dmodel.CurrentQuantity = 0;
                                        dmodel.ActualQuantity = 0;
                                    }
                                    else
                                    {
                                        dmodel.CurrentQuantity = inventoryModel.Quantity.Value;
                                        dmodel.ActualQuantity = inventoryModel.Quantity.Value;
                                    }
                                    dmodel.CreateCustomGuid(_user.Name);
                                    dmodel.MaterialId = inventoryModel.MaterialId;
                                    dmodel.SublotId = inventoryModel.SlotId;
                                    dmodel.BatchId = inventoryModel.LotId;
                                    dmodel.EquipmentId = equpmentID;
                                    dmodel.Result = " ";
                                    dmodel.UnitId = inventoryModel.UId;
                                    dmodel.Difference = 0;
                                    dmodel.Reason = " ";
                                    dmodel.Inventtype = "未处理";
                                    dmodel.VerifiylistId = reqModel.id;
                                    addDetailList.Add(dmodel);

                                    _unitOfWork.BeginTran();

                                    if (addDetailList.Count > 0)
                                    {
                                        v = await _dal.Add(addDetailList) > 0;
                                    }
                                    if (upVerifiyList.Count > 0)
                                    {
                                        v1 = await _dalVerifiyListEntity.Update(upVerifiyList);
                                    }
                                    if (addTransfer.Count > 0)
                                    {
                                        v2 = await _dalMaterialTransferEntity.Add(addTransfer) > 0;
                                    }
                                    if (upinventList.Count > 0)
                                    {
                                        v3 = await _dalMaterialInventoryEntity.Update(upinventList);
                                    }

                                    if (v && v1 && v2 && v3)
                                    {
                                        _unitOfWork.CommitTran();

                                        #region 这里查询数据

                                        var whereExpression1 = Expressionable.Create<VerifiyDetailViewEntity>()
                                       .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                                       .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key) || a.Code.Contains(reqModel.key) || a.EquipmentName.Contains(reqModel.key))
                                       .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                                            .AndIF(!string.IsNullOrEmpty(reqModel.Inventtype), a => a.Inventtype.Contains(reqModel.Inventtype))
                                       .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
                                        var data1 = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                                                      .Where(whereExpression1).ToListAsync();

                                        int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                        var rDat1 = data1.OrderBy(p => p.Result).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
                                        result.dataCount = data1.Count;
                                        result.data = rDat1;

                                        return result;

                                        #endregion

                                    }
                                    else
                                    {
                                        _unitOfWork.RollbackTran();
                                        result.data = new List<VerifiyDetailViewEntity>();
                                        result.dataCount = -4;//新增退货失败
                                        return result;
                                    }
                                }
                            }
                        }
                    }
                }


                #region 复点优先

                #endregion          

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data.OrderByDescending(p => p.Ordernumber).ThenByDescending(p => p.ModifyDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data.Count;
                result.data = rDat;
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.data = new List<VerifiyDetailViewEntity>();
                result.dataCount = -5;//新增退货失败
                return result;
            }



        }



        //退库汇总
        public async Task<PageModel<VerifiyDetailTotalDatas>> GetReturn_DetailTotal(VerifiyListRequestModel reqModel)
        {
            PageModel<VerifiyDetailTotalDatas> result = new PageModel<VerifiyDetailTotalDatas>();

            try
            {
                if (string.IsNullOrEmpty(reqModel.id))
                {
                    List<VerifiyDetailTotalDatas> Vlist = new List<VerifiyDetailTotalDatas>();
                    int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                    var rDat1 = Vlist;
                    result.dataCount = 1;
                    result.data = rDat1;
                    return result;
                }

                var whereExpression = Expressionable.Create<VerifiyDetailViewEntity>()
                   .AndIF(!string.IsNullOrEmpty(reqModel.id), a => a.VerifiylistId == reqModel.id)
                   .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Mcode.Contains(reqModel.key) || a.SubLotId.Contains(reqModel.key) || a.Code.Contains(reqModel.key) || a.EquipmentName.Contains(reqModel.key))
                   .AndIF(!string.IsNullOrEmpty(reqModel.selectkey), a => a.Result.Contains(reqModel.selectkey))
                   .AndIF(!string.IsNullOrEmpty(reqModel.Inventtype), a => a.Inventtype.Contains(reqModel.Inventtype))
                   .AndIF(!string.IsNullOrEmpty(reqModel.sscc), a => a.SubLotId == reqModel.sscc).ToExpression();
                var data = await _dalView.Db.Queryable<VerifiyDetailViewEntity>()
                              .Where(whereExpression).ToListAsync();

                List<VerifiyDetailTotalDatas> rList = (from a in data
                                                       group a by new
                                                       {
                                                           a.Mcode,
                                                           a.LotId
                                                       } into g
                                                       select new VerifiyDetailTotalDatas
                                                       {
                                                           MCode = g.Key.Mcode,
                                                           Batch_Code = g.Key.LotId,
                                                           InventQty = g.Sum(p => p.CurrentQuantity),
                                                           Move_ActureQty = g.Sum(p => p.ActualQuantity),
                                                           DifferenceQty = g.Sum(p => p.Differencenumber.Value)
                                                       }).ToList();

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = rList.OrderBy(p => p.Batch_Code).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = rList.Count;
                result.data = rDat;
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.data = new List<VerifiyDetailTotalDatas>();
                result.dataCount = -5;//新增退货失败
                return result;
            }



        }

        /// <summary>
        /// 退库改变状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="stateType"></param>
        /// <returns></returns>

        public async Task<MessageModel<string>> TK_ChangeState(string[] id, string stateType)
        {

            var result = new MessageModel<string>();
            result.success = false;

            try
            {


                if (id == null || id.Count() <= 0)
                {
                    result.msg = "请选中数据";
                    return result;
                }

                //拿数据
                List<VerifiyDetailEntity> listModel = await _dal.Db.Queryable<VerifiyDetailEntity>().In(P => P.ID, id).ToListAsync();


                if (result != null)
                {
                    List<VerifiyDetailEntity> upData = new List<VerifiyDetailEntity>();
                    VerifiyDetailEntity model = new VerifiyDetailEntity();


                    for (int i = 0; i < listModel.Count; i++)
                    {
                        model = listModel[i];

                        if (string.IsNullOrEmpty(model.Result) || model.Result == " ")
                        {
                            result.msg = "请确认当前数据是进行了通过确认";
                            return result;
                        }
                        if (model.Result == "复检")
                        {
                            result.msg = "请先完成复检查";
                            return result;
                        }

                        model.Modify(model.ID, _user.Name);
                        model.Inventtype = stateType;
                        upData.Add(model);
                    }



                    if (upData != null && upData.Count > 0)
                    {
                        bool dResult = await _dal.Update(model);

                        if (dResult == false)
                        {
                            result.msg = "调整失败";
                            return result;
                        }
                        else
                        {
                            result.success = true;
                            result.msg = "调整成功";
                            return result;
                        }
                    }
                    else
                    {

                        result.msg = "调整失败";
                        return result;
                    }

                }
                result.msg = "调整失败";
                return result;
            }
            catch (Exception ex)
            {
                result.msg = "调整失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 复检
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ReInspection(string[] id)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (id == null || id.Length <= 0)
                {
                    result.msg = "请选中需要复点数据";
                    return result;
                }
                //拿数据
                List<VerifiyDetailEntity> vList = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, id).ToListAsync();
                if (vList == null || vList.Count <= 0)
                {
                    result.msg = "不存在复点数据";
                    return result;
                }
                int count = vList.Where(p => p.Inventtype == "已退仓").Count();
                if (count > 0)
                {
                    result.msg = "存在已退仓数据，无法进行复点";
                    return result;
                }

                List<VerifiyDetailEntity> upList = new List<VerifiyDetailEntity>();

                for (int i = 0; i < vList.Count; i++)
                {
                    VerifiyDetailEntity model = vList[i];
                    model.Modify(model.ID, _user.Name);
                    model.Result = "复点";
                    upList.Add(model);
                }

                bool dResult = await _dal.Update(upList);
                if (dResult == false)
                {
                    result.msg = "操作失败";
                    return result;
                }
                result.success = true;
                result.msg = "操作成功";
                return result;
            }
            catch (Exception ex)
            {
                result.msg = "操作失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 是否可以退库
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> IsReturn(string[] id)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (id == null || id.Length <= 0)
                {
                    result.msg = "请选中退仓数据";
                    return result;
                }
                //拿数据
                List<VerifiyDetailEntity> vList = await _dal.Db.Queryable<VerifiyDetailEntity>().In(p => p.ID, id).ToListAsync();
                if (vList == null || vList.Count <= 0)
                {
                    result.msg = "不存在退仓数据";
                    return result;
                }

                int count = vList.Where(p => p.Inventtype == "已退仓").Count();
                if (count > 0)
                {
                    result.msg = "存在已退仓数据，无法进行退仓";
                    return result;
                }

                result.success = true;
                result.msg = "操作成功";
                return result;
            }
            catch (Exception ex)
            {
                result.msg = "操作失败" + ex.Message;
                return result;
            }
        }


        public async Task<MessageModel<string>> ChangeRead(string id, string state)
        {

            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //拿数据
                VerifiyDetailEntity model = await _dal.FindEntity(P => P.ID == id);
                if (result != null)
                {
                    model.Modify(model.ID, _user.Name);
                    model.Isread = state;
                    bool dResult = await _dal.Update(model);

                    if (dResult == false)
                    {
                        result.msg = "失败";
                        return result;
                    }
                    result.success = true;
                    result.msg = "成功";
                    return result;
                }
                result.msg = "失败";
                return result;
            }
            catch (Exception ex)
            {
                result.msg = "失败" + ex.Message;
                return result;
            }
        }


        public async Task<MessageModel<string>> ChangeRemark(string id, string remark)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //拿数据
                VerifiyDetailEntity model = await _dal.FindEntity(P => P.ID == id);
                if (result != null)
                {
                    _unitOfWork.BeginTran();

                    model.Modify(model.ID, _user.Name);
                    model.Remark = remark;
                    bool dResult = await _dal.Update(model);
                    bool inventResult = true;

                    //这里要调整库存的备注
                    MaterialSubLotEntity invent = await _dalMaterialSubLotEntity.FindEntity(p => p.ID == model.SublotId);
                    if (invent != null)
                    {
                        invent.Modify(invent.ID, _user.Name);
                        invent.Comment = remark;
                        inventResult = await _dalMaterialSubLotEntity.Update(invent);
                    }

                    if (dResult == false || inventResult == false)
                    {
                        _unitOfWork.RollbackTran();

                        result.msg = "备注失败";
                        return result;
                    }

                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "备注成功";
                    return result;
                }

                result.msg = "备注失败";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "调整失败" + ex.Message;
                return result;
            }
        }


        #endregion




        public async Task<List<VerifiyDetailEntity>> GetList(VerifiyDetailRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<VerifiyDetailEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<VerifiyDetailEntity>> GetPageList(VerifiyDetailRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<VerifiyDetailEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(VerifiyDetailEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}