using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialSubLotController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialSubLotServices _materialSubLotServices;

        public MaterialSubLotController(IMaterialSubLotServices MaterialSubLotServices)
        {
            _materialSubLotServices = MaterialSubLotServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialSubLotEntity>>> GetList(string key = "")
        {
            Expression<Func<MaterialSubLotEntity, bool>> whereExpression = a => true;
            var data = await _materialSubLotServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialSubLotEntity>>> GetPageList([FromBody] MaterialSubLotRequestModel reqModel)
        {

            Expression<Func<MaterialSubLotEntity, bool>> whereExpression = a => true;
            var data = await _materialSubLotServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialSubLotEntity>> GetEntity(string id)
        {
            var data = await _materialSubLotServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialSubLotEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialSubLotServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialSubLotServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialSubLotServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class MaterialSubLotRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}