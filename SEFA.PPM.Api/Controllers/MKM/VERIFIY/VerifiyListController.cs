using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class VerifiyListController : BaseApiController
    {
        /// <summary>
        /// VerifiyList
        /// </summary>
        private readonly IVerifiyListServices _verifiyListServices;

        public VerifiyListController(IVerifiyListServices VerifiyListServices)
        {
            _verifiyListServices = VerifiyListServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<VerifiyListEntity>>> GetList([FromBody] VerifiyListRequestModel reqModel)
        {
            var data = await _verifiyListServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<VerifiyListEntity>>> GetPageList([FromBody] VerifiyListRequestModel reqModel)
        {
            var data = await _verifiyListServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<VerifiyListEntity>> GetEntity(string id)
        {
            var data = await _verifiyListServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] VerifiyListEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _verifiyListServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _verifiyListServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] VerifiyListEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _verifiyListServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _verifiyListServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class VerifiyListRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}