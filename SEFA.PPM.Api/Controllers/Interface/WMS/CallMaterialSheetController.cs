using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.WMS;

namespace SEFA.PPM.Api.Controllers.Interface.WMS
{
    /// <summary>
    /// 叫料单控制器
    /// </summary>
    [Route("wms/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class CallMaterialSheetController : BaseApiController
    {
        private readonly ICallMaterialSheetServices _callMaterialSheetServices;

        public CallMaterialSheetController(ICallMaterialSheetServices callMaterialSheetServices)
        {
            _callMaterialSheetServices = callMaterialSheetServices;
        }

        /// <summary>
        /// 新增叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddCallMaterialSheet([FromBody] CallMaterialSheetRequestModel request)
        {
            var result = await _callMaterialSheetServices.AddCallMaterialSheet(request);
            if (result)
            {
                return Success<string>("叫料单新增成功");
            }

            return Failed("叫料单新增失败");
        }

        /// <summary>
        /// 根据产线ID获取工单列表
        /// </summary>
        /// <param name="lineId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ProductionOrderEntity>>> GetProductionOrderListByLine([FromBody] string lineId)
        {
            var result = new MessageModel<List<ProductionOrderEntity>>();
            return result;
        }
        
        /// <summary>
        /// 根据产线ID获取工单列表
        /// </summary>
        /// <param name="lineId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<CallMaterialDetailEntity>>> GetCallMaterialDetailsByOrder([FromBody] string productionOrderId)
        {
            var result = new MessageModel<List<CallMaterialDetailEntity>>();
            return result;
        }

        /// <summary>
        /// 修改叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateCallMaterialSheet(
            [FromBody] CallMaterialSheetRequestModel request)
        {
            var result = await _callMaterialSheetServices.UpdateCallMaterialSheet(request);
            if (result)
            {
                return Success<string>("叫料单修改成功");
            }

            return Failed("叫料单修改失败");
        }

        /// <summary>
        /// 删除叫料单（逻辑删除）
        /// </summary>
        /// <param name="ids">叫料单ID列表</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteCallMaterialSheet([FromBody] string[] ids)
        {
            var result = await _callMaterialSheetServices.DeleteCallMaterialSheet(ids);
            if (result)
            {
                return Success<string>("叫料单删除成功");
            }

            return Failed("叫料单删除失败");
        }

        /// <summary>
        /// 分页查询叫料单
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<CallMaterialHeaderView>>> GetPageList(
            [FromBody] CallMaterialHeaderRequestModel request)
        {
            var data = await _callMaterialSheetServices.GetPageList(request);
            return Success(data, "叫料单查询成功");
        }

        /// <summary>
        /// 获取叫料单详情
        /// </summary>
        /// <param name="id">叫料单ID</param>
        /// <returns>叫料单详情</returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<CallMaterialSheetViewModel>> GetCallMaterialSheetById(string id)
        {
            var data = await _callMaterialSheetServices.GetCallMaterialSheetById(id);
            if (data != null)
            {
                return Success(data, "叫料单详情获取成功");
            }

            return Failed<CallMaterialSheetViewModel>("叫料单详情不存在");
        }

        /// <summary>
        /// 根据叫料单ID获取明细列表
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>明细列表</returns>
        [HttpGet("{sheetId}/details")]
        public async Task<MessageModel<CallMaterialSheetViewModel>> GetCallMaterialDetailsBySheetId(string sheetId)
        {
            var data = await _callMaterialSheetServices.GetCallMaterialSheetById(sheetId);
            if (data != null)
            {
                return Success(data, "叫料单明细获取成功");
            }

            return Failed<CallMaterialSheetViewModel>("叫料单不存在");
        }
    }
}