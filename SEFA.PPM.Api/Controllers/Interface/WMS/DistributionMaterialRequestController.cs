using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.MKM.Dto;

namespace SEFA.PPM.Api.Controllers.Interface.WMS
{
    /// <summary>
    /// 发料通知制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class DistributionMaterialRequestController : BaseApiController
    {
        private readonly IDistributionMaterialRequestServices _requestServices;
        private readonly IWMSInterfaceLogServices _logServices;

        public DistributionMaterialRequestController(
            IDistributionMaterialRequestServices requestServices,
            IWMSInterfaceLogServices logServices)
        {
            _requestServices = requestServices;
            _logServices = logServices;
        }

        /// <summary>
        /// 新增发料通知
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Add([FromBody] DistributionMaterialRequestEntity requestEntity)
        {
            try
            {
                // 设置创建时间
                requestEntity.CreateDate = DateTime.Now;
                requestEntity.ModifyDate = DateTime.Now;

                var result = await _requestServices.AddAsync(requestEntity);

                if (result)
                {
                    // 记录成功日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Add",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                        ResponseData = "新增叫料申请成功",
                        IsSuccess = true,
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = requestEntity.CreateUserId ?? "SYSTEM"
                    });

                    return Ok(new { success = true, msg = "新增叫料申请成功" });
                }
                else
                {
                    // 记录失败日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Add",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                        ResponseData = "新增叫料申请失败",
                        IsSuccess = false,
                        ErrorMessage = "新增叫料申请失败",
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = requestEntity.CreateUserId ?? "SYSTEM"
                    });

                    return Ok(new { success = false, msg = "新增叫料申请失败" });
                }
            }
            catch (Exception ex)
            {
                // 记录异常日志
                await _logServices.AddAsync(new WMSInterfaceLogEntity
                {
                    InterfaceName = "DistributionMaterialRequest/Add",
                    RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                    ResponseData = "新增叫料申请异常",
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Direction = 1, // WMS调用我们
                    CreateDate = DateTime.Now,
                    CreateUserId = requestEntity?.CreateUserId ?? "SYSTEM"
                });

                return Ok(new { success = false, msg = "新增叫料申请异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取叫料申请
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var result = await _requestServices.GetByIdAsync(id);

                if (result != null)
                {
                    return Ok(new { success = true, data = result });
                }
                else
                {
                    return Ok(new { success = false, msg = "未找到指定的叫料申请" });
                }
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, msg = "查询叫料申请异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取叫料申请列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetPageList([FromBody] DistributionMaterialRequestQueryDto queryDto,
            int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                var result = await _requestServices.GetPageListAsync(queryDto, pageIndex, pageSize);

                return Ok(new
                {
                    success = true,
                    data = result.data,
                    totalCount = result.dataCount,
                    pageIndex = pageIndex,
                    pageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, msg = "查询叫料申请列表异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取叫料申请列表（不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] DistributionMaterialRequestQueryDto queryDto)
        {
            try
            {
                var result = await _requestServices.GetListAsync(queryDto);

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, msg = "查询叫料申请列表异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取叫料申请列表（包含明细，分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetPageListWithDetails([FromBody] DistributionMaterialRequestQueryDto queryDto,
            int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                var result = await _requestServices.GetPageListWithDetailsAsync(queryDto, pageIndex, pageSize);

                return Ok(new
                {
                    success = true,
                    data = result.data,
                    totalCount = result.dataCount,
                    pageIndex = pageIndex,
                    pageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, msg = "查询叫料申请列表（含明细）异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取叫料申请列表（包含明细，不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetListWithDetails([FromBody] DistributionMaterialRequestQueryDto queryDto)
        {
            try
            {
                var result = await _requestServices.GetListWithDetailsAsync(queryDto);

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, msg = "查询叫料申请列表（含明细）异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 更新叫料申请
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>操作结果</returns>
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] DistributionMaterialRequestEntity requestEntity)
        {
            try
            {
                requestEntity.ModifyDate = DateTime.Now;

                var result = await _requestServices.UpdateAsync(requestEntity);

                if (result)
                {
                    // 记录成功日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Update",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                        ResponseData = "更新叫料申请成功",
                        IsSuccess = true,
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = requestEntity.ModifyUserId ?? "SYSTEM"
                    });

                    return Ok(new { success = true, msg = "更新叫料申请成功" });
                }
                else
                {
                    // 记录失败日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Update",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                        ResponseData = "更新叫料申请失败",
                        IsSuccess = false,
                        ErrorMessage = "更新叫料申请失败",
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = requestEntity.ModifyUserId ?? "SYSTEM"
                    });

                    return Ok(new { success = false, msg = "更新叫料申请失败" });
                }
            }
            catch (Exception ex)
            {
                // 记录异常日志
                await _logServices.AddAsync(new WMSInterfaceLogEntity
                {
                    InterfaceName = "DistributionMaterialRequest/Update",
                    RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(requestEntity),
                    ResponseData = "更新叫料申请异常",
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Direction = 1, // WMS调用我们
                    CreateDate = DateTime.Now,
                    CreateUserId = requestEntity?.ModifyUserId ?? "SYSTEM"
                });

                return Ok(new { success = false, msg = "更新叫料申请异常：" + ex.Message });
            }
        }

        /// <summary>
        /// 删除叫料申请（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await _requestServices.DeleteAsync(id);

                if (result)
                {
                    // 记录成功日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Delete",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(new { id = id }),
                        ResponseData = "删除叫料申请成功",
                        IsSuccess = true,
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = "SYSTEM"
                    });

                    return Ok(new { success = true, msg = "删除叫料申请成功" });
                }
                else
                {
                    // 记录失败日志
                    await _logServices.AddAsync(new WMSInterfaceLogEntity
                    {
                        InterfaceName = "DistributionMaterialRequest/Delete",
                        RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(new { id = id }),
                        ResponseData = "删除叫料申请失败",
                        IsSuccess = false,
                        ErrorMessage = "删除叫料申请失败",
                        Direction = 1, // WMS调用我们
                        CreateDate = DateTime.Now,
                        CreateUserId = "SYSTEM"
                    });

                    return Ok(new { success = false, msg = "删除叫料申请失败" });
                }
            }
            catch (Exception ex)
            {
                // 记录异常日志
                await _logServices.AddAsync(new WMSInterfaceLogEntity
                {
                    InterfaceName = "DistributionMaterialRequest/Delete",
                    RequestData = Newtonsoft.Json.JsonConvert.SerializeObject(new { id = id }),
                    ResponseData = "删除叫料申请异常",
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Direction = 1, // WMS调用我们
                    CreateDate = DateTime.Now,
                    CreateUserId = "SYSTEM"
                });

                return Ok(new { success = false, msg = "删除叫料申请异常：" + ex.Message });
            }
        }
    }
}