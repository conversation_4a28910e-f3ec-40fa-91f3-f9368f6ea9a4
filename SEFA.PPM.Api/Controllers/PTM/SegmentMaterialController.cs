using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class SegmentMaterialController : BaseApiController
    {
        /// <summary>
        /// SegmentMaterial
        /// </summary>
        private readonly ISegmentMaterialServices _segmentMaterialServices;

        public SegmentMaterialController(ISegmentMaterialServices SegmentMaterialServices)
        {
            _segmentMaterialServices = SegmentMaterialServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<SegmentMaterialEntity>>> GetList([FromBody] SegmentMaterialRequestModel reqModel)
        {
            var data = await _segmentMaterialServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SegmentMaterialEntity>>> GetPageList([FromBody] SegmentMaterialRequestModel reqModel)
        {
            var data = await _segmentMaterialServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据产品版本id获取工艺长文本列表（带长文本解密）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageModel<MaterialProcessDataEntity>> GetProcessDataList([FromBody] MaterialProcessDataRequestModel reqModel)
        {
            return await _segmentMaterialServices.GetProcessDataList(reqModel);
        }

        /// <summary>
        /// 保存新版本工艺长文本entity.ID用旧版本ID，entity.ProcessData传输工艺长文本明文
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveProcessData([FromBody] MaterialProcessDataEntity entity)
        {
            return await _segmentMaterialServices.SaveProcessData(entity);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Release([FromBody] string id)
        {
            return await _segmentMaterialServices.Release(id);
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdatePO([FromBody] string key)
        {
            return await _segmentMaterialServices.UpdatePO(key);
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SegmentMaterialEntity>> GetEntity(string id)
        {
            var data = await _segmentMaterialServices.QueryById(id);
            return Success(data, "获取成功");
        }
    }
    //public class SegmentMaterialRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}