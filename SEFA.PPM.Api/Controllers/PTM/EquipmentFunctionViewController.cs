using SEFA.PTM.IServices;
using SEFA.Base.Model;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PTMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentFunctionViewController : BaseApiController
    {
        /// <summary>
        /// EquipmentFunctionView
        /// </summary>
        private readonly IEquipmentFunctionViewServices _equipmentFunctionViewServices;
    
        public EquipmentFunctionViewController(IEquipmentFunctionViewServices EquipmentFunctionViewServices)
        {
            _equipmentFunctionViewServices = EquipmentFunctionViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentFunctionViewEntity>>> GetList([FromBody] EquipmentFunctionViewRequestModel reqModel)
        {
            var data = await _equipmentFunctionViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

		[HttpGet]
		public async Task<MessageModel<List<GroupModel<EquipmentGroupEntity>>>> GetEquipmentGroups()
		{
			return await _equipmentFunctionViewServices.GetEquipmentGroups();
		}
		
		[HttpPost]
        public async Task<MessageModel<PageModel<EquipmentFunctionViewEntity>>> GetPageList([FromBody] EquipmentFunctionViewRequestModel reqModel)
        {
            var data = await _equipmentFunctionViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentFunctionViewEntity>> GetEntity(string id)
        {
            var data = await _equipmentFunctionViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentFunctionViewEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _equipmentFunctionViewServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _equipmentFunctionViewServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class EquipmentFunctionViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}