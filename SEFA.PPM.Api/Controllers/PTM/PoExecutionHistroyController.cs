using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.MKM.Model.Models.MKM;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class PoExecutionHistroyController : BaseApiController
    {
        /// <summary>
        /// PoExecutionHistroy
        /// </summary>
        private readonly IPoExecutionHistroyServices _poExecutionHistroyServices;
    
        public PoExecutionHistroyController(IPoExecutionHistroyServices PoExecutionHistroyServices)
        {
            _poExecutionHistroyServices = PoExecutionHistroyServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<PoExecutionHistroyViewEntity>>> GetList([FromBody] PoExecutionHistroyRequestModel reqModel)
        {
            var data = await _poExecutionHistroyServices.GetList(reqModel);
            return Success(data, "获取成功");
        }


		[HttpPost]
		public async Task<MessageModel<List<Select>>> GetRunOrderList([FromBody] PoExecutionHistroyRequestModel reqModel)
		{
			var data = await _poExecutionHistroyServices.GetRunOrderList(reqModel);
			return Success(data, "获取成功");
		}

		[HttpPost]
        public async Task<MessageModel<PageModel<PoExecutionHistroyViewEntity>>> GetPageList([FromBody] PoExecutionHistroyRequestModel reqModel)
        {
            var data = await _poExecutionHistroyServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoExecutionHistroyViewEntity>> GetEntity(string id)
        {
            var data = await _poExecutionHistroyServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] PoExecutionHistroyEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _poExecutionHistroyServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _poExecutionHistroyServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class PoExecutionHistroyRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}