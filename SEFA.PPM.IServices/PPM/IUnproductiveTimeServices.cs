using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using static SEFA.PPM.Model.Models.PTM.GroupAndReasonModel;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IUnproductiveTimeServices
	/// </summary>	
    public interface IUnproductiveTimeServices :IBaseServices<UnproductiveTimeEntity>
	{
		Task<PageModel<UnproductiveTimeEntity>> GetPageList(UnproductiveTimeRequestModel reqModel);
        Task<bool> DelByIdList(string[] idList);
        //Task<MessageModel> UpDelete(string[] ids);
        Task<List<UnproductiveTimeEntity>> GetList(UnproductiveTimeRequestModel reqModel);
        Task<List<GroupAndReasonList>> GetGroupAndReasonModelList(string search);
        Task<bool> SaveForm(UnproductiveTimeEntity entity);
    }
}