using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ISappackorderrecordServices
	/// </summary>	
    public interface ISappackorderrecordServices :IBaseServices<SappackorderrecordEntity>
	{
		Task<PageModel<SappackorderrecordEntity>> GetPageList(SappackorderrecordRequestModel reqModel);

        Task<List<SappackorderrecordEntity>> GetList(SappackorderrecordRequestModel reqModel);

		Task<bool> SaveForm(SappackorderrecordEntity entity);

		Task<MessageModel<string>> DealSapPackOrder ();
    }
}