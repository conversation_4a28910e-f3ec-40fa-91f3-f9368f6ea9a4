using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IWorkorderthroatServices
	/// </summary>	
    public interface IWorkorderthroatServices :IBaseServices<WorkorderthroatEntity>
	{
		Task<PageModel<WorkorderthroatEntity>> GetPageList(WorkorderthroatRequestModel reqModel);

        Task<List<WorkorderthroatEntity>> GetList(WorkorderthroatRequestModel reqModel);

		Task<MessageModel<string>> SaveForm(WorkorderthroatEntity entity);
		Task<bool> DeleteDataByIds(string[] ids);
    }
}