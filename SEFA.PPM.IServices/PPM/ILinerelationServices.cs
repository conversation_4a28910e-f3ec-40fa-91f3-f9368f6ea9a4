using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ILinerelationServices
	/// </summary>	
    public interface ILinerelationServices :IBaseServices<LinerelationEntity>
	{
		Task<PageModel<LinerelationEntity>> GetPageList(LinerelationRequestModel reqModel);

        Task<List<LinerelationEntity>> GetList(LinerelationRequestModel reqModel);

		Task<bool> SaveForm(LinerelationEntity entity);
    }
}