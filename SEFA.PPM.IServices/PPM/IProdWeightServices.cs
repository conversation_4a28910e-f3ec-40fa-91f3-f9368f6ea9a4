using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IProdWeightServices
	/// </summary>	
    public interface IProdWeightServices :IBaseServices<ProdWeightEntity>
	{
		Task<PageModel<ProdWeightModel>> GetPageList(ProdWeightRequestModel reqModel);

        Task<List<ProdWeightEntity>> GetList(ProdWeightRequestModel reqModel);

		Task<bool> SaveForm(ProdWeightEntity entity);

		Task<ResultString> ImportData ([FromForm] FileImportDto input);
    }
}