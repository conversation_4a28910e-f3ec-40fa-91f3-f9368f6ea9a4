using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models.WMS;
using SEFA.PPM.Model.ViewModels.WMS;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices.WMS
{
    /// <summary>
    /// 叫料单服务接口
    /// </summary>
    public interface ICallMaterialSheetServices : IBaseServices<CallMaterialSheetEntity>
    {
        /// <summary>
        /// 新增叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>是否成功</returns>
        Task<bool> AddCallMaterialSheet(CallMaterialSheetRequestModel request);

        /// <summary>
        /// 修改叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateCallMaterialSheet(CallMaterialSheetRequestModel request);

        /// <summary>
        /// 删除叫料单（逻辑删除）
        /// </summary>
        /// <param name="ids">叫料单ID列表</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteCallMaterialSheet(string[] ids);

        /// <summary>
        /// 分页查询叫料单
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PageModel<CallMaterialHeaderView>> GetPageList(CallMaterialHeaderRequestModel request);

        /// <summary>
        /// 获取叫料单详情
        /// </summary>
        /// <param name="id">叫料单ID</param>
        /// <returns>叫料单详情</returns>
        Task<CallMaterialSheetViewModel> GetCallMaterialSheetById(string id);

        /// <summary>
        /// 根据叫料单ID获取明细列表
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>明细列表</returns>
        Task<List<CallMaterialDetailEntity>> GetCallMaterialDetailsBySheetId(string sheetId);
    }
}