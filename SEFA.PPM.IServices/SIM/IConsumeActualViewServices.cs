using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IConsumeActualViewServices
	/// </summary>	
    public interface IConsumeActualViewServices :IBaseServices<ConsumeActualViewEntity>
	{
		Task<PageModel<ConsumeActualViewEntity>> GetPageList(ConsumeActualViewRequestModel reqModel);

        Task<List<ConsumeActualViewEntity>> GetList(ConsumeActualViewRequestModel reqModel);

		Task<bool> SaveForm(ConsumeActualViewEntity entity);
    }
}