using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IKpiTgtViewServices
	/// </summary>	
    public interface IKpiTgtViewServices :IBaseServices<KpiTgtViewEntity>
	{
		Task<PageModel<KpiTgtViewEntity>> GetPageList(KpiTgtViewRequestModel reqModel);

        Task<List<KpiTgtViewEntity>> GetList(KpiTgtViewRequestModel reqModel);

		Task<bool> SaveForm(KpiTgtViewEntity entity);
    }
}