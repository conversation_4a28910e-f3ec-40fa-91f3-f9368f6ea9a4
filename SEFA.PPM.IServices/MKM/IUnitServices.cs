using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;



namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IUnitServices
	/// </summary>	
    public interface IUnitServices :IBaseServices<UnitEntity>
	{
		Task<PageModel<UnitEntity>> GetPageList(UnitRequestModel reqModel);

        Task<List<UnitEntity>> GetList(UnitRequestModel reqModel);

		Task<bool> SaveForm(UnitEntity entity);
    }
}