using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IMPreparationIiViewServices
	/// </summary>	
    public interface IMPreparationIiViewServices :IBaseServices<MPreparationIiViewEntity>
	{
		Task<PageModel<MPreparationIiViewEntity>> GetPageList(MPreparationIiViewRequestModel reqModel);

        Task<List<MPreparationIiViewEntity>> GetList(MPreparationIiViewRequestModel reqModel);

		Task<bool> SaveForm(MPreparationIiViewEntity entity);
    }
}