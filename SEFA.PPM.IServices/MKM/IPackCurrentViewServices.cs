using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackCurrentViewServices
	/// </summary>	
    public interface IPackCurrentViewServices :IBaseServices<PackCurrentViewEntity>
	{
		Task<PageModel<PackCurrentViewEntity>> GetPageList(PackCurrentViewRequestModel reqModel);

        Task<List<PackCurrentViewEntity>> GetList(PackCurrentViewRequestModel reqModel);

		Task<bool> SaveForm(PackCurrentViewEntity entity);
    }
}