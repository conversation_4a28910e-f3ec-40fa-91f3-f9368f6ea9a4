using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IUnitmanageServices
	/// </summary>	
    public interface IUnitmanageServices :IBaseServices<UnitmanageEntity>
	{
		Task<PageModel<UnitmanageEntity>> GetPageList(UnitmanageRequestModel reqModel);

        Task<List<UnitmanageEntity>> GetList(UnitmanageRequestModel reqModel);

		Task<bool> SaveForm(UnitmanageEntity entity);
    }
}