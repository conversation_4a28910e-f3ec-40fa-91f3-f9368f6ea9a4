using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ICookingProgressViewServices
	/// </summary>	
    public interface ICookingProgressViewServices :IBaseServices<CookingProgressViewEntity>
	{
		Task<PageModel<CookingProgressViewEntity>> GetPageList(CookingProgressViewRequestModel reqModel);

		//Task<List<CookingProgressViewEntity>> GetSchedule();


        Task<bool> SaveForm(CookingProgressViewEntity entity);	
    }
}