using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackNextCurrentViewServices
	/// </summary>	
    public interface IPackNextCurrentViewServices :IBaseServices<PackNextCurrentViewEntity>
	{
		Task<PageModel<PackNextCurrentViewEntity>> GetPageList(PackNextCurrentViewRequestModel reqModel);

        Task<List<PackNextCurrentViewEntity>> GetList(PackNextCurrentViewRequestModel reqModel);

		Task<bool> SaveForm(PackNextCurrentViewEntity entity);
    }
}