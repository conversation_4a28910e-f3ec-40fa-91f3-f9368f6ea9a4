using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ITPrecheckDetailViewServices
	/// </summary>	
    public interface ITPrecheckDetailViewServices :IBaseServices<TPrecheckDetailViewEntity>
	{
		Task<PageModel<TPrecheckDetailViewEntity>> GetPageList(TPrecheckDetailViewRequestModel reqModel);

        Task<List<TPrecheckDetailViewEntity>> GetList(TPrecheckDetailViewRequestModel reqModel);

		Task<bool> SaveForm(TPrecheckDetailViewEntity entity);
    }
}