using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IDicContainerBpalletViewServices
	/// </summary>	
    public interface IDicContainerBpalletViewServices :IBaseServices<DicContainerBpalletViewEntity>
	{
		Task<PageModel<DicContainerBpalletViewEntity>> GetPageList(DicContainerBpalletViewRequestModel reqModel);

        Task<List<DicContainerBpalletViewEntity>> GetList(DicContainerBpalletViewRequestModel reqModel);

		Task<bool> SaveForm(DicContainerBpalletViewEntity entity);
    }
}