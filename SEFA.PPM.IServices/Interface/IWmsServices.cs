using System;
using System.Threading.Tasks;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.IServices;

public interface IWmsServices
{
    /// <summary>
    /// 线边仓叫料接口
    /// </summary>
    /// <param name="request">叫料请求参数</param>
    /// <returns>叫料结果</returns>
    // Task<MessageModel<String>> CallMaterialAsync(CallMaterialSheet request);

    /// <summary>
    /// 物料到达通知接口
    /// </summary>
    /// <param name="request">物料到达请求参数</param>
    /// <returns>通知结果</returns>
    Task<MessageModel<String>> SendDistributionMaterialAsync(DistributionMaterialRequest request);

    /// <summary>
    /// 托盘解绑接口
    /// </summary>
    /// <param name="request">托盘解绑请求参数</param>
    /// <returns>解绑结果</returns>
    Task<MessageModel<String>> PalletReleaseAsync(PalletReleaseRequest request);

    /// <summary>
    /// 组托退库申请接口
    /// </summary>
    /// <param name="request">组托退库申请请求参数</param>
    /// <returns>申请结果</returns>
    Task<MessageModel<String>> PalletReturnRequestAsync(PalletReturnRequest request);
    
    /// <summary>
    /// 打印标签接口
    /// </summary>
    /// <param name="request">打印标签请求参数</param>
    /// <returns>打印结果</returns>
    Task<MessageModel<String>> BarCodePrintAsync(BarCodePrintRequest request);

    /// <summary>
    /// IBC桶空桶退库申请接口
    /// </summary>
    /// <param name="request">退库请求参数</param>
    /// <returns>申请结果</returns>
    Task<MessageModel<String>> EmptyIBCReturnAsync(EmptyIBCReturnRequest request);

    /// <summary>
    /// IBC桶配置成品入库申请接口
    /// </summary>
    /// <param name="request">入库请求参数</param>
    /// <returns>申请结果</returns>
    Task<MessageModel<String>> IBCMaterialInBoundAsync(IBCMaterialInBoundRequest request);

    /// <summary>
    /// 线边物料出库接口
    /// </summary>
    /// <param name="request">出库请求参数</param>
    /// <returns>出库结果</returns>
    Task<MessageModel<String>> LineWarehouseMaterialOutBoundAsync(LineWarehouseMaterialOutBoundRequest request);
}