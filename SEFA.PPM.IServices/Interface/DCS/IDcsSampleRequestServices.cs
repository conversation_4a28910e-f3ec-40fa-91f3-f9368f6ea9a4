using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsSampleRequestServices
	/// </summary>	
    public interface IDcsSampleRequestServices :IBaseServices<DcsSampleRequestEntity>
	{
		Task<PageModel<DcsSampleRequestEntity>> GetPageList(DcsSampleRequestRequestModel reqModel);

        Task<List<DcsSampleRequestEntity>> GetList(DcsSampleRequestRequestModel reqModel);

		Task<bool> SaveForm(DcsSampleRequestEntity entity);
    }
}