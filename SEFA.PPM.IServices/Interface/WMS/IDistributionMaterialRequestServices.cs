using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.MKM.Dto;

namespace SEFA.PPM.IServices.Interface.WMS
{
    /// <summary>
    /// 叫料申请服务接口
    /// </summary>
    public interface IDistributionMaterialRequestServices
    {
        /// <summary>
        /// 新增叫料申请
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>是否成功</returns>
        Task<bool> AddAsync(DistributionMaterialRequestEntity requestEntity);

        /// <summary>
        /// 根据ID获取叫料申请
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请信息</returns>
        Task<DistributionMaterialRequestEntity> GetByIdAsync(string id);

        /// <summary>
        /// 获取叫料申请列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        Task<PageModel<DistributionMaterialRequestEntity>> GetPageListAsync(
            DistributionMaterialRequestQueryDto queryDto, int pageIndex, int pageSize);

        /// <summary>
        /// 获取叫料申请列表（不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        Task<List<DistributionMaterialRequestEntity>> GetListAsync(DistributionMaterialRequestQueryDto queryDto);

        /// <summary>
        /// 获取叫料申请列表（包含明细，分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>叫料申请列表</returns>
        Task<PageModel<DistributionMaterialRequestEntity>> GetPageListWithDetailsAsync(
            DistributionMaterialRequestQueryDto queryDto, int pageIndex, int pageSize);

        /// <summary>
        /// 获取叫料申请列表（包含明细，不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请列表</returns>
        Task<List<DistributionMaterialRequestEntity>> GetListWithDetailsAsync(DistributionMaterialRequestQueryDto queryDto);

        /// <summary>
        /// 更新叫料申请
        /// </summary>
        /// <param name="requestEntity">叫料申请信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAsync(DistributionMaterialRequestEntity requestEntity);

        /// <summary>
        /// 删除叫料申请（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(string id);
    }
}