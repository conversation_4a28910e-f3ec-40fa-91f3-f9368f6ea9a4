using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.IServices.Interface.WMS
{
    /// <summary>
    /// 叫料申请明细服务接口
    /// </summary>
    public interface IDistributionMaterialDetailServices
    {
        /// <summary>
        /// 根据主表ID获取叫料申请明细列表
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>叫料申请明细列表</returns>
        Task<List<DistributionMaterialDetailEntity>> GetByRequestIdAsync(string requestId);

        /// <summary>
        /// 根据ID获取叫料申请明细
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请明细信息</returns>
        Task<DistributionMaterialDetailEntity> GetByIdAsync(string id);

        /// <summary>
        /// 新增叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        Task<bool> AddAsync(DistributionMaterialDetailEntity detailEntity);

        /// <summary>
        /// 批量新增叫料申请明细
        /// </summary>
        /// <param name="details">叫料申请明细列表</param>
        /// <returns>是否成功</returns>
        Task<bool> AddRangeAsync(List<DistributionMaterialDetailEntity> details);

        /// <summary>
        /// 更新叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAsync(DistributionMaterialDetailEntity detailEntity);

        /// <summary>
        /// 删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// 根据主表ID删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteByRequestIdAsync(string requestId);
    }
}