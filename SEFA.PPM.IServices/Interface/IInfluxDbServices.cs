using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using SEFA.PPM.Model.Models.InfluxDB;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// IInfluxDbServices
	/// </summary>	
	public interface IInfluxDbServices
	{
		Task<List<TagValue>> GetInfluxData(DateTime start, DateTime end, string tagId, string tag);
		Task<List<TagValue>> GetInfluxData(DateTime start, DateTime end, string tagId, string tag, int skip, int take);
		Task<TagValue> GetLastInfluxData(DateTime start, DateTime end, string tagId, string tag);
		Task<List<TagValue>> GetLastInfluxDataList(DateTime start, DateTime end, string tagId, string[] tag);
		Task<TagValue> GetMaxInfluxData(DateTime start, DateTime end, string tagId, string tag);
	}


}