git pull;
find .PublishFiles/ -type f -and ! -path '*/wwwroot/images/*' ! -name 'appsettings.*' |xargs rm -rf
dotnet build;
rm -rf /home/<USER>/SEFA.PPM.Api/bin/Debug/.PublishFiles;
dotnet publish -o /home/<USER>/SEFA.PPM.Api/bin/Debug/.PublishFiles;
# cp -r /home/<USER>/SEFA.PPM.Api/bin/Debug/.PublishFiles ./;
awk 'BEGIN { cmd="cp -ri /home/<USER>/SEFA.PPM.Api/bin/Debug/.PublishFiles ./"; print "n" |cmd; }'
echo "Successfully!!!! ^ please see the file .PublishFiles";