<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
	</PropertyGroup>


  <ItemGroup>
    <PackageReference Include="MongoDB.Bson" Version="2.10.0" />
    <PackageReference Include="MongoDB.Driver.Core" Version="2.10.0" />
  </ItemGroup>
  
  
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>..\SEFA.PPM.Api\bin\Debug\</OutputPath>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<OutputPath>..\SEFA.PPM\bin\Release\</OutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.2.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\SEFA.PPM.Model\SEFA.PPM.Model.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="SEFA.Base.Model">
	    <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Repository">
	    <HintPath>..\..\common\SEFA.Base.Repository.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Model">
	    <HintPath>..\..\common\SEFA.DFM.Model.dll</HintPath>
	  </Reference>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Interface\" />
	  <Folder Include="Interface\MKM\" />
	  <Folder Include="Report\" />
	</ItemGroup>

</Project>
