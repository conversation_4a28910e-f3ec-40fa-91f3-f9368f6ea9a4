using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.SIM;

namespace SEFA.PPM.Repository.SIM
{
    /// <summary>
    /// EnergyByorderRepository
    /// </summary>
    public class EnergyByorderRepository : BaseRepository<EnergyByorderEntity>, IEnergyByorderRepository
    {
        public EnergyByorderRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}