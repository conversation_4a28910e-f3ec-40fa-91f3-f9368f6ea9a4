using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ProdtgtSaucetypeViewRepository
	/// </summary>
    public class ProdtgtSaucetypeViewRepository : BaseRepository<ProdtgtSaucetypeViewEntity>, IProdtgtSaucetypeViewRepository
    {
        public ProdtgtSaucetypeViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}