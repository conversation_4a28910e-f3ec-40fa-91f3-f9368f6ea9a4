using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PlanOrderRepository
	/// </summary>
    public class PlanOrderRepository : BaseRepository<PlanOrderEntity>, IPlanOrderRepository
    {
        public PlanOrderRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}