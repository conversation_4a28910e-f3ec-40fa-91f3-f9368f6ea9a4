using SEFA.Base.IRepository.Base;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IRepository
{
    /// <summary>
    /// ILogsheetHistoryListViewRepository
    /// </summary>	
    public interface ILogsheetHistoryListViewRepository : IBaseRepository<LogsheetHistoryListViewRequireEntity>
    {
    }
}