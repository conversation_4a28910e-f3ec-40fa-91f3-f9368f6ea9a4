CREATE TABLE MKM_B_WMS_INTERFACE_LOG (
    ID NVARCHAR(50) NOT NULL PRIMARY KEY,
    INTERFACENAME NVARCHAR(100) NOT NULL,
    REQUESTDATA NTEXT NULL,
    R<PERSON>PONSEDATA NTEXT NULL,
    ISSUCCESS BIT NOT NULL,
    ERRORMESSAGE NVARCHAR(500) NULL,
    DIRECTION INT NOT NULL,
    CREATEDATE DATETIME NOT NULL,
    CREATEUSERID NVARCHAR(50) NOT NULL,
    MODIFYDATE DATETIME NULL,
    MODIFYUSERID NVARCHAR(50) NULL,
    UPDATETIMESTAMP TIMESTAMP,
    DELETED INT DEFAULT 0
);

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'主键标识', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'ID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'接口名称', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'INTERFACENAME';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'请求参数', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'REQUESTDATA';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'响应结果', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'RESPONSEDATA';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'是否成功', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'ISSUCCESS';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'错误信息', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'ERRORMESSAGE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'调用方向（0:我们调用WMS，1:WMS调用我们）', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'DIRECTION';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'CREATEDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'CREATEUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'MODIFYDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'MODIFYUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'时间戳', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'UPDATETIMESTAMP';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'逻辑删除标记，0：未删除，1：已删除', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG',
    @level2type = N'COLUMN', @level2name = 'DELETED';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'WMS接口调用日志表', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_WMS_INTERFACE_LOG';