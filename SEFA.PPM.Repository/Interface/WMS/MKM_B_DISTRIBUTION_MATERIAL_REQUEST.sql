CREATE TABLE MKM_B_DISTRIBUTION_MATERIAL_REQUEST (
    ID NVARCHAR(50) NOT NULL PRIMARY KEY,
    REQUESTSHEETNO NVARCHAR(50) NOT NULL,
    LINEWAREHOUSECODE NVARCHAR(50) NOT NULL,
    CREATEDATE DATETIME NOT NULL,
    CREATEUSERID NVARCHAR(50) NOT NULL,
    MODIFYDATE DATETIME NULL,
    MODIFYUSERID NVARCHAR(50) NULL,
    UPDATETIMESTAMP TIMESTAMP,
    DELETED INT DEFAULT 0
);

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'主键标识', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'ID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'叫料申请单号', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'REQUESTSHEETNO';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'送达线边库编号', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'LINEWAREHOUSECODE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'CREATEDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'CREATEUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'MODIFYDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'MODIFYUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'时间戳', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'UPDATETIMESTAMP';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'逻辑删除标记，0：未删除，1：已删除', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST',
    @level2type = N'COLUMN', @level2name = 'DELETED';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'WMS发料申请主表', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_REQUEST';

CREATE TABLE MKM_B_DISTRIBUTION_MATERIAL_DETAIL (
    ID NVARCHAR(50) NOT NULL PRIMARY KEY,
    REQUESTID NVARCHAR(50) NOT NULL,
    PLANT NVARCHAR(50) NOT NULL,
    MATERIALCODE NVARCHAR(50) NOT NULL,
    MATERIALNAME NVARCHAR(200) NOT NULL,
    MATERIALVERSIONCODE NVARCHAR(50) NULL,
    BATCHNO NVARCHAR(50) NULL,
    PALLETNO NVARCHAR(50) NULL,
    BARCODE NVARCHAR(100) NULL,
    QUANTITY DECIMAL(18,6) NOT NULL,
    UNIT NVARCHAR(20) NULL,
    DENSITY DECIMAL(18,6) NULL,
    COACONTENT DECIMAL(18,6) NULL,
    REMARK NVARCHAR(500) NULL,
    CREATEDATE DATETIME NOT NULL,
    CREATEUSERID NVARCHAR(50) NOT NULL,
    MODIFYDATE DATETIME NULL,
    MODIFYUSERID NVARCHAR(50) NULL,
    UPDATETIMESTAMP TIMESTAMP,
    DELETED INT DEFAULT 0
);

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'主键标识', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'ID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'申请主表ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'REQUESTID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'工厂', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'PLANT';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'物料编码', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'MATERIALCODE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'物料描述', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'MATERIALNAME';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'物料版本号', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'MATERIALVERSIONCODE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'批次号', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'BATCHNO';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'托盘号', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'PALLETNO';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'物料唯一标签码', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'BARCODE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'数量', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'QUANTITY';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'计量单位', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'UNIT';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'密度', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'DENSITY';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'Coa含量%', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'COACONTENT';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'备注说明', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'REMARK';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'CREATEDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'CREATEUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改时间', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'MODIFYDATE';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'修改人ID', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'MODIFYUSERID';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'时间戳', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'UPDATETIMESTAMP';
    
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'逻辑删除标记，0：未删除，1：已删除', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL',
    @level2type = N'COLUMN', @level2name = 'DELETED';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', @value = N'WMS发料申请明细表', 
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'TABLE',  @level1name = 'MKM_B_DISTRIBUTION_MATERIAL_DETAIL';

-- 添加外键约束
ALTER TABLE MKM_B_DISTRIBUTION_MATERIAL_DETAIL 
ADD CONSTRAINT FK_DISTRIBUTION_MATERIAL_DETAIL_REQUEST 
FOREIGN KEY (REQUESTID) REFERENCES MKM_B_DISTRIBUTION_MATERIAL_REQUEST(ID);