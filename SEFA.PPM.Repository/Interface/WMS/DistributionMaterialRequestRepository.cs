using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.Interface.WMS;
using SqlSugar;

namespace SEFA.PPM.Repository.Interface.WMS
{
    /// <summary>
    /// 叫料申请仓储实现类
    /// </summary>
    public class DistributionMaterialRequestRepository : BaseRepository<DistributionMaterialRequestEntity>, IDistributionMaterialRequestRepository
    {
        public DistributionMaterialRequestRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}