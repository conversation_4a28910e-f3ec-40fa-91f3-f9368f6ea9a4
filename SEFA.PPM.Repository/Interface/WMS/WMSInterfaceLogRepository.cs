using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.Repository.Interface.WMS
{
    /// <summary>
    /// WMS接口调用日志仓储实现类
    /// </summary>
    public class WMSInterfaceLogRepository : BaseRepository<WMSInterfaceLogEntity>, IWMSInterfaceLogRepository
    {
        public WMSInterfaceLogRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}