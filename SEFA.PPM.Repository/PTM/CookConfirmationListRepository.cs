using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// CookConfirmationListRepository
    /// </summary>
    public class CookConfirmationListRepository : BaseRepository<CookConfirmationListEntity>, ICookConfirmationListRepository
    {
        public CookConfirmationListRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}