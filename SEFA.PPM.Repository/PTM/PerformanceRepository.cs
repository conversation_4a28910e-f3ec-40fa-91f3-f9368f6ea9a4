using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// PerformanceRepository
    /// </summary>
    public class PerformanceRepository : BaseRepository<PerformanceEntity>, IPerformanceRepository
    {
        public PerformanceRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}