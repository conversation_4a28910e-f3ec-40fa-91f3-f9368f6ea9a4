using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackProductionFrequencyViewRepository
	/// </summary>
    public class PackProductionFrequencyViewRepository : BaseRepository<PackProductionFrequencyViewEntity>, IPackProductionFrequencyViewRepository
    {
        public PackProductionFrequencyViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}