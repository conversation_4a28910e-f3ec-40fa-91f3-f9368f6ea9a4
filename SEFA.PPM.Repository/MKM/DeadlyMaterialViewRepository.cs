using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// DeadlyMaterialViewRepository
	/// </summary>
    public class DeadlyMaterialViewRepository : BaseRepository<DeadlyMaterialViewEntity>, IDeadlyMaterialViewRepository
    {
        public DeadlyMaterialViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}