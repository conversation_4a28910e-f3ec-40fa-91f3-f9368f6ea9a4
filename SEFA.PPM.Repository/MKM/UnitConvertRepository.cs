using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// UnitConvertRepository
	/// </summary>
    public class UnitConvertRepository : BaseRepository<UnitConvertEntity>, IUnitConvertRepository
    {
        public UnitConvertRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}