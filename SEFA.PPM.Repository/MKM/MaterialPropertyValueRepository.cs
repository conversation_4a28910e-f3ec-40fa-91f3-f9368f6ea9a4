using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// MaterialPropertyValueRepository
	/// </summary>
    public class MaterialPropertyValueRepository : BaseRepository<MaterialPropertyValueEntity>, IMaterialPropertyValueRepository
    {
        public MaterialPropertyValueRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}