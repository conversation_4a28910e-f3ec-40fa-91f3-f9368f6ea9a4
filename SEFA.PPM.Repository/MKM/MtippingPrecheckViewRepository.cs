using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// MtippingPrecheckViewRepository
	/// </summary>
    public class MtippingPrecheckViewRepository : BaseRepository<MtippingPrecheckViewEntity>, IMtippingPrecheckViewRepository
    {
        public MtippingPrecheckViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}