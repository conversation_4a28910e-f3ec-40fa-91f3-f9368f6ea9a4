using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ClblDiiViewRepository
	/// </summary>
    public class ClblDiiViewRepository : BaseRepository<ClblDiiViewEntity>, IClblDiiViewRepository
    {
        public ClblDiiViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}