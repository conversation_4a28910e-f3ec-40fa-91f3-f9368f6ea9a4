using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
    public class InterfaceLogRequestModel : RequestPageModelBase
    {
        public InterfaceLogRequestModel()
        {
        }

		public string Search { get; set; }
		/// <summary>
		/// Desc:工单执行ID
		/// Default:
		/// Nullable:True
		/// </summary>
		public string ExecutionId { get; set; }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// Desc:批号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BatchNo { get; set; }
        /// <summary>
        /// Desc:接口名
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Desc:接口描述
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// Desc:日志内容
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// Desc:明细
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Detail { get; set; }

		public DateTime? StartTime { get; set; }

		public DateTime? EndTime { get; set; }


	}
}