using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoMaterialConsumeRequestModel : RequestPageModelBase
    {
        public PoMaterialConsumeRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:工单消耗需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchConsumeRequirementId { get; set; }
           /// <summary>
           /// Desc:物料批次
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialBatchNo { get; set; }
           /// <summary>
           /// Desc:消耗子批次
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialSubBatchNo { get; set; }
           /// <summary>
           /// Desc:库存位置
           /// Default:
           /// Nullable:False
           /// </summary>
        public string StorageCode { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Unit { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }
           /// <summary>
           /// Desc:备料单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SheetContainerId { get; set; }
        public string MaterialId { get; set; }
        public string MaterialName { get; set; }
        public string MaterialVersionId { get; set; }
        public string MaterialVersionNumber { get; set; }
        public string ContainerNo { get; set; }
        public string UnitId { get; set; }
        public string UnitName { get; set; }
        public string MaterialCode { get; set; }
        public string ID { get; set; }
    }
}