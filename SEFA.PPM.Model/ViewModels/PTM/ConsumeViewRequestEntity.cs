using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PTM.Model.Models
{
	///<summary>
	///生产执行-工单消耗（Consume）
	///</summary>

	public class ConsumeViewRequestEntity : ConsumeViewEntity
	{
		public ConsumeViewRequestEntity()
		{
		}
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		public string MaterialInventoryId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		public decimal Quantity3 { get; set; }

		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		public string BatchId { get; set; }
	}
}