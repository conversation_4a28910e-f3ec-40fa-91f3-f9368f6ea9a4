using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class ConfirmationRequestModel : RequestPageModelBase
    {
        public ConfirmationRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:工序ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SapSegmentId { get; set; }
           /// <summary>
           /// Desc:运行时长
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Runtime { get; set; }
           /// <summary>
           /// Desc:计划停机时长
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Plannedtime { get; set; }
           /// <summary>
           /// Desc:非计划停机时长
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Unplannedtime { get; set; }
           /// <summary>
           /// Desc:运行时长修改
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? RuntimeOverride { get; set; }
           /// <summary>
           /// Desc:计划停机时长修改
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? PlannedtimeOverride { get; set; }
           /// <summary>
           /// Desc:非计划停机时长修改
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? UnplannedtimeOverride { get; set; }
           /// <summary>
           /// Desc:人员工时
           /// Default:
           /// Nullable:False
           /// </summary>
        public int CrewHours { get; set; }
           /// <summary>
           /// Desc:良品数
           /// Default:
           /// Nullable:False
           /// </summary>
        public int GoodCount { get; set; }
           /// <summary>
           /// Desc:废品数
           /// Default:
           /// Nullable:False
           /// </summary>
        public int WasteCount { get; set; }

    }
}