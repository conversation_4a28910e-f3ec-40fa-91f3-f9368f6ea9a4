using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
    public class LogsheetImageRequestModel : RequestPageModelBase
    {
        public LogsheetImageRequestModel()
        {
        }
        /// <summary>
        /// Desc:表单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LogsheetId { get; set; }
        /// <summary>
        /// Desc:图片路径
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Image { get; set; }

    }
}