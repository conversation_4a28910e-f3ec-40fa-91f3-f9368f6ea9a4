using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.PPM
{
    /// <summary>
    /// 制造工单查询返回模型
    /// </summary>
    public class ProductionOrderViewModel:RequestPageModelBase
    {
        /// <summary>
        /// Desc:主键ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
		/// Desc:订单号
		/// Default:
		/// Nullable:True
		/// </summary>
		public string ProductionOrderNo { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVersionId { get; set; }
        /// <summary>
        /// Desc:物料版本
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVersionNumber { get; set; }
        /// <summary>
        /// Desc:配方code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MatCode { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MatName { get; set; }
        /// <summary>
        /// Desc:类型：Batch，WorkOrder，PlanOrder
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:父级ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQty { get; set; }
        /// <summary>
        /// Desc:设定速度
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Speed { get; set; }
        /// <summary>
        /// Desc:设定速度单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SpeedUom { get; set; }
        /// <summary>
        /// Desc:BOM版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BomVersion { get; set; }
        /// <summary>
        /// Desc:长文本版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string TextVersion { get; set; }

        /// <summary>
        /// Desc:生产日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? date { get; set; }
        /// <summary>
        /// Desc:计划开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanStartTime { get; set; }
        /// <summary>
        /// Desc:计划结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanEndTime { get; set; }
        /// <summary>
        /// Desc:订单状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string PoStatus { get; set; }
        /// <summary>
        /// Desc:下发状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReleaseStatus { get; set; }
        /// <summary>
        /// Desc:完结状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? FinalStatus { get; set; }
        /// <summary>
        /// Desc:容量
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Capicity { get; set; }
        /// <summary>
        /// Desc:期望效率
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ExpectedEfficiency { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SegmentCode { get; set; }

        /// <summary>
        /// Desc:灌装线
        /// </summary>
        public string FillLine { get; set; }

        /// <summary>
        /// Desc:灌装线ID
        /// </summary>
        public string FillLineId { get; set; }

        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:CIP方式
        /// Default:
        /// Nullable:True
        /// </summary>
        public string CipTypes {  get; set; }
        /// <summary>
        /// Desc:SAP工单类型  ZXH9 包装车间返工工单   ZXH1 包装   ZXH2 制造   ZXH4  开盖取料(制造车间返工工单)
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string SapOrderType { get; set; }
        /// <summary>
        /// Desc:MES工单号 'C'+4为年+2位月+2位日+3位顺序号
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string MesOrderCode { get; set; }
        /// <summary>
        /// Desc:配方号
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string SapFormula { get; set; }
        /// <summary>
        /// Desc:顺序号
        /// Default: 
        /// Nullable:True
        /// </summary>
        public long Sequence { get; set; }
        /// <summary>
        /// Desc:工单类型2 ，FILL 灌注工单， PACK包装工单
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string PDType { get; set; }

        /// <summary>
        /// Desc:是否有喉头  1 有， 0 没有
        /// Default: false 
        /// Nullable:True
        /// </summary>
        public int HasThroat { get; set; }

        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 已上传 3 同步状态 4 同步失败 
        /// Nullable:False
        /// </summary>
        public int SapFlag { get; set; }

        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 已上传 3 同步状态 4 同步失败 
        /// Nullable:False
        /// </summary>
        public List<int> SapFlagList { get; set; }
        /// <summary>
        /// DESC:订单类型
        /// DEFAULT:C    C 普通工单 ， S 试制工单， O 油渣工单， F 返工工单
        /// Nullable:true
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 生成工单数量
        /// </summary>
        public int OrderCount { get; set; }
        /// <summary>
        /// DESC:隔筛
        /// Nullable:true
        /// </summary>
        public string GeShai { get; set; }
        /// <summary>
        /// DESC:防腐剂
        /// Nullable:true
        /// </summary>
        public string FangFuJi { get; set; }

        /// <summary>
        /// Desc:CIP清单
        /// </summary>
        public List<ProductionOrderCipEntity> CipDetails { get; set; }
        /// <summary>
        /// Desc:喉头
        /// </summary>
        public string ThroatDetail { get; set; }
        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ProduceDate { get; set; }
        /// <summary>
        /// Desc:喉头
        /// </summary>
        public string LongTextResult { get; set; }
    }
    /// <summary>
    /// 包装工单视图模型
    /// </summary>
    public class PackOrderViewModel :RequestPageModelBase
    {
        /// <summary>
        /// Desc:主键ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:工单状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoStatus { get; set; }
        /// <summary>
        /// 包装工单号
        /// </summary>
        public string PackOrderNo { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        public string SaleContainer {  get; set; }
        /// <summary>
        /// 配方代号
        /// </summary>
        public string Formula {  get; set; }

        /// <summary>
        /// Desc:配方code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MatCode { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MatName { get; set; }

        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQty { get; set; }

        /// <summary>
        /// Desc:实际数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal ActualQty { get; set; }

        /// <summary>
        /// Desc:半成品配方code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ComMatCode { get; set; }
        /// <summary>
        /// Desc:半成品配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ComMatName { get; set; }

        /// <summary>
        /// Desc:半成品数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal ComPlanQty { get; set; }

        /// <summary>
        /// Desc:类型：Batch，WorkOrder，PlanOrder
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PDType { get; set; }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SegmentCode { get; set; }

        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineCode { get; set; }

        /// <summary>
        /// Desc:生产日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? ProDate { get; set; }
        /// <summary>
        /// Desc:计划开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanStartTime { get; set; }
        /// <summary>
        /// Desc:计划结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanEndTime { get; set; }
        /// <summary>
        /// Desc:MRP控制者
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MRP {  get; set; }
        /// <summary>
        /// Desc:批次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BatchNo {  get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitCode { get; set; }

        /// <summary>
        /// Desc:销售订单
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SalesOrder { get; set; }
        public long Sequence { get; set; }
        /// <summary>
        /// Desc:出柜日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public string IHRAN { get; set; }
        /// <summary>
        /// Desc:长文本1
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT1 { get; set; }
        /// <summary>
        /// Desc:长文本2
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT2 { get; set; }
        /// <summary>
        /// Desc:长文本3
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT3 { get; set; }
        /// <summary>
        /// Desc:长文本4
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT4 { get; set; }
        /// <summary>
        /// Desc:长文本5
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT5 { get; set; }
        /// <summary>
        /// Desc:长文本6
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LTEXT6 { get; set; }
        /// <summary>
        /// Desc:销售市场
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Slmkt { get; set; }

        /// <summary>
        /// Desc:纸箱及招纸
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MAKTX_C_FW { get; set; }
        /// <summary>
        /// Desc:生产单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:包装规格
        /// Default:
        /// Nullable:True
        /// </summary>
        public string VText { get; set; }
        /// <summary>
        /// Desc:SAP状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapStatus { get; set; }
        /// <summary>
        /// Desc:销售市场
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LANDZ {  get; set; }

        /// <summary>
        /// Desc:返工责任部门
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReWorkDRI { get; set; }

        /// <summary>
        /// Desc:返工类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReWorkType { get; set; }

        /// <summary>
        /// Desc:返工原因
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReWorkReason { get; set; }

        /// <summary>
        /// Desc:返工原因备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReWorkRemark { get; set; }

        /// <summary>
        /// Desc:返工方法
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReWorkMathord { get; set; }

        /// <summary>
        /// Desc:是否忽略  0 -不忽略， 1-忽略
        /// Default:0， 
        /// Nullable:False
        /// </summary>
        public int IsIgnore { get; set; }

        /// <summary>
        /// Desc:清缸备注
        /// Default:IsIgnore
        /// Nullable:True
        /// </summary>
        public string OrderJarClear { get; set; }

        /// <summary>
        /// 走柜期
        /// </summary>
        public DateTime? ETDAT { get; set; }
        /// <summary>
        /// Cust PO num
        /// </summary>
        public string BSTNK { get; set; }
        /// <summary>
        /// Sales org  
        /// </summary>
        public string VKORG { get; set; }
        /// <summary>
        /// Desc:优先排产要求
        /// Default:IsIgnore
        /// Nullable:True
        /// </summary>
        public string Fgpri { get; set; }
        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 已上传 3 同步状态 4 同步失败 
        /// Nullable:False
        public int SapFlag { get; set; }

        /// <summary>
        /// DESC:订单类型
        /// DEFAULT:C    C 普通工单 ， S 试制工单， O 油渣工单， F 返工工单
        /// Nullable:true
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// DESC:物料转换备注
        /// Nullable:true
        /// </summary>
        public string BomRemark { get; set; }
        /// <summary>
        /// DESC:生产备注
        /// Nullable:true
        /// </summary>
        public string Remark { get; set; }
        public string Zcylcd { get; set; }
        public string Codtx { get; set; }
        /// <summary>
        /// 变更履历
        /// </summary>
        public string ChangeRecord { get; set; }
        public decimal? Speed { get; set; }
        public decimal PreWeight { get; set; }
        public decimal Ntgew { get; set; }
        public decimal MngPu { get; set; }
        public string PackOrderId { get; set; }
        public decimal MngPuo { get; set; }
        public decimal PsmngComp { get; set; }
    }
    /// <summary>
    /// 忽略/取消忽略工单
    /// </summary>
    public class PackOrderIgnore
    {
        /// <summary>
        /// 工单号
        /// </summary>
        public List<string> packOrderNo { get; set; }
        /// <summary>
        /// 0 不忽略 ， 1 忽略 
        /// </summary>
        public int isIgnore { get; set; }

    }
    /// <summary>
    /// 节拍信息
    /// </summary>
    public class BeatInfo
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int dataIndex {  get; set; }
        /// <summary>
        /// 产线编码
        /// </summary>

        public string LineCode { get; set; }
        /// <summary>
        /// 消息
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string LongTextResult { get; set; }

    }
    /// <summary>
    /// 喉头配置
    /// </summary>
    public class ThroatConfig
    {
        /// <summary>
        /// 喉头名称
        /// </summary>
        public string Name { get;  set; }

        /// <summary>
        /// 喉头物料编码
        /// </summary>
        public string MatCode { get; set; }
        /// <summary>
        /// 喉头配方编码
        /// </summary>
        public string SapFormula { get;  set; }
        /// <summary>
        /// 喉头物料id
        /// </summary>
        public string MatId { get;  set; }
        /// <summary>
        /// 可添加物料id
        /// </summary>
        public string MatAddableId { get;  set; }
        /// <summary>
        /// 可添加物料名称
        /// </summary>
        public string MatAddableName { get;  set; }
        public decimal Rate { get;  set; }
    }


    /// <summary>
    /// 喉头库存类型
    /// </summary>
    public class ThroarInventory
    {
        /// <summary>
        /// 库存ID
        /// </summary>
        public string ID { get;  set; }
        /// <summary>
        /// 喉头名称
        /// </summary>
        public string Name { get;  set; }
        /// <summary>
        /// 喉头编码
        /// </summary>
        public string Code { get;  set; }
        /// <summary>
        /// 物料id
        /// </summary>
        public string MaterialId { get;  set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get;  set; }
        /// <summary>
        /// 储存设备id
        /// </summary>
        public string EquipmentId { get;  set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string LotId { get;  set; }
        /// <summary>
        /// 追溯码
        /// </summary>
        public string SSCC { get; set; }
        /// <summary>
        /// 设备编号
        /// </summary>
        public string EquipmentCode { get;  set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProDate { get; set; }
        /// <summary>
        /// 有效日期
        /// </summary>
        public DateTime ExpirationDate { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string ExternalStatus { get; set; }
        /// <summary>
        /// 标准添加率
        /// </summary>
        public decimal StandardRate { get; set;}
        /// <summary>
        /// 配方号
        /// </summary>
        public string SapFormula { get; set;}
    }
    /// <summary>
    /// 配方排序
    /// </summary>
    public class SortOrderModel
    {
        public string ID { get; set; }
        public string Formula { get; set; }
        public string ShortFormula { get; set; }
        public string MatId { get; set; }
        public string LindId { get; set; }

        public string FillLineId { get; set; }
        public DateTime ProductDate { get; set; }
        /// <summary>
        /// 有油
        /// </summary>
        public int Sort1 { get; set; }
        /// <summary>
        /// 高优先级
        /// </summary>
        public int Sort2 { get; set; }
        /// <summary>
        /// 与前一日连续
        /// </summary>
        public int Sort3 { get; set; }
        /// <summary>
        /// 与后一日连续
        /// </summary>
        public int Sort4 { get; set; }
        /// <summary>
        /// CIP时间
        /// </summary>
        public int Sort5 { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        public int Sort6 { get; set; }
        /// <summary>
        /// 磨滑产品，后生产颗粒产品
        /// </summary>
        public int Sort7 { get; set; }
        /// <summary>
        /// 无麦麸
        /// </summary>
        public int Sort8 { get; set; }
        /// <summary>
        /// 无防腐剂
        /// </summary>
        public int Sort9 { get; set; }
        /// <summary>
        /// 色素
        /// </summary>
        public int Sort10 { get; set; }
        /// <summary>
        /// 过敏原
        /// </summary>
        public int Sort11 { get; set; }
    }
    /// <summary>
    /// 排序辅助类 包装规格
    /// </summary>
    public class SortOrderContainer
    {
        public string Magrv { get; set; }
        public string Cid { get; set; }
        public string Fid { get; set; }
    }
    /// <summary>
    /// 包装工单查询条件
    /// </summary>
    public class SortPackOrder
    {
        /// <summary>
        /// 销售工单ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 包装工单号
        /// </summary>
        public string OrderNo { get; set; }

        public DateTime? ProduceDate {  get; set; }

        /// <summary>
        /// 产品物料编号
        /// </summary>
        public string MtrCode { get; set; }
        /// <summary>
        /// 包装类型
        /// </summary>
        public string SaleContainer { get; set; }
        /// <summary>
        /// 清缸工单  包含清缸
        /// </summary>
        public string ClearTxt { get; set; }
        /// <summary>
        /// 走柜日期
        /// </summary>
        public string CallDate { get; set; }
        /// <summary>
        /// MRP控制者
        /// </summary>
        public string Mrp { get; set; }
        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQty { get; set; }
        /// <summary>
        /// 煮制工单
        /// </summary>
        public string CookOrderId { get; set; }
        /// <summary>
        /// 送达方  KUNNR4
        /// </summary>
        public string ShipToParty {  get; set; }
        /// <summary>
        /// 优先排产表示
        /// </summary>
        public string Fgprie { get; set; }
        /// <summary>
        /// 煮制工单顺序
        /// </summary>
        public int Sort1 { get; set; }
        /// <summary>
        /// 规格排序
        /// </summary>
        public int Sort2 { get; set; }
        /// <summary>
        /// 优先排产
        /// </summary>
        public int Sort3 { get; set; }
        /// <summary>
        /// 非标产品
        /// </summary>
        public int Sort4 { get; set; }
        /// <summary>
        /// MRP控制者
        /// </summary>
        public int Sort5 { get; set; }
        /// <summary>
        /// 招纸相同
        /// </summary>
        public int Sort6 { get; set; }
        /// <summary>
        /// 喷码要求
        /// </summary>
        public int Sort7 { get; set; }

        /// <summary>
        /// 纸盘
        /// </summary>
        public int Sort8 { get; set; }
        /// <summary>
        /// 清缸工单
        /// </summary>
        public int Sort9 { get; set; }
        
    }
    /// <summary>
    /// 工单属性修改请求参数
    /// </summary>
    public class OrderChangeProperyModel
    {
        /// <summary>
        /// 工单ID
        /// </summary>
        public string OrderID { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 工单状态
        /// </summary>
        public string PoStatus { get; set; }
    }

    public class OrderRemarkChangeModel
    {
        /// <summary>
        /// 工单ID
        /// </summary>
        public List<string> OrderID { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        public string Remark { get; set; }
    }
    /// <summary>
    /// SAP 状态前缀
    /// </summary>
    public class SapStatusString
    {
        /// <summary>
        /// 删除标识
        /// </summary>
        public static string DLFL = "DLFL";
        /// <summary>
        /// 已确认
        /// </summary>
        public static string CNF = "CNF";
        /// <summary>
        /// 部分确认
        /// </summary>
        public static string PCNF = "PCNF";
        /// <summary>
        /// 已核发
        /// </summary>
        public static string REL = "REL";
        /// <summary>
        /// 技术完成
        /// </summary>
        public static string TECO = "TECO";
    }
}
