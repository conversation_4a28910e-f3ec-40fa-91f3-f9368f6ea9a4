using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class CooktimeModel : EntityBase
    {

        /// <summary>
        /// Desc:物料编码ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:酱料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:酱料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:配方号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:煮料重量（KG）
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal StandardWeight { get; set; }
        /// <summary>
        /// Desc:生产耗时（h）
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal StandardHours { get; set; }
        /// <summary>
        /// Desc:标准泵料时长（h）
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Duration { get; set; }
        /// <summary>
        /// Desc:缸容量
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? TankCapacity { get; set; }
    }
}