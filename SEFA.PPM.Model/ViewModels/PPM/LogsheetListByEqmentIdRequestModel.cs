using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class LogsheetRequestModel : RequestPageModelBase
    {
        public LogsheetRequestModel()
        {
        }
           /// <summary>
           /// Desc:批次ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:参数组ID（日志表名）
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ParameterGroupId { get; set; }
           /// <summary>
           /// Desc:执行ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoExecutionId { get; set; }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Status { get; set; }
           /// <summary>
           /// Desc:评论
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Comment { get; set; }
           /// <summary>
           /// Desc:班组ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ShiftId { get; set; }
           /// <summary>
           /// Desc:频率
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Frequency { get; set; }
           /// <summary>
           /// Desc:审批时间
           /// Default:
           /// Nullable:False
           /// </summary>
        public DateTime Approvedate { get; set; }
           /// <summary>
           /// Desc:审批人
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Approveuserid { get; set; }

    }
}