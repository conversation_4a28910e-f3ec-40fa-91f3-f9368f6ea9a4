using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class CipSwitchtypeModel : EntityBase
    {

        /// <summary>
        /// Desc:前配方ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PrematId { get; set; }
        /// <summary>
        /// Desc:前配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PrematName { get; set; }
        /// <summary>
        /// Desc:后配方ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PostmatId { get; set; }
        /// <summary>
        /// Desc:后配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PostmatName { get; set; }
        /// <summary>
        /// Desc:切换方式ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Switchid { get; set; }

        /// <summary>
        /// Desc:切换方式名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Switchname { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:前配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string PrematCode { get; set; }
        /// <summary>
        /// Desc:后配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string PostmatCode { get; set; }

        /// <summary>
        /// Desc:前配方物料编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string PreMaterialCode { get; set; }

        /// <summary>
        /// Desc:后配方物料编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string PostMaterialCode { get; set; }

    }
}