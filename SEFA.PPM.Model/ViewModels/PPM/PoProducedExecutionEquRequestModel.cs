using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoProducedExecutionEquRequestModel : RequestPageModelBase
    {
        public PoProducedExecutionEquRequestModel()
        {
        }
           /// <summary>
           /// Desc:生产执行ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ProducedExecutionId { get; set; }
           /// <summary>
           /// Desc:是否是瓶颈设备
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? IsBottleneck { get; set; }
           /// <summary>
           /// Desc:是否是瓶颈设备后的设备
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? IsAfterBottleneck { get; set; }
           /// <summary>
           /// Desc:瓶颈工站分摊比
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? SplitPercentage { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}