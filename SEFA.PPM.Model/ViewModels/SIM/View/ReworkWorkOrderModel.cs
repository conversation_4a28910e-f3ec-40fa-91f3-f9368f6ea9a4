using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.SIM.View
{
    public class ReworkWorkOrderModel
    {
        /// <summary>
        /// Desc:建单日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_CREATEDATE")]
        public string  ReworkorderCreatedate { get; set; }
        /// <summary>
        /// Desc:月
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MONTH")]
        public string  Month { get; set; }
        /// <summary>
        /// Desc:生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }

        /// <summary>
        /// Desc:订单号码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:产品编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:产品名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_DECS")]
        public string MaterialDecs { get; set; }
        /// <summary>
        /// Desc:批号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_BATCH_ID")]
        public string ReworkorderBatchId { get; set; }
        /// <summary>
        /// Desc:需要返工的数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_REWORKQTY")]
        public decimal ReworkorderReworkqty { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_UNIT")]
        public string ReworkorderUnit { get; set; }
        /// <summary>
        /// Desc:责任部门
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_AUTHORITY")]
        public string ReworkorderAuthority { get; set; }
        /// <summary>
        /// Desc:返工类别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_TYPE")]
        public string ReworkorderType { get; set; }
        /// <summary>
        /// Desc:返工原因
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_REASON")]
        public string ReworkorderReason { get; set; }
        /// <summary>
        /// Desc:返工方法
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_METHOD")]
        public string ReworkorderMethod { get; set; }
        /// <summary>
        /// Desc:返工后数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_QTY_BOTTLE")]
        public decimal? ActualQtyBottle { get; set; }
        /// <summary>
        /// Desc:人时
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LABOR_HOUR")]
        public decimal LaborHour { get; set; }
        /// <summary>
        /// Desc:工时类别1
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "HOUR_TYPE1")]
        public string HourType1 { get; set; }
        /// <summary>
        /// Desc:机时工时
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MACH_HOUR")]
        public decimal MachHour { get; set; }
        /// <summary>
        /// Desc:工时类别2
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "HOUR_TYPE2")]
        public string HourType2 { get; set; }
        /// <summary>
        /// Desc:成本中心
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "COST_CENTER")]
        public string CostCenter { get; set; }
        /// <summary>
        /// Desc:返工工单备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REWORKORDER_REMARK")]
        public string ReworkorderRemark { get; set; }
    }
}
