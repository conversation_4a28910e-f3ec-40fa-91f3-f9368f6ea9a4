using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.SIM.View
{
    public class LosstgtModel
    {
       /* /// <summary>
        /// Desc:损耗类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOSS_TYPE")]
        public string LossType { get; set; }*/
        /// <summary>
        /// Desc:原料所属群组/包材（物料）小分类/生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ITEM")]
        public string Item { get; set; }
        /// <summary>
        /// Desc:年
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "YEAR")]
        public int Year { get; set; }
        
        /// <summary>
        /// Desc:目标值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TGT")]
        public decimal Tgt { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string UnitName { get; set; }
    }

    public class LosstgtGroupModel
    {
       /* /// <summary>
        /// Desc:损耗类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOSS_TYPE")]
        public string LossType { get; set; }*/
        /// <summary>
        /// Desc:包材大分类
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ITEM_GROUP")]
        public string ItemGroup { get; set; }
        /// <summary>
        /// Desc:原料所属群组/包材（物料）小分类/生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ITEM")]
        public string Item { get; set; }
        /// <summary>
        /// Desc:年
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "YEAR")]
        public int Year { get; set; }

        /// <summary>
        /// Desc:目标值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TGT")]
        public decimal Tgt { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string UnitName { get; set; }
    }

    public class LosstgtLineModel
    {
        /// <summary>
        /// Desc:生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LineName")]
        public string LineName { get; set; }
        /// <summary>
        /// Desc:年
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "YEAR")]
        public int Year { get; set; }

        /// <summary>
        /// Desc:目标值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TGT")]
        public decimal Tgt { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string UnitName { get; set; }
    }
}
