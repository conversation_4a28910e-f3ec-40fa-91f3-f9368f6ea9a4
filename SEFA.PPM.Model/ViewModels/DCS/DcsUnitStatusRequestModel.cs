using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class DcsUnitStatusRequestModel : RequestPageModelBase
    {
        public DcsUnitStatusRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:False
           /// </summary>
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}