using SEFA.Base.Model;
using System;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.Model.ViewModels.WMS
{
    /// <summary>
    /// 叫料单请求模型
    /// </summary>
    public class CallMaterialSheetRequestModel : RequestPageModelBase
    {
        /// <summary>
        /// 叫料单ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 叫料单号
        /// </summary>
        public string CallOrderNo { get; set; }

        /// <summary>
        /// 工单ID
        /// </summary>
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 叫料人ID
        /// </summary>
        public string CallerId { get; set; }

        /// <summary>
        /// 叫料时间
        /// </summary>
        public DateTime CallTime { get; set; }

        /// <summary>
        /// 线边仓
        /// </summary>
        public string LineSideWarehouse { get; set; }

        /// <summary>
        /// 叫料点
        /// </summary>
        public string CallPoint { get; set; }

        /// <summary>
        /// 叫料状态
        /// </summary>
        public string CallStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 明细列表
        /// </summary>
        public List<CallMaterialDetailEntity> Details { get; set; }
    }
}