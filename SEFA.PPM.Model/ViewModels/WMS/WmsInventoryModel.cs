using SEFA.Base.Model;
using SEFA.Base.Model.BASE;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System;
using System.Linq;
using System.Text;

namespace SEFA.PPM.Model.ViewModels
{
    public class WmsInventoryModel
    {
        /// <summary>
        /// 仓库编号
        /// </summary>
        public string  WarehouseCode { get; set; }
        public string Plant { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string  MaterialName { get; set; }
        /// <summary>
        /// 物料版本号
        /// </summary>
        public string MaterialVersionCode { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string Supplier { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }
        /// <summary>
        /// IBC桶号
        /// </summary>
        public string IBCNo { get; set; }
        /// <summary>
        /// 物料唯一标签码
        /// </summary>
        public string BarCode { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        public string StorageTime { get; set; }
        /// <summary>
        /// 到期日期
        /// </summary>
        public string ExpireTime { get; set; }
        /// <summary>
        /// 母托盘号
        /// </summary>
        public string PalletNo { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 计量单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 密度
        /// </summary>
        public string Density { get; set; }
        /// <summary>
        /// Coa含量%
        /// </summary>
        public string CoAContent { get; set; }
        /// <summary>
        /// 备注说明
        /// </summary>
        public string Remark { get; set; }


    }
}