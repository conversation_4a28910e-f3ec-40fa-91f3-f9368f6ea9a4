using SEFA.Base.Model;
using System;

namespace SEFA.PPM.Model.ViewModels.WMS
{
    /// <summary>
    /// 叫料单分页查询模型
    /// </summary>
    public class CallMaterialHeaderRequestModel : RequestPageModelBase
    {
        /// <summary>
        /// 工单号
        /// </summary>
        public string WorkOrder { get; set; }

        /// <summary>
        /// 叫料单号
        /// </summary>
        public string CallOrderNo { get; set; }

        /// <summary>
        /// 叫料人
        /// </summary>
        public string CallerId { get; set; }

        /// <summary>
        /// 叫料点
        /// </summary>
        public string CallPoint { get; set; }

        /// <summary>
        /// 线边仓
        /// </summary>
        public string LineSideWarehouse { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 叫料状态
        /// </summary>
        public string CallMaterialStatus { get; set; }
    }
}