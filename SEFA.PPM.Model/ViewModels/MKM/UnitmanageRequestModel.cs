using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class UnitmanageRequestModel : RequestPageModelBase
    {
        public UnitmanageRequestModel()
        {
        }
           /// <summary>
           /// Desc:单位名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Name { get; set; }
           /// <summary>
           /// Desc:简称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Shortname { get; set; }
           /// <summary>
           /// Desc:类型编号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Typecode { get; set; }
           /// <summary>
           /// Desc:类型名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Typename { get; set; }
           /// <summary>
           /// Desc:标准点位
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Isstandard { get; set; }
           /// <summary>
           /// Desc:序号
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Sequence { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:是否启用（0-禁用   1-启用）
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Enable { get; set; }
           /// <summary>
           /// Desc:是否删除
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}