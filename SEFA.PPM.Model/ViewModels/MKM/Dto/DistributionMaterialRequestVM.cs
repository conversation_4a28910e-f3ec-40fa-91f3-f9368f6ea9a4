using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SEFA.PPM.Model.ViewModels.MKM.Dto
{
    /// <summary>
    /// 叫料申请视图模型
    /// </summary>
    public class DistributionMaterialRequestVM
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 叫料申请单号
        /// </summary>
        [Required]
        public string RequestSheetNo { get; set; }

        /// <summary>
        /// 送达线边库编号
        /// </summary>
        [Required]
        public string LineWarehouseCode { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 叫料申请明细列表
        /// </summary>
        public ICollection<DistributionMaterialDetailVM> Details { get; set; } = new List<DistributionMaterialDetailVM>();
    }

    /// <summary>
    /// 叫料申请明细视图模型
    /// </summary>
    public class DistributionMaterialDetailVM
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Required]
        public string Plant { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Required]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Required]
        public string MaterialName { get; set; }

        /// <summary>
        /// 物料版本号
        /// </summary>
        public string MaterialVersionCode { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }

        /// <summary>
        /// 托盘号
        /// </summary>
        public string PalletNo { get; set; }

        /// <summary>
        /// 物料唯一标签码
        /// </summary>
        public string BarCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 密度
        /// </summary>
        public decimal Density { get; set; }

        /// <summary>
        /// Coa含量%
        /// </summary>
        public decimal CoAContent { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        public string Remark { get; set; }
    }
}