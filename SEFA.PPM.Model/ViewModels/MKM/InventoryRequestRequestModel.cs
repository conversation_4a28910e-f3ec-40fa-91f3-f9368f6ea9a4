using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class InventoryRequestRequestModel : RequestPageModelBase
    {
        public InventoryRequestRequestModel()
        {
        }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:单位ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string UomId { get; set; }
           /// <summary>
           /// Desc:存储位置ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ExternalStatus { get; set; }
           /// <summary>
           /// Desc:方式
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }

    }
}