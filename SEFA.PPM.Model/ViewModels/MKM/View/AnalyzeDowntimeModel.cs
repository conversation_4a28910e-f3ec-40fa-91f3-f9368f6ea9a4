using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class AnalyzeDowntimeModel
    {
        public List<ReasonModels> reasonModels { get; set; }
        public decimal? TotalTime1 { get; set; }
        public decimal? TotalTime2 { get; set; }
    }
    public class ReasonModel
    {
        public int id { get; set; }
        public string GroupName { get; set; }
        public decimal? Time1 { get; set; }
        public decimal? Time2 { get; set; }
    }

    public class ReasonList
    {
        public int id { get; set; }
        public string GroupName { get; set; }
        public decimal? Time1 { get; set; }
        public decimal? Time2 { get; set; }
    }
    public class ReasonModels
    {
        public int id { get; set; }
        public string GroupName { get; set; }
        public decimal? Time1 { get; set; }
        public decimal? Time2 { get; set; }
        public List<ReasonList> ReasonLists { get; set; }
    }
}
