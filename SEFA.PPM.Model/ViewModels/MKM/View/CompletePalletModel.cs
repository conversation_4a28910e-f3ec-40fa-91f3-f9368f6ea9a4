using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    //string containerID, int actualWeight, string uomId
    public class CompletePalletModel
    {
        /// <summary>
        /// Desc:
        /// Default:选中的容器ID
        /// Nullable:True
        /// </summary>
        public string containerID { get; set; }

        /// <summary>
        /// Desc:
        /// Default:下拉容器集合
        /// Nullable:True
        /// </summary>
        public string[] containerIDs { get; set; }
        /// <summary>
        /// Desc:
        /// Default:上面的实际数量
        /// Nullable:True
        /// </summary>
        public string TagWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:单位ID
        /// Nullable:True
        /// </summary>
        public string uomId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:物料ID
        /// Nullable:True
        /// </summary>
        public string MaterialId { get; set; }

        public string ProID { get; set; }
        public string BatchID { get; set; }
    }
}
