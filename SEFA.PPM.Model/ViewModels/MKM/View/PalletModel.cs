using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels.View;

public class PalletModel:RequestPageModelBase
{
    public string Destination { get; set; }
    public string Area { get; set; }

    public string VerifiedStatus { get; set; }

    public DateTime? StarTime{ get; set; }

    public DateTime? EndTime { get; set; }
  
}