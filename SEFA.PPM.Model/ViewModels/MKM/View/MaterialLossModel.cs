using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class MaterialLossModel
    {
         /// <summary>
         /// 原料所属群组
         /// </summary>
         public string RowmaterialGroup { get; set; }
         /// <summary>
         /// 年目标
         /// </summary>
         public decimal AnnualTgt { get; set; }
         /// <summary>
         /// 1月
         /// </summary>
         public decimal s1 { get; set; }
         /// <summary>
         /// 损耗
         /// </summary>
         public decimal m1 { get; set; }
         /// <summary>
         /// 损耗率
         /// </summary>
         public decimal l1 { get; set; }
        /// <summary>
        /// 2月
        /// </summary>
        public decimal s2 { get; set; }
        /// <summary>
        /// 损耗2
        /// </summary>
        public decimal m2 { get; set; }
        /// <summary>
        /// 损耗率2
        /// </summary>
        public decimal l2 { get; set; }
        /// <summary>
        /// 3月
        /// </summary>
        public decimal s3 { get; set; }
        /// <summary>
        /// 损耗3
        /// </summary>
        public decimal m3 { get; set; }
        /// <summary>
        /// 损耗率3
        /// </summary>
        public decimal l3 { get; set; }
        /// <summary>
        /// 4月
        /// </summary>
        public decimal s4 { get; set; }
        /// <summary>
        /// 损耗4
        /// </summary>
        public decimal m4 { get; set; }
        /// <summary>
        /// 损耗率4
        /// </summary>
        public decimal l4 { get; set; }
        /// <summary>
        /// 5月
        /// </summary>
        public decimal s5 { get; set; }
        /// <summary>
        /// 损耗5
        /// </summary>
        public decimal m5 { get; set; }
        /// <summary>
        /// 损耗率5
        /// </summary>
        public decimal l5 { get; set; }
        /// <summary>
        /// 6月
        /// </summary>
        public decimal s6 { get; set; }
        /// <summary>
        /// 损耗6
        /// </summary>
        public decimal m6 { get; set; }
        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal l6 { get; set; }
        /// <summary>
        /// 7月
        /// </summary>
        public decimal s7 { get; set; }
        /// <summary>
        /// 损耗
        /// </summary>
        public decimal m7 { get; set; }
        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal l7 { get; set; }
        /// <summary>
        /// 8月
        /// </summary>
        public decimal s8 { get; set; }
        /// <summary>
        /// 损耗
        /// </summary>
        public decimal m8 { get; set; }
        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal l8 { get; set; }
        /// <summary>
        /// 9月
        /// </summary>
        public decimal s9 { get; set; }
        /// <summary>
        /// 损耗
        /// </summary>
        public decimal m9 { get; set; }
        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal l9 { get; set; }
        /// <summary>
        /// 10月
        /// </summary>
        public decimal s10 { get; set; }
        /// <summary>
        /// 损耗
        /// </summary>
        public decimal m10 { get; set; }
        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal l10 { get; set; }
        /// <summary>
        /// 11月
        /// </summary>
        public decimal s11 { get; set; }
        /// <summary>
        /// 损耗11
        /// </summary>
        public decimal m11 { get; set; }
        /// <summary>
        /// 损耗率11
        /// </summary>
        public decimal l11 { get; set; }
        /// <summary>
        /// 12月
        /// </summary>
        public decimal s12 { get; set; }
        /// <summary>
        /// 损耗12
        /// </summary>
        public decimal m12 { get; set; }
        /// <summary>
        /// 损耗率12
        /// </summary>
        public decimal l12 { get; set; }
        /// <summary>
        /// 年累计
        /// </summary>
        public decimal s13 { get; set; }
        /// <summary>
        /// 损耗13
        /// </summary>
        public decimal m13 { get; set; }
        /// <summary>
        /// 损耗率13
        /// </summary>
        public decimal l13 { get; set; }
    }
}
