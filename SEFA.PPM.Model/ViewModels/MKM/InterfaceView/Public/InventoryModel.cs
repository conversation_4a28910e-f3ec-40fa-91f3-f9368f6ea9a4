using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using Magicodes.ExporterAndImporter.Core;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///物料库存管理
    ///</summary>

   
    public class InVentPDAModel 
    {
        public InVentPDAModel()
        {
        }


        public string SSCC { get; set; }

        public string MaterialId { get; set; }

        public string SSCCID { get; set; }

        public string Bag { get; set; }
        public string Size { get; set; }
      
        public string MUnit { get; set; }

        public decimal TargetWeight { get; set; }

       
    }
}