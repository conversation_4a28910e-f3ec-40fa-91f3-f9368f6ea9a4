using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class EquipmentMaterialRequestModel : RequestPageModelBase
    {
        public EquipmentMaterialRequestModel()
        {
        }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料组ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string GroupId { get; set; }
           /// <summary>
           /// Desc:类型（是否包含）
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:状态（是否启用）
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Status { get; set; }

    }
}