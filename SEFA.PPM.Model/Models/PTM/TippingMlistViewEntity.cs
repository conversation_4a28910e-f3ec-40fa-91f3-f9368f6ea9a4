using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///工单执行-投料功能-物料清单
	///</summary>

	[SugarTable("V_PTM_TIPPING_MLIST_VIEW")]
	public class TippingMlistViewEntity : EntityBase
	{
		public TippingMlistViewEntity()
		{
		}
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SORT_ORDER")]
		public int? SortOrder { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_ID")]
		public string MaterialId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_CODE")]
		public string MaterialCode { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_NAME")]
		public string MaterialName { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "QUANTITY")]
		public decimal Quantity { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "QUANTITY2")]
		public decimal Quantity2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "QUANTITY4")]
		public decimal Quantity4 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNIT_ID")]
		public string UnitId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNIT1")]
		public string Unit1 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "BATCH_ID")]
		public string BatchId { get; set; }
		/// <summary>
		/// Desc:投料状态（0 未投料，1，已投料 2，投料中）
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "FEED_STATES")]
		public int FeedStates { get; set; }
		/// <summary>
		/// Desc:批次投料状态
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PREP_STATUS")]
		public string PrepStatus { get; set; }

	}

	public class DockScanModel
	{
		public string TraceCode { get; set; }

		public string EquipmentActionId { get; set; }

		public string DestinationId { get; set; }

		public string DestinationName { get; set; }
	}


	public class TippingMlistViewModel : TippingMlistViewEntity
	{
		/// <summary>
		/// 是否PDA
		/// </summary>
		public bool IsPDA { get; set; }
		/// <summary>
		/// 是否强制完成
		/// </summary>
		public bool IsForcedCompletion { get; set; }

		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		public string PoExecutionId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		public string RunEquipmentId { get; set; }
	}
}