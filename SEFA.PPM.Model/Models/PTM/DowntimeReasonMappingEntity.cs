using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.Collections.Generic;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PTM_M_DOWNTIME_REASON_MAPPING")]
	public class DowntimeReasonMappingEntity : EntityBase
	{
		public DowntimeReasonMappingEntity()
		{
		}
		/// <summary>
		/// Desc:产线ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LINE_ID")]
		public string LineId { get; set; }
		/// <summary>
		/// Desc:设备ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MACHINE_ID")]
		public string MachineId { get; set; }
		/// <summary>
		/// Desc:原因ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "REASON_ID")]
		public string ReasonId { get; set; }
		/// <summary>
		/// Desc:是否默认
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "IS_DEFAULT")]
		public string IsDefault { get; set; }
		/// <summary>
		/// Desc:排序
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SORT_ORDER")]
		public string SortOrder { get; set; }
		/// <summary>
		/// Desc:类型
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public string Type { get; set; }

	}


}