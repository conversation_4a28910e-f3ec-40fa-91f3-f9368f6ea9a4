using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_PTM_M_REASON_EQ_VIEW")] 
    public class MReasonEqViewEntity : EntityBase
    {
        public MReasonEqViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="REASON_ID")]
        public string ReasonId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLC_CODE")]
        public string PlcCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EXTERNAL_MACHINE_ID")]
        public string ExternalMachineId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ROUTE_ROW_ID")]
        public string EquipmentRouteRowId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLC_CODE_DESCRIPTION")]
        public string PlcCodeDescription { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_PROTECTED")]
        public string IsProtected { get; set; }
		/// <summary>
		/// Desc:是否自动创建维修工单
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "IS_AUTO_CREATE_REPAIR_ORDER")]
		public string IsAutoCreateRepairOrder { get; set; }
        /// <summary>
		/// Desc:是否Andon报警
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "IS_ANDON_ARALM")]
        public string IsAndonAralm { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="REASON_DESCRIPTION")]
        public string ReasonDescription { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="GROUP_DESCRIPTION")]
        public string GroupDescription { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CATEGORY_DESCRIPTION")]
        public string CategoryDescription { get; set; }
    }
}