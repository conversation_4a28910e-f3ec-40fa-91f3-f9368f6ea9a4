using InfluxDB.Client.Core;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
using SEFA.Base.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.InfluxDB
{
	public enum SortBy
	{
		ASC, DESC
	}


	public class Count
	{
		public int Value { get; set; }
	}

	public class InfluxData
	{
		public DateTime time { get; set; }
		public object value { get; set; }
	}

	public class InfluxDBRequestModel : RequestPageModelBase
	{
		public string tagId { get; set; }

		public DateTime start { get; set; }

		public DateTime end { get; set; }

		public string measurement { get; set; }

		public string bucket { get; set; }

		public string org { get; set; }
	}


	/// <summary>
	/// 数据定义
	/// </summary>
	[Measurement("TagValue")]
	public class TagValue
	{
		//[@Column("tagId", IsTag = true)] public string TagId { get; set; }
		[@Column("tag", IsTag = true)] public string Tag { get; set; }
		[@Column("value")] public object Value { get; set; }
		[@Column(IsTimestamp = true)] public DateTime Time { get; set; }
	}
}
