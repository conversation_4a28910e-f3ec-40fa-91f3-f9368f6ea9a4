using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_MKM_PACK_FORMULA_VIEW")] 
    public class PackFormulaViewEntity : EntityBase
    {
        public PackFormulaViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="YEAR")]
        public int? Year { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MONTH")]
        public int? Month { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FORMULA_TYPE")]
        public string FormulaType { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TGT")]
        public decimal? Tgt { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_QTY_TON")]
        public decimal? ActualQtyTon { get; set; }
    }
}