using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_PPM_B_BATCH_DETAIL_VIEW")] 
    public class BBatchDetailViewEntity : EntityBase
    {
        public BBatchDetailViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_NUMBER")]
        public string BatchNumber { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY_TOTAL")]
        public decimal QuantityTotal { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_Q")]
        public string UnitQ { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TARGET_QUANTITY")]
        public decimal TargetQuantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_T")]
        public string UnitT { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_NAME")]
        public string ItemName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TIPPING_DONE")]
        public string TippingDone { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }

    }
}