using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// 发起QA_Proleit
	/// </summary>
	public class MMI_InitiateQA
	{
		/// <summary>
		///工单编号
		/// </summary>
		public string ProductionNo { get; set; }
		/// <summary>
		///产品ID
		/// </summary>
		public string ProductId { get; set; }
		/// <summary>
		///产品名称
		/// </summary>
		public string ProductName { get; set; }
		/// <summary>
		///产品版本信息
		/// </summary>
		public string ProductVersion { get; set; }
		/// <summary>
		///执行设备ID
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		///批次
		/// </summary>
		public string BatchIndex { get; set; }

	}
}
