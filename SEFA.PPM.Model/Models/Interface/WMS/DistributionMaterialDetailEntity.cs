using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.Interface.WMS
{
    /// <summary>
    /// WMS发料申请明细表实体
    /// </summary>
    [SugarTable("MKM_B_DISTRIBUTION_MATERIAL_DETAIL")]
    public class DistributionMaterialDetailEntity : EntityBase
    {

        /// <summary>
        /// 申请主表ID
        /// </summary>
        [SugarColumn(ColumnName = "REQUESTID")]
        [Required]
        [MaxLength(50)]
        public string RequestId { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [SugarColumn(ColumnName = "PLANT")]
        [Required]
        [MaxLength(50)]
        public string Plant { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [SugarColumn(ColumnName = "MATERIALCODE")]
        [Required]
        [MaxLength(50)]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [SugarColumn(ColumnName = "MATERIALNAME")]
        [Required]
        [MaxLength(200)]
        public string MaterialName { get; set; }

        /// <summary>
        /// 物料版本号
        /// </summary>
        [SugarColumn(ColumnName = "MATERIALVERSIONCODE")]
        [MaxLength(50)]
        public string MaterialVersionCode { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnName = "BATCHNO")]
        [MaxLength(50)]
        public string BatchNo { get; set; }

        /// <summary>
        /// 托盘号
        /// </summary>
        [SugarColumn(ColumnName = "PALLETNO")]
        [MaxLength(50)]
        public string PalletNo { get; set; }

        /// <summary>
        /// 物料唯一标签码
        /// </summary>
        [SugarColumn(ColumnName = "BARCODE")]
        [MaxLength(100)]
        public string BarCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY", ColumnDataType = "decimal(18,6)")]
        [Required]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        [MaxLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 密度
        /// </summary>
        [SugarColumn(ColumnName = "DENSITY", ColumnDataType = "decimal(18,6)")]
        public decimal? Density { get; set; }

        /// <summary>
        /// Coa含量%
        /// </summary>
        [SugarColumn(ColumnName = "COACONTENT", ColumnDataType = "decimal(18,6)")]
        public decimal? CoAContent { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        [MaxLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 逻辑删除标记，0：未删除，1：已删除
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;

        /// <summary>
        /// 叫料申请主表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public virtual DistributionMaterialRequestEntity RequestEntity { get; set; }
    }
}