using System;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.Interface.WMS
{
    /// <summary>
    /// 叫料单主表实体
    /// </summary>
    [SugarTable("MKM_B_CALL_MATERIAL_SHEET")]
    public class CallMaterialSheetEntity : EntityBase
    {
        
        /// <summary>
        /// 工单号
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER")]
        public string ProductionOrder { get; set; }
        
        /// <summary>
        /// 叫料单号
        /// </summary>
        [SugarColumn(ColumnName = "CALL_ORDER_NO")]
        public string CallOrderNo { get; set; }

        /// <summary>
        /// 工单ID
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 叫料人ID
        /// </summary>
        [SugarColumn(ColumnName = "CALLER_ID")]
        public string CallerId { get; set; }

        /// <summary>
        /// 叫料时间
        /// </summary>
        [SugarColumn(ColumnName = "CALL_TIME")]
        public DateTime CallTime { get; set; }

        /// <summary>
        /// 线边仓
        /// </summary>
        [SugarColumn(ColumnName = "LINE_SIDE_WAREHOUSE")]
        public string LineSideWarehouse { get; set; }

        /// <summary>
        /// 叫料点
        /// </summary>
        [SugarColumn(ColumnName = "CALL_POINT")]
        public string CallPoint { get; set; }

        /// <summary>
        /// 叫料状态 (0-未叫料 1-已叫料 2-已完成)
        /// </summary>
        [SugarColumn(ColumnName = "CALL_STATUS")]
        public string CallStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

        /// <summary>
        /// 逻辑删除标记(0:未删除 1:已删除)
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}