using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.Interface.WMS
{
    /// <summary>
    /// WMS发料申请主表实体
    /// </summary>
    [SugarTable("MKM_B_DISTRIBUTION_MATERIAL_REQUEST")]
    public class DistributionMaterialRequestEntity : EntityBase
    {

        /// <summary>
        /// 叫料申请单号
        /// </summary>
        [SugarColumn(ColumnName = "REQUESTSHEETNO")]
        [Required]
        [MaxLength(50)]
        public string RequestSheetNo { get; set; }

        /// <summary>
        /// 送达线边库编号
        /// </summary>
        [SugarColumn(ColumnName = "LINEWAREHOUSECODE")]
        [Required]
        [MaxLength(50)]
        public string LineWarehouseCode { get; set; }


        /// <summary>
        /// 逻辑删除标记，0：未删除，1：已删除
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;

        /// <summary>
        /// 叫料申请明细列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public virtual ICollection<DistributionMaterialDetailEntity> Details { get; set; } = new List<DistributionMaterialDetailEntity>();
    }
}