using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.Interface.WMS
{
    /// <summary>
    /// 叫料单明细表实体
    /// </summary>
    [SugarTable("MKM_B_CALL_MATERIAL_DETAIL")]
    public class CallMaterialDetailEntity : EntityBase
    {
        /// <summary>
        /// 叫料单ID（主表ID）
        /// </summary>
        [SugarColumn(ColumnName = "CALL_SHEET_ID")]
        public string CallSheetId { get; set; }

        /// <summary>
        /// 叫料明细号
        /// </summary>
        [SugarColumn(ColumnName = "CALL_DETAIL_NO")]
        public string CallDetailNo { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [SugarColumn(ColumnName = "SPECIFICATION")]
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }

        /// <summary>
        /// 需求数量
        /// </summary>
        [SugarColumn(ColumnName = "REQUIRED_QUANTITY")]
        public decimal RequiredQuantity { get; set; }

        /// <summary>
        /// 实际叫料数量
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_QUANTITY")]
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

        /// <summary>
        /// 逻辑删除标记(0:未删除 1:已删除)
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}