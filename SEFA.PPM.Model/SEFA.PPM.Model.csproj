<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>..\SEFA.PPM.Api\SEFA.PPM.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>..\SEFA.PPM\SEFA.PPM.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Models\SegmentPropertyVEntity.cs" />
	  <Compile Remove="ViewModels\DFM\MaterialVersionRequestModel.cs" />
	  <Compile Remove="ViewModels\DFM\SapBomPhaseInjectionModel.cs" />
	  <Compile Remove="ViewModels\DFM\SapSegmentMaterialStepModel.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Abp" Version="7.3.0" />
		<PackageReference Include="InfluxDB.Client" Version="4.0.0" />
		<PackageReference Include="Magicodes.IE.Excel" Version="2.6.4" />
		<PackageReference Include="Magicodes.IE.Excel.AspNetCore" Version="2.6.4" />
		<PackageReference Include="MiniExcel" Version="1.33.0" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.120" />
		<PackageReference Include="sqlSugarCore" Version="5.1.4.151" />
		<PackageReference Include="AutoMapper" Version="10.1.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.0" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="MiniExcel">
	    <HintPath>C:\Users\<USER>\.nuget\packages\miniexcel\1.33.0\lib\net6.0\MiniExcel.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Model">
	    <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Model">
	    <HintPath>..\..\common\SEFA.DFM.Model.dll</HintPath>
	  </Reference>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\Report\" />
	  <Folder Include="ViewModels\DFM\" />
	  <Folder Include="ViewModels\Report\" />
	</ItemGroup>

</Project>
